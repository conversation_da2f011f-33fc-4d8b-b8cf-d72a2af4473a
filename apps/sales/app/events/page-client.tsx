"use client";

import { useState, useMemo, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { BigCalendar } from "shared-big-calendar";
import type { Mode } from "shared-big-calendar/src/calendar-types";
import "shared-big-calendar/calendar-styles.css";

interface EventsPageClientProps {
  initialEvents: any[];
}

export default function EventsPageClient({
  initialEvents,
}: EventsPageClientProps) {
  const router = useRouter();
  const calendarRef = useRef<HTMLDivElement>(null);

  // Big Calendar state
  const [calendarMode, setCalendarMode] = useState<Mode>("day");
  const [calendarDate, setCalendarDate] = useState(new Date());

  // Transform events for BigCalendar
  const transformedEvents = useMemo(() => {
    return initialEvents?.map((event) => ({
      id: event.id,
      title: event.title,
      color: "blue", // You can add logic to determine color based on event type
      start: new Date(event.startDate),
      end: new Date(event.endDate),
    }));
  }, [initialEvents]);

  // Handle event clicks to navigate to view page
  useEffect(() => {
    const handleEventClick = (event: Event) => {
      const target = event.target as HTMLElement;

      // Find the closest calendar event element
      const eventElement =
        target.closest("[data-event-id]") ||
        target.closest('.cursor-pointer[style*="bg-"]') ||
        target.closest(".cursor-pointer");

      if (eventElement) {
        // Try to get event ID from data attribute first
        const eventId = eventElement.getAttribute("data-event-id");

        if (eventId) {
          event.preventDefault();
          event.stopPropagation();

          // Find the event to get its title
          const matchingEvent = transformedEvents.find(e => e.id === eventId);
          const eventTitle = matchingEvent?.title || "Unnamed Event";

          // Create URL with title parameter
          const url = `/events/${eventId}/view?title=${encodeURIComponent(eventTitle)}`;
          router.push(url);
          return;
        }

        // Fallback: try to find event by title text
        const titleElement = eventElement.querySelector("p") || eventElement;
        const eventTitle = titleElement.textContent?.trim();

        if (eventTitle) {
          const matchingEvent = transformedEvents.find(
            (e) => e.title === eventTitle,
          );
          if (matchingEvent) {
            event.preventDefault();
            event.stopPropagation();

            // Create URL with title parameter
            const url = `/events/${matchingEvent.id}/view?title=${encodeURIComponent(matchingEvent.title)}`;
            router.push(url);
          }
        }
      }
    };

    const calendarElement = calendarRef.current;
    if (calendarElement) {
      // Add event listener with capture to intercept before BigCalendar handles it
      calendarElement.addEventListener("click", handleEventClick, true);

      return () => {
        calendarElement.removeEventListener("click", handleEventClick, true);
      };
    }
  }, [transformedEvents, router]);

  // Handle add new event
  const handleAddNewEvent = () => {
    router.push("/events/new");
  };

  return (
    <div className="space-y-2">
      <div ref={calendarRef}>
        <BigCalendar
          data={transformedEvents}
          view={calendarMode}
          setView={setCalendarMode}
          date={calendarDate}
          setDate={setCalendarDate}
          handleAddEvent={handleAddNewEvent}
        />
      </div>
    </div>
  );
}
