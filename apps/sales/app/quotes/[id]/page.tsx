import { QuoteCreateForm } from "./page-client";
import {
  getOrganizationDetails,
  getQuoteById,
  getOpportunityProducts,
  getOpportunityPackages,
} from "./actions";
import { getOpportunityById } from "../../opportunities/[id]/view/actions";
import { validateAction } from "@flinkk/shared-rbac";

export async function generateMetadata(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  return {
    title: params.id === "new" ? "Create Quote" : "Edit Quote",
  };
}

export default async function CreateQuotePage(props: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const params = await props.params;
  const searchParams = await props.searchParams;

  // Extract opportunity ID and mode from search params
  const opportunityId =
    typeof searchParams.opportunityId === "string"
      ? searchParams.opportunityId
      : undefined;

  const mode =
    typeof searchParams.mode === "string"
      ? searchParams.mode
      : "edit"; // Default to edit mode

  // Validate permissions based on the operation and mode
  if (params.id === "new") {
    // Validate create permission for new quotes
    await validateAction(
      "Quotations",
      "create",
      "You do not have permission to create quotes.",
    );
  } else {
    // Validate appropriate permission based on mode
    if (mode === "view") {
      await validateAction(
        "Quotations",
        "read",
        "You do not have permission to view quote details.",
      );
    } else {
      await validateAction(
        "Quotations",
        "update",
        "You do not have permission to edit quote details.",
      );
    }
  }

  // Fetch organization details using server action
  const organizationDetails = await getOrganizationDetails();

  // Fetch opportunity data, products, and packages if opportunityId is provided
  let opportunityData = null;
  let opportunityProducts: any[] = [];
  let opportunityPackages: any[] = [];
  if (opportunityId) {
    try {
      opportunityData = await getOpportunityById(opportunityId);
      // Fetch opportunity products for pre-populating line items
      opportunityProducts = await getOpportunityProducts(opportunityId);
      // Fetch opportunity packages for pre-populating line items
      opportunityPackages = await getOpportunityPackages(opportunityId);
    } catch (error) {
      console.error("Error fetching opportunity:", error);
      // Continue without opportunity data
    }
  }

  // Fetch existing quote data if in edit mode
  let initialData = null;
  if (params.id !== "new") {
    try {
      initialData = await getQuoteById(params.id);
    } catch (error) {
      console.error("Error fetching quote:", error);
      // Handle error - could redirect to 404 or show error message
    }
  }

  return (
    <QuoteCreateForm
      id={params.id}
      initialData={initialData}
      opportunityId={opportunityId}
      opportunityData={opportunityData}
      opportunityProducts={opportunityProducts}
      opportunityPackages={opportunityPackages}
      organizationDetails={organizationDetails}
      mode={mode}
    />
  );
}
