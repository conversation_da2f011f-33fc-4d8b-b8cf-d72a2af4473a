"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { Button } from "@flinkk/components/ui/button";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@flinkk/components/ui/form";
import { Loader2Icon } from "lucide-react";
import { EnhancedQuoteGeneratorButton } from "@flinkk/quote-pdf";
import { useQuoteTemplate } from "@flinkk/shared-hooks/use-quote-template";
import { useQuery } from "@tanstack/react-query";

// Country configuration hook
function useCurrentCountryConfiguration() {
  return useQuery({
    queryKey: ["current-country-configuration"],
    queryFn: async () => {
      const response = await fetch("/api/country-configurations/current");
      if (!response.ok) {
        throw new Error("Failed to fetch country configuration");
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Organization data hook
function useOrganizationData() {
  return useQuery({
    queryKey: ["organization-data"],
    queryFn: async () => {
      const response = await fetch("/api/quote-templates");
      if (!response.ok) {
        throw new Error("Failed to fetch organization data");
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Currency formatter hook
function useCurrencyFormatter() {
  const { data: countryConfig, isLoading } = useCurrentCountryConfiguration();

  const formatCurrency = (amount: number) => {
    if (!countryConfig?.configuration) {
      // Fallback to USD formatting
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amount);
    }

    const config = countryConfig.configuration;
    const symbol = config.currencySymbol;
    const formatted = amount.toFixed(config.decimalPlaces);

    // Simple formatting based on position
    switch (config.currencyPosition) {
      case "BEFORE":
        return `${symbol}${formatted}`;
      case "AFTER":
        return `${formatted}${symbol}`;
      case "BEFORE_WITH_SPACE":
        return `${symbol} ${formatted}`;
      case "AFTER_WITH_SPACE":
        return `${formatted} ${symbol}`;
      default:
        return `${symbol}${formatted}`;
    }
  };

  return {
    formatCurrency,
    isLoading,
    countryConfig: countryConfig?.configuration,
  };
}

// Import our dynamic form elements
import {
  TextFieldFormElement,
  TextAreaFieldFormElement,
  NumberFieldFormElement,
  DynamicSelectFieldFormElement,
} from "@flinkk/dynamic-form/form-elements";

// Import UI components for custom controls
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";

// Import section wrapper
import { SectionWrapper } from "@flinkk/dynamic-form/components";

// Import the enhanced line items table component
import { EnhancedLineItemsTable } from "../../packages/_components/enhanced-line-items-table";
// Import the package line items table component
import { PackageLineItemsTable } from "../_components/package-line-items-table";

// Quote form schema
const quoteFormSchema = z.object({
  name: z.string().min(1, "Quote name is required"),
  description: z.string().default(""),
  status: z.string().default("DRAFT"),

  // Customer/Company selection
  contactId: z.string().default(""),
  accountId: z.string().default(""),
  opportunityId: z.string().default(""),

  // Dates
  validUntil: z.string().default(""),

  // Terms
  paymentTerms: z.string().default("Net 30"),
  deliveryTerms: z.string().default(""),

  // Quotation From fields
  quotationFromCountry: z.string().min(1, "Country is required"),
  quotationFromBusinessName: z.string().min(1, "Business name is required"),
  quotationFromGSTIN: z.string().default(""),
  quotationFromAddress: z.string().default(""),
  quotationFromCity: z.string().default(""),
  quotationFromPostalCode: z.string().default(""),
  quotationFromState: z.string().default(""),

  // Quotation To fields (existing)
  quotationToCountry: z.string().default(""),
  quotationToBusinessName: z.string().default(""),
  quotationToGSTIN: z.string().default(""),
  quotationToAddress: z.string().default(""),
  quotationToCity: z.string().default(""),
  quotationToPostalCode: z.string().default(""),
  quotationToState: z.string().default(""),

  // Pricing (will be calculated from line items)
  subtotal: z.number().default(0),
  totalTax: z.number().default(0),
  totalDiscount: z.number().default(0),
  grandTotal: z.number().default(0),

  // Enhanced pricing fields
  quoteDiscountType: z.string().default("PERCENTAGE"),
  quoteDiscountValue: z.coerce
    .number()
    .min(0, "Discount value must be positive")
    .default(0),
  intraStateTaxRate: z.coerce
    .number()
    .min(0, "Tax rate must be positive")
    .max(100, "Tax rate cannot exceed 100%")
    .default(0),
  interStateTaxRate: z.coerce
    .number()
    .min(0, "Tax rate must be positive")
    .max(100, "Tax rate cannot exceed 100%")
    .default(0),
  adjustments: z.coerce.number().default(0),
  isManualTotal: z.boolean().default(false),
  manualTotalValue: z.coerce.number().optional(),

  // Manual grand total override
  isManualGrandTotal: z.boolean().default(false),
  manualGrandTotal: z.coerce.number().optional(),

  // Tax settings
  taxPercentage: z.number().default(0),

  // Line items
  lineItems: z
    .array(
      z.object({
        id: z.string(),
        description: z.string(),
        quantity: z.number().min(1, "Quantity must be at least 1"),
        unitPrice: z.number().min(0, "Unit price must be positive"),
        lineTotal: z.number().default(0),
        productId: z.string().default(""),
        unitType: z.string().optional(),
        discountType: z.string().default("PERCENTAGE"),
        discountValue: z.number().default(0),
        taxRate: z.number().default(0),
        subtotal: z.number().default(0),
      }),
    )
    .default([]),
});

type QuoteFormValues = z.infer<typeof quoteFormSchema>;

interface QuoteCreateFormProps {
  id: string;
  initialData?: any;
  opportunityId?: string;
  opportunityData?: any;
  opportunityProducts?: any[];
  opportunityPackages?: any[];
  organizationDetails?: any;
  mode?: string;
}

// Helper function to safely parse number values
const safeParseNumber = (value: any, defaultValue: number = 0): number => {
  if (value === null || value === undefined || value === "") {
    return defaultValue;
  }
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
};

export function QuoteCreateForm({
  id,
  initialData,
  opportunityId,
  opportunityData,
  opportunityProducts = [],
  opportunityPackages = [],
  organizationDetails,
  mode = "edit",
}: QuoteCreateFormProps) {
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  const isEditMode = id !== "new" && initialData;
  const isViewMode = mode === "view" && isEditMode;

  // Fetch organization's template preference
  const {
    templateId,
    templateName,
    isLoading: isTemplateLoading,
    error: templateError,
  } = useQuoteTemplate();

  // Country configuration hooks
  const { data: countryConfig } = useCurrentCountryConfiguration();
  const { formatCurrency } = useCurrencyFormatter();

  // Organization data hooks
  const { data: organizationData } = useOrganizationData();

  // Helper function to generate pre-filled data from opportunity
  const generatePreFilledDataFromOpportunity = (opportunity: any) => {
    if (!opportunity) return {};

    const account = opportunity.account;
    const contact = opportunity.contact;
    const convertedFromLead = opportunity.convertedFromLead;

    // Helper function to parse address
    const parseAddress = (fullAddress: string) => {
      if (!fullAddress) return {};

      const lines = fullAddress
        .split("\n")
        .map((line) => line.trim())
        .filter(Boolean);
      if (lines.length === 0) return {};

      // Simple address parsing - can be enhanced
      const lastLine = lines[lines.length - 1];
      const cityStateZipPattern =
        /^(.+),\s*([A-Z]{2})\s+(\d{5}(?:-\d{4})?)(?:,\s*(.+))?$/i;
      const cityStateMatch = lastLine.match(cityStateZipPattern);

      if (cityStateMatch) {
        return {
          address: lines.slice(0, -1).join("\n") || lines[0] || "",
          city: cityStateMatch[1].trim(),
          state: cityStateMatch[2].trim(),
          postalCode: cityStateMatch[3].trim(),
          country: cityStateMatch[4]?.trim() || "",
        };
      }

      return {
        address: fullAddress,
        city: "",
        state: "",
        postalCode: "",
        country: "",
      };
    };

    const addressToParse =
      account?.billingAddress || account?.shippingAddress || "";
    const parsedAddress = parseAddress(addressToParse);

    // Determine the best business name to use
    let businessName = "";
    if (account?.name) {
      businessName = account.name;
    } else if (convertedFromLead?.company) {
      businessName = convertedFromLead.company;
    } else if (contact?.firstName || contact?.lastName) {
      businessName =
        `${contact.firstName || ""} ${contact.lastName || ""}`.trim();
    } else if (convertedFromLead?.firstName || convertedFromLead?.lastName) {
      businessName =
        `${convertedFromLead.firstName || ""} ${convertedFromLead.lastName || ""}`.trim();
    }

    return {
      name:
        opportunity.dealName || opportunity.name
          ? `Quote for ${opportunity.dealName || opportunity.name}`
          : "",
      description:
        opportunity.description ||
        (convertedFromLead
          ? `Converted from lead: ${convertedFromLead.firstName} ${convertedFromLead.lastName}`
          : ""),
      contactId: opportunity.contactId || "",
      accountId: opportunity.accountId || "",

      // Quotation To fields - prioritize lead/contact information over account
      quotationToBusinessName: businessName,
      quotationToAddress: parsedAddress.address || addressToParse || "",
      quotationToCity: parsedAddress.city || "",
      quotationToState: parsedAddress.state || account?.region || "",
      quotationToCountry: parsedAddress.country || account?.region || "",
      quotationToPostalCode: parsedAddress.postalCode || "",
      quotationToGSTIN: "", // Not available in current account structure
    };
  };

  // Storage key for form persistence
  const storageKey = `quote-form-${id}`;

  // Set up form persistence only for new records
  const shouldPersist = id === "new";

  // Flag to prevent saving after successful submission
  const [isSubmissionSuccessful, setIsSubmissionSuccessful] = useState(false);

  // Line items state - transform database line items to match form structure (for individual products only)
  const [lineItems, setLineItems] = useState<any[]>(() => {
    if (initialData?.lines && initialData.lines.length > 0) {
      // Filter only PRODUCT items when loading existing quote (edit mode)
      const productLines = initialData.lines.filter(
        (line: any) => line.itemType === "PRODUCT" || !line.itemType, // Backward compatibility for old quotes without itemType
      );
      return productLines.map((line: any) => ({
        id: line.id || crypto.randomUUID(),
        description: line.description || "",
        quantity: line.quantity || 1,
        unitPrice: line.unitPrice || 0,
        lineTotal: line.lineTotal || 0,
        productId: line.productId || "",
        unitType: line.unitType || "",
        discountType: line.discountType || "PERCENTAGE",
        discountValue: line.discountValue || 0,
        taxRate: line.taxRate || 0,
        subtotal: line.subtotal || 0,
        isManualPricing: line.isManualPricing || false,
        manualLineTotal: line.manualLineTotal || null,
      }));
    } else if (opportunityProducts && opportunityProducts.length > 0) {
      // Pre-populate with individual opportunity products only (packages handled separately)
      return opportunityProducts.map((product: any) => ({
        id: product.id, // Use the generated ID from server
        description: product.description,
        quantity: product.quantity,
        unitPrice: product.unitPrice,
        lineTotal: product.lineTotal,
        productId: product.productId,
        unitType: product.unitType || "",
        discountType: "PERCENTAGE",
        discountValue: 0,
        taxRate: 0,
        subtotal: product.quantity * product.unitPrice,
      }));
    }
    // Default empty line item for new quotes without opportunity
    return [
      {
        id: crypto.randomUUID(),
        description: "",
        quantity: 1,
        unitPrice: 0,
        lineTotal: 0,
        productId: "",
        unitType: "",
        discountType: "PERCENTAGE",
        discountValue: 0,
        taxRate: 0,
        subtotal: 0,
      },
    ];
  });

  // Package items state (for packages from opportunities or existing quotes)
  const [packageItems, setPackageItems] = useState<any[]>(() => {
    if (initialData?.lines && initialData.lines.length > 0) {
      // Filter only PACKAGE items when loading existing quote (edit mode)
      const packageLines = initialData.lines.filter(
        (line: any) => line.itemType === "PACKAGE",
      );
      return packageLines.map((line: any) => ({
        id: line.id || crypto.randomUUID(),
        packageId: line.packageId || null,
        packageName: line.packageName || line.description || "Package",
        description: line.description || "",
        quantity: line.quantity || 1,
        unitPrice: line.unitPrice || 0,
        lineTotal: line.lineTotal || 0,
        discountType: line.discountType || "PERCENTAGE",
        discountValue: line.discountValue || 0,
        taxRate: line.taxRate || 0,
        subtotal: line.subtotal || 0,
      }));
    } else if (opportunityPackages && opportunityPackages.length > 0) {
      // Transform opportunity packages to package line items (as single items, not expanded)
      return opportunityPackages.map((packageItem: any) => ({
        id: crypto.randomUUID(), // Generate new ID for quote line item
        packageId: packageItem.packageId || packageItem.id,
        packageName: packageItem.packageName || packageItem.name || "Package",
        description: packageItem.description || "",
        quantity: packageItem.quantity || 1,
        unitPrice: packageItem.unitPrice || packageItem.totalPrice || 0,
        lineTotal: packageItem.lineTotal || packageItem.totalPrice || 0,
        discountType: packageItem.discountType || "PERCENTAGE",
        discountValue: packageItem.discountValue || 0,
        taxRate: packageItem.taxRate || 0,
        subtotal:
          packageItem.subtotal ||
          packageItem.quantity *
            (packageItem.unitPrice || packageItem.totalPrice),
        lineTotalBeforeDiscount: packageItem.lineTotalBeforeDiscount,
        discount: packageItem.discount,
      }));
    }
    return [];
  });

  // Use the useSaveFormData hook with built-in toast notifications
  const { save: saveQuote, isLoading: isSavingFromHook } = useSaveFormData({
    model: "quote",
    clearFormPersistence: shouldPersist
      ? () => {
          if (typeof window !== "undefined") {
            setIsSubmissionSuccessful(true);
            sessionStorage.removeItem(storageKey);
            sessionStorage.removeItem("quote-form-new");
          }
        }
      : undefined,
    onSuccess: () => {
      // Navigate back to quotes list
      router.push("/quotes");
    },
  });

  // Generate pre-filled data from opportunity if available
  const opportunityPreFill =
    generatePreFilledDataFromOpportunity(opportunityData);

  // Initialize React Hook Form with Zod validation
  const form = useForm<QuoteFormValues>({
    // resolver: zodResolver(quoteFormSchema),
    defaultValues: {
      name: initialData?.name || opportunityPreFill.name || "",
      description:
        initialData?.description || opportunityPreFill.description || "",
      status: initialData?.status || "DRAFT",
      contactId: initialData?.contactId || opportunityPreFill.contactId || "",
      accountId: initialData?.accountId || opportunityPreFill.accountId || "",
      opportunityId: opportunityId || initialData?.opportunityId || "",
      validUntil: initialData?.validUntil
        ? new Date(initialData.validUntil).toISOString().split("T")[0]
        : "",
      paymentTerms: initialData?.paymentTerms || "Net 30",
      deliveryTerms: initialData?.deliveryTerms || "",

      // Quotation From fields - auto-populated from organization details
      quotationFromCountry:
        initialData?.quotationFromCountry ||
        organizationDetails?.country ||
        "US", // Fallback to US if no country provided
      quotationFromBusinessName:
        initialData?.quotationFromBusinessName ||
        organizationDetails?.businessName ||
        organizationDetails?.name ||
        "",
      quotationFromGSTIN:
        initialData?.quotationFromGSTIN || organizationDetails?.gstin || "",
      quotationFromAddress:
        initialData?.quotationFromAddress || organizationDetails?.address || "",
      quotationFromCity:
        initialData?.quotationFromCity || organizationDetails?.city || "",
      quotationFromPostalCode:
        initialData?.quotationFromPostalCode ||
        organizationDetails?.postalCode ||
        "",
      quotationFromState:
        initialData?.quotationFromState || organizationDetails?.state || "",

      // Quotation To fields
      quotationToCountry:
        initialData?.quotationToCountry ||
        opportunityPreFill.quotationToCountry ||
        "",
      quotationToBusinessName:
        initialData?.quotationToBusinessName ||
        opportunityPreFill.quotationToBusinessName ||
        "",
      quotationToGSTIN:
        initialData?.quotationToGSTIN ||
        opportunityPreFill.quotationToGSTIN ||
        "",
      quotationToAddress:
        initialData?.quotationToAddress ||
        opportunityPreFill.quotationToAddress ||
        "",
      quotationToCity:
        initialData?.quotationToCity ||
        opportunityPreFill.quotationToCity ||
        "",
      quotationToPostalCode:
        initialData?.quotationToPostalCode ||
        opportunityPreFill.quotationToPostalCode ||
        "",
      quotationToState:
        initialData?.quotationToState ||
        opportunityPreFill.quotationToState ||
        "",

      subtotal: initialData?.subtotal || 0,
      totalTax: initialData?.totalTax || 0,
      totalDiscount: initialData?.totalDiscount || 0,
      grandTotal: initialData?.grandTotal || 0,

      // Enhanced pricing fields
      quoteDiscountType: initialData?.quoteDiscountType || "PERCENTAGE",
      quoteDiscountValue: initialData?.quoteDiscountValue || 0,
      intraStateTaxRate: initialData?.intraStateTaxRate || 0,
      interStateTaxRate: initialData?.interStateTaxRate || 0,
      adjustments: initialData?.adjustments || 0,
      isManualTotal: initialData?.isManualTotal || false,
      manualTotalValue: initialData?.manualTotalValue,
      isManualGrandTotal: initialData?.isManualGrandTotal || false,
      manualGrandTotal: initialData?.manualGrandTotal,

      taxPercentage: 0, // This is a form-only field for calculation
      lineItems: lineItems,
    },
  });

  // Simple form persistence for new quotes
  useEffect(() => {
    if (
      !shouldPersist ||
      typeof window === "undefined" ||
      isSubmissionSuccessful
    )
      return;

    // Watch for form changes and save to storage
    const subscription = form.watch((data) => {
      try {
        sessionStorage.setItem(storageKey, JSON.stringify(data));
      } catch (error) {
        console.error("Error saving form data:", error);
      }
    });

    return () => subscription.unsubscribe();
  }, [shouldPersist, form, storageKey, isSubmissionSuccessful]);

  // Restore saved form data on mount (for form persistence only)
  useEffect(() => {
    if (!shouldPersist || typeof window === "undefined") return;

    try {
      const savedData = sessionStorage.getItem(storageKey);
      if (savedData && savedData !== "{}") {
        const parsedData = JSON.parse(savedData);
        Object.keys(parsedData).forEach((key) => {
          if (
            parsedData[key] !== undefined &&
            parsedData[key] !== null &&
            parsedData[key] !== ""
          ) {
            form.setValue(key as any, parsedData[key]);
          }
        });
      }
    } catch (error) {
      console.error("Error restoring form data:", error);
    }
  }, [shouldPersist, form, storageKey]);

  // Effect to update tax rates when country configuration changes
  useEffect(() => {
    if (countryConfig?.configuration && !isEditMode) {
      // Only auto-populate for new quotes, not when editing existing ones
      const config = countryConfig.configuration;

      // Set default tax rates based on country configuration
      if (config.primaryTaxRate > 0) {
        form.setValue("intraStateTaxRate", config.primaryTaxRate);

        // For countries with secondary tax, set inter-state rate
        if (config.secondaryTaxRate && config.secondaryTaxRate > 0) {
          form.setValue("interStateTaxRate", config.secondaryTaxRate);
        } else {
          // For single tax systems, use the same rate for both
          form.setValue("interStateTaxRate", config.primaryTaxRate);
        }
      }
    }
  }, [countryConfig, isEditMode, form]);

  // Effect to reset form when initialData changes (only for edit mode)
  useEffect(() => {
    if (initialData && isEditMode) {
      form.reset({
        name: initialData.name || "",
        description: initialData.description || "",
        status: initialData.status || "DRAFT",
        contactId: initialData.contactId || "",
        accountId: initialData.accountId || "",
        opportunityId: initialData.opportunityId || "",
        validUntil: initialData.validUntil
          ? new Date(initialData.validUntil).toISOString().split("T")[0]
          : "",
        paymentTerms: initialData.paymentTerms || "Net 30",
        deliveryTerms: initialData.deliveryTerms || "",
        quotationFromCountry:
          initialData.quotationFromCountry ||
          organizationDetails?.country ||
          "US", // Fallback to US if no country provided
        quotationFromBusinessName:
          initialData.quotationFromBusinessName ||
          organizationDetails?.businessName ||
          organizationDetails?.name ||
          "Your Business", // Fallback business name
        quotationFromGSTIN:
          initialData.quotationFromGSTIN || organizationDetails?.gstin || "",
        quotationFromAddress:
          initialData.quotationFromAddress ||
          organizationDetails?.address ||
          "",
        quotationFromCity:
          initialData.quotationFromCity || organizationDetails?.city || "",
        quotationFromPostalCode:
          initialData.quotationFromPostalCode ||
          organizationDetails?.postalCode ||
          "",
        quotationFromState:
          initialData.quotationFromState || organizationDetails?.state || "",
        quotationToCountry: initialData.quotationToCountry || "",
        quotationToBusinessName: initialData.quotationToBusinessName || "",
        quotationToGSTIN: initialData.quotationToGSTIN || "",
        quotationToAddress: initialData.quotationToAddress || "",
        quotationToCity: initialData.quotationToCity || "",
        quotationToPostalCode: initialData.quotationToPostalCode || "",
        quotationToState: initialData.quotationToState || "",
        subtotal: initialData.subtotal || 0,
        totalTax: initialData.totalTax || 0,
        totalDiscount: initialData.totalDiscount || 0,
        grandTotal: initialData.grandTotal || 0,
        taxPercentage: 0, // Default tax percentage
        lineItems: lineItems, // Will be handled separately by lineItems state
      });

      // Also update line items and package items state
      if (initialData.lines && initialData.lines.length > 0) {
        // Filter and transform product lines only
        const productLines = initialData.lines.filter(
          (line: any) => line.itemType === "PRODUCT" || !line.itemType, // Backward compatibility
        );
        const transformedLines = productLines.map((line: any) => ({
          id: line.id || crypto.randomUUID(),
          description: line.description || "",
          quantity: line.quantity || 1,
          unitPrice: line.unitPrice || 0,
          lineTotal: line.lineTotal || 0,
          productId: line.productId || "",
          unitType: line.unitType || "",
          discountType: line.discountType || "PERCENTAGE",
          discountValue: line.discountValue || 0,
          taxRate: line.taxRate || 0,
          subtotal: line.subtotal || 0,
          isManualPricing: line.isManualPricing || false,
          manualLineTotal: line.manualLineTotal || null,
        }));
        setLineItems(transformedLines);

        // Filter and transform package lines only
        const packageLines = initialData.lines.filter(
          (line: any) => line.itemType === "PACKAGE",
        );
        const transformedPackages = packageLines.map((line: any) => ({
          id: line.id || crypto.randomUUID(),
          packageId: line.packageId || null,
          packageName: line.packageName || line.description || "Package",
          description: line.description || "",
          quantity: line.quantity || 1,
          unitPrice: line.unitPrice || 0,
          lineTotal: line.lineTotal || 0,
          discountType: line.discountType || "PERCENTAGE",
          discountValue: line.discountValue || 0,
          taxRate: line.taxRate || 0,
          subtotal: line.subtotal || 0,
        }));
        setPackageItems(transformedPackages);
      }
    }
  }, [initialData, form, organizationDetails, isEditMode]);

  // Calculate totals when line items or package items change
  const calculateTotals = React.useCallback(
    (updatedLineItems?: any[], updatedPackageItems?: any[]) => {
      const itemsToCalculate = updatedLineItems || lineItems;
      const packagesToCalculate = updatedPackageItems || packageItems;

      // Calculate subtotal from both line items and package items
      const lineItemsSubtotal = itemsToCalculate.reduce(
        (sum, item) => sum + (item.lineTotal || 0),
        0,
      );

      const packageItemsSubtotal = packagesToCalculate.reduce(
        (sum, item) => sum + (item.lineTotal || 0),
        0,
      );

      const subtotal = lineItemsSubtotal + packageItemsSubtotal;

      // Get quote-level discount with safe number conversion
      const quoteDiscountType =
        form.getValues("quoteDiscountType") || "PERCENTAGE";
      const quoteDiscountValue = safeParseNumber(
        form.getValues("quoteDiscountValue"),
        0,
      );

      let quoteDiscount = 0;
      if (quoteDiscountValue > 0) {
        if (quoteDiscountType === "PERCENTAGE") {
          quoteDiscount = (subtotal * quoteDiscountValue) / 100;
        } else {
          quoteDiscount = quoteDiscountValue;
        }
      }

      // Calculate tax rates with safe number conversion
      const intraStateTaxRate = safeParseNumber(
        form.getValues("intraStateTaxRate"),
        0,
      );
      const interStateTaxRate = safeParseNumber(
        form.getValues("interStateTaxRate"),
        0,
      );
      const adjustments = safeParseNumber(form.getValues("adjustments"), 0);

      const amountAfterDiscount = subtotal - quoteDiscount;
      const intraStateTax = (amountAfterDiscount * intraStateTaxRate) / 100;
      const interStateTax = (amountAfterDiscount * interStateTaxRate) / 100;
      const totalTax = intraStateTax + interStateTax;

      // Check for manual total override
      const isManualTotal = form.getValues("isManualTotal") || false;
      const manualTotalValue = safeParseNumber(
        form.getValues("manualTotalValue"),
      );

      // Check for manual grand total override
      const isManualGrandTotal = form.getValues("isManualGrandTotal") || false;
      const manualGrandTotal = safeParseNumber(
        form.getValues("manualGrandTotal"),
      );

      const calculatedGrandTotal = amountAfterDiscount + totalTax + adjustments;
      const grandTotal =
        isManualGrandTotal && manualGrandTotal > 0
          ? manualGrandTotal
          : isManualTotal && manualTotalValue > 0
            ? manualTotalValue
            : calculatedGrandTotal;

      // Update form values with proper number formatting
      form.setValue("subtotal", Number(subtotal.toFixed(2)));
      form.setValue("totalDiscount", Number(quoteDiscount.toFixed(2)));
      form.setValue("totalTax", Number(totalTax.toFixed(2)));
      form.setValue("grandTotal", Number(grandTotal.toFixed(2)));
      form.setValue("lineItems", itemsToCalculate);
    },
    [lineItems, packageItems, form],
  );

  // Watch for pricing field changes
  const quoteDiscountType = form.watch("quoteDiscountType");
  const quoteDiscountValue = form.watch("quoteDiscountValue");
  const intraStateTaxRate = form.watch("intraStateTaxRate");
  const interStateTaxRate = form.watch("interStateTaxRate");
  const adjustments = form.watch("adjustments");
  const isManualTotal = form.watch("isManualTotal");
  const manualTotalValue = form.watch("manualTotalValue");
  const isManualGrandTotal = form.watch("isManualGrandTotal");
  const manualGrandTotal = form.watch("manualGrandTotal");

  // Update totals when any pricing field changes
  React.useEffect(() => {
    calculateTotals();
  }, [
    calculateTotals,
    quoteDiscountType,
    quoteDiscountValue,
    intraStateTaxRate,
    interStateTaxRate,
    adjustments,
    isManualTotal,
    manualTotalValue,
    isManualGrandTotal,
    manualGrandTotal,
  ]);

  const onSubmit = async (data: QuoteFormValues) => {
    try {
      // Validate and clean numeric fields before submission
      const cleanedData = {
        ...data,
        quoteDiscountValue: safeParseNumber(data.quoteDiscountValue, 0),
        intraStateTaxRate: safeParseNumber(data.intraStateTaxRate, 0),
        interStateTaxRate: safeParseNumber(data.interStateTaxRate, 0),
        adjustments: safeParseNumber(data.adjustments, 0),
        manualTotalValue: data.manualTotalValue
          ? safeParseNumber(data.manualTotalValue, 0)
          : undefined,
        isManualGrandTotal: data.isManualGrandTotal || false,
        manualGrandTotal: data.manualGrandTotal
          ? safeParseNumber(data.manualGrandTotal, 0)
          : undefined,
      };

      // Prepare the quote data with line items
      const quoteData = {
        ...cleanedData,
        // Include opportunity ID if provided
        opportunityId: opportunityId || data.opportunityId || null,
        // Convert date string to Date object
        validUntil: data.validUntil ? new Date(data.validUntil) : null,
        // Prepare all line items for creation (both products and packages)
        lines: [
          // Individual product line items
          ...lineItems.map((item, index) => ({
            lineNumber: index + 1,
            description: item.description,
            quantity: safeParseNumber(item.quantity, 1),
            unitPrice: safeParseNumber(item.unitPrice, 0),
            lineTotal: safeParseNumber(item.lineTotal, 0),
            productId: item.productId || null,
            priceBookId: item.priceBookId || null,
            unitType: item.unitType || null,
            discountType: item.discountType || "PERCENTAGE",
            discountValue: safeParseNumber(item.discountValue, 0),
            taxRate: safeParseNumber(item.taxRate, 0),
            subtotal: safeParseNumber(item.subtotal, 0),
            itemType: "PRODUCT", // Mark as product line item
            isManualPricing: item.isManualPricing || false,
            manualLineTotal: item.manualLineTotal
              ? safeParseNumber(item.manualLineTotal, 0)
              : null,
          })),
          // Package line items
          ...packageItems.map((item, index) => ({
            lineNumber: lineItems.length + index + 1, // Continue numbering after products
            description: item.packageName || item.description,
            quantity: safeParseNumber(item.quantity, 1),
            unitPrice: safeParseNumber(item.unitPrice, 0),
            lineTotal: safeParseNumber(item.lineTotal, 0),
            productId: null, // Packages don't have productId
            unitType: null,
            discountType: item.discountType || "PERCENTAGE",
            discountValue: safeParseNumber(item.discountValue, 0),
            taxRate: safeParseNumber(item.taxRate, 0),
            subtotal: safeParseNumber(item.subtotal, 0),
            itemType: "PACKAGE", // Mark as package line item
            packageId: item.packageId || null,
            packageName: item.packageName || null,
            packageContents: null, // Don't store package contents
          })),
        ],
      };

      // Remove the form-only fields and ensure proper number formatting
      const { lineItems: _, taxPercentage: __, ...quoteDataRaw } = quoteData;

      // Ensure all numeric fields are properly formatted
      const finalQuoteData = {
        ...quoteDataRaw,
        quoteDiscountValue: safeParseNumber(quoteDataRaw.quoteDiscountValue, 0),
        intraStateTaxRate: safeParseNumber(quoteDataRaw.intraStateTaxRate, 0),
        interStateTaxRate: safeParseNumber(quoteDataRaw.interStateTaxRate, 0),
        adjustments: safeParseNumber(quoteDataRaw.adjustments, 0),
        manualTotalValue: quoteDataRaw.manualTotalValue
          ? safeParseNumber(quoteDataRaw.manualTotalValue, 0)
          : undefined,
        isManualGrandTotal: quoteDataRaw.isManualGrandTotal || false,
        manualGrandTotal: quoteDataRaw.manualGrandTotal
          ? safeParseNumber(quoteDataRaw.manualGrandTotal, 0)
          : undefined,
      };

      await saveQuote(id, finalQuoteData);
    } catch (error) {
      console.error("Error saving quote:", error);
    }
  };

  // View mode rendering
  if (isViewMode && initialData) {
    const statusOptions = [
      { label: "Draft", value: "DRAFT" },
      { label: "In Review", value: "IN_REVIEW" },
      { label: "Sent", value: "SENT" },
      { label: "Accepted", value: "ACCEPTED" },
      { label: "Rejected", value: "REJECTED" },
      { label: "Expired", value: "EXPIRED" },
      { label: "Converted", value: "CONVERTED" },
      { label: "Cancelled", value: "CANCELLED" },
    ];

    const statusOption = statusOptions.find(
      (option) => option.value === initialData.status
    );

    const formatDate = (date: string | Date) => {
      const dateObj = typeof date === "string" ? new Date(date) : date;
      return dateObj.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    };

    return (
      <div className="container mx-auto py-6 space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{initialData.name}</h1>
            <p className="text-muted-foreground">
              Quote #{initialData.id.slice(-8).toUpperCase()}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/quotes/${id}?title=${encodeURIComponent(`Edit - ${initialData.name}`)}&mode=edit`)}
            >
              Edit
            </Button>
            <EnhancedQuoteGeneratorButton
              quote={{
                quote_number: `QUO-${initialData.id.slice(-8).toUpperCase()}`,
                date_issued: initialData.createdAt,
                valid_until: initialData.validUntil || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                customer_name: initialData.contact
                  ? `${initialData.contact.firstName} ${initialData.contact.lastName}`
                  : initialData.account?.name || "Customer",
                customer_address: initialData.account?.address || "Customer Address",
                payment_terms: initialData.paymentTerms || "Net 30",
                line_items: initialData.lines?.map((line: any) => ({
                  description: line.description,
                  quantity: line.quantity,
                  unit_price: line.unitPrice,
                  subtotal: line.subtotal || 0,
                  discount_type: line.discountType || "PERCENTAGE",
                  discount_value: line.discountValue || 0,
                  tax_rate: line.taxRate || 0,
                  is_manual_pricing: line.isManualPricing || false,
                  manual_line_total: line.manualLineTotal || undefined,
                  item_type: "PRODUCT" as const,
                  unit_type: line.unitType || undefined,
                })) || [],
                package_items: [],
                quote_discount_type: (initialData.quoteDiscountType || "PERCENTAGE") as "PERCENTAGE" | "FLAT",
                quote_discount_value: initialData.quoteDiscountValue || 0,
                intra_state_tax_rate: initialData.intraStateTaxRate || 0,
                inter_state_tax_rate: initialData.interStateTaxRate || 0,
                adjustments: initialData.adjustments || 0,
                is_manual_total: initialData.isManualTotal || false,
                manual_total_value: initialData.manualTotalValue || undefined,
                is_manual_grand_total: initialData.isManualGrandTotal || false,
                manual_grand_total: initialData.manualGrandTotal || undefined,
              }}
              templateId={templateId}
              variant="default"
              size="sm"
              options={{
                company_name: organizationData?.data?.organization?.name || "Flinkk CRM",
                company_logo: organizationData?.data?.organization?.logo || undefined,
                company_address: "Tech Park, Electronic City\nBangalore, Karnataka 560100\nIndia",
              }}
            >
              Generate PDF {templateId && `(${templateName})`}
            </EnhancedQuoteGeneratorButton>
          </div>
        </div>

        {/* Quote Details Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg border">
            <h3 className="text-lg font-semibold mb-4">Quote Information</h3>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <div className="mt-1">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {statusOption?.label || initialData.status}
                  </span>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Total Amount</label>
                <div className="mt-1 text-lg font-semibold">
                  {formatCurrency(parseFloat(initialData.grandTotal))}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Valid Until</label>
                <div className="mt-1">
                  {initialData.validUntil ? formatDate(initialData.validUntil) : "—"}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Created</label>
                <div className="mt-1">{formatDate(initialData.createdAt)}</div>
              </div>
            </div>
          </div>

          {/* Customer Information */}
          {(initialData.contact || initialData.account) && (
            <div className="bg-white p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-4">Customer Information</h3>
              <div className="space-y-3">
                {initialData.contact && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Contact</label>
                    <div className="mt-1">
                      {initialData.contact.firstName} {initialData.contact.lastName}
                    </div>
                  </div>
                )}
                {initialData.account && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Account</label>
                    <div className="mt-1">{initialData.account.name}</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Payment Terms */}
          {initialData.paymentTerms && (
            <div className="bg-white p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-4">Payment Terms</h3>
              <div>{initialData.paymentTerms}</div>
            </div>
          )}
        </div>

        {/* Line Items */}
        {initialData.lines && initialData.lines.length > 0 && (
          <div className="bg-white p-6 rounded-lg border">
            <h3 className="text-lg font-semibold mb-4">Line Items</h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Description</th>
                    <th className="text-right p-2">Quantity</th>
                    <th className="text-right p-2">Unit Price</th>
                    <th className="text-right p-2">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {initialData.lines.map((line: any, index: number) => (
                    <tr key={index} className="border-b">
                      <td className="p-2">{line.description}</td>
                      <td className="text-right p-2">{line.quantity}</td>
                      <td className="text-right p-2">
                        {formatCurrency(line.unitPrice)}
                      </td>
                      <td className="text-right p-2">
                        {formatCurrency(line.subtotal || line.quantity * line.unitPrice)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* Responsive 2-column layout */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            {/* Left Column (8/12) - Core quote information */}
            <div className="lg:col-span-8 space-y-6">
              {/* Quote Information Section */}
              <SectionWrapper
                title="Quotation Information"
                description="Basic details of the quotation."
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TextFieldFormElement
                    name="name"
                    label="Quotation Name"
                    control={form.control}
                    placeholder="e.g. Website Redesign Q3"
                    required
                  />
                  {/* Only show opportunity selector if not creating from an opportunity */}
                  {!opportunityId && (
                    <DynamicSelectFieldFormElement
                      name="opportunityId"
                      label="Related Opportunity"
                      control={form.control}
                      placeholder="Select an opportunity (optional)"
                      dynamicKey="opportunities"
                      allowNone={true}
                    />
                  )}
                </div>
                <div className="mt-6">
                  <TextAreaFieldFormElement
                    name="description"
                    label="Description"
                    control={form.control}
                    placeholder="Provide a brief description of the quotation..."
                  />
                </div>
              </SectionWrapper>

              {/* Quotation From/To Section - HIDDEN: Auto-populated from opportunity and organization data */}
              {/*
            Note: The "Quotation From" and "Quotation To" form sections have been hidden
            to streamline the UI since these fields are auto-populated from:
            - Quotation From: Organization details (business name, address, etc.)
            - Quotation To: Opportunity account/contact details

            The data is still being captured and stored in the form state and database,
            and will be included in PDF generation and quote records.

            If manual editing is needed in the future, uncomment the sections below.
          */}
              {/*
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <SectionWrapper
              title="Quotation From"
              description="Your Details"
              className="col-span-1"
            >
              <div className="space-y-4">
                <CountryFieldFormElement
                  name="quotationFromCountry"
                  label="Country"
                  control={form.control}
                  placeholder="Select country"
                  required
                />
                <TextFieldFormElement
                  name="quotationFromBusinessName"
                  label="Business Name"
                  control={form.control}
                  placeholder="Enter your business name"
                  required
                />
                <TextFieldFormElement
                  name="quotationFromGSTIN"
                  label="Your GSTIN"
                  control={form.control}
                  placeholder="Enter your GSTIN (optional)"
                />
                <TextAreaFieldFormElement
                  name="quotationFromAddress"
                  label="Address"
                  control={form.control}
                  placeholder="Enter your address (optional)"
                />
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <TextFieldFormElement
                    name="quotationFromCity"
                    label="City"
                    control={form.control}
                    placeholder="Enter city (optional)"
                  />
                  <TextFieldFormElement
                    name="quotationFromPostalCode"
                    label="Postal Code / ZIP Code"
                    control={form.control}
                    placeholder="Enter postal code (optional)"
                  />
                </div>
                <TextFieldFormElement
                  name="quotationFromState"
                  label="State"
                  control={form.control}
                  placeholder="Enter state (optional)"
                />
              </div>
            </SectionWrapper>

            <SectionWrapper
              title="Quotation For"
              description="Client's Details"
              className="col-span-1"
            >
              <div className="space-y-4">
                <CountryFieldFormElement
                  name="quotationToCountry"
                  label="Country"
                  control={form.control}
                  placeholder="Select country"
                />
                <TextFieldFormElement
                  name="quotationToBusinessName"
                  label="Client's Business Name"
                  control={form.control}
                  placeholder="Enter client's business name"
                  required
                />
                <TextFieldFormElement
                  name="quotationToGSTIN"
                  label="Client's GSTIN"
                  control={form.control}
                  placeholder="Enter client's GSTIN (optional)"
                />
                <TextAreaFieldFormElement
                  name="quotationToAddress"
                  label="Address"
                  control={form.control}
                  placeholder="Enter client's address (optional)"
                />
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <TextFieldFormElement
                    name="quotationToCity"
                    label="City"
                    control={form.control}
                    placeholder="Enter city (optional)"
                  />
                  <TextFieldFormElement
                    name="quotationToPostalCode"
                    label="Postal Code / ZIP Code"
                    control={form.control}
                    placeholder="Enter postal code (optional)"
                  />
                </div>
                <TextFieldFormElement
                  name="quotationToState"
                  label="State"
                  control={form.control}
                  placeholder="Enter state (optional)"
                />
              </div>
            </SectionWrapper>
          </div>
          */}

              {/* Packages Section - Only show if there are packages or if this is a new quote without opportunity */}
              {(packageItems.length > 0 ||
                opportunityPackages.length > 0 ||
                (id === "new" && !opportunityId)) && (
                <SectionWrapper title="Packages">
                  <PackageLineItemsTable
                    packageItems={packageItems}
                    setPackageItems={setPackageItems}
                    onCalculateTotal={(updatedPackageItems) =>
                      calculateTotals(undefined, updatedPackageItems)
                    }
                    title="Packages"
                  />
                </SectionWrapper>
              )}

              {/* Line Items Section - Only show if there are products or if this is a new quote without opportunity */}
              {(lineItems.length > 0 ||
                opportunityProducts.length > 0 ||
                (id === "new" && !opportunityId)) && (
                <SectionWrapper title="Products">
                  <EnhancedLineItemsTable
                    lineItems={lineItems}
                    setLineItems={setLineItems}
                    onCalculateTotal={(updatedItems) =>
                      calculateTotals(updatedItems, undefined)
                    }
                    showUnits={true}
                    showDiscounts={true}
                    showListPrice={false}
                    showTaxRate={true}
                    title="Products"
                  />
                </SectionWrapper>
              )}
            </div>

            {/* Right Column (4/12) - Quote summary and pricing */}
            <div className="lg:col-span-4 space-y-6">
              {/* Quote Summary Card */}
              <div className="bg-gray-50 rounded-lg p-6 border">
                <h3 className="text-lg font-semibold mb-4">Quote Summary</h3>

                {/* Quote-level Discount */}
                <div className="space-y-4 mb-6">
                  <h4 className="font-medium text-sm text-gray-700">
                    Quote Discount
                  </h4>
                  <div className="flex gap-2">
                    <FormField
                      control={form.control}
                      name="quoteDiscountType"
                      render={({ field }) => (
                        <FormItem className="w-20">
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="PERCENTAGE">%</SelectItem>
                                <SelectItem value="FLAT">
                                  {countryConfig?.configuration
                                    ?.currencySymbol || "$"}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <NumberFieldFormElement
                      control={form.control}
                      name="quoteDiscountValue"
                      label=""
                      placeholder="0.00"
                      step={0.01}
                      min={0}
                    />
                  </div>
                </div>

                {/* Tax Rates */}
                <div className="space-y-4 mb-6">
                  <h4 className="font-medium text-sm text-gray-700">
                    Tax Rates
                  </h4>
                  <div className="grid grid-cols-1 gap-3">
                    <NumberFieldFormElement
                      control={form.control}
                      name="intraStateTaxRate"
                      label={`${countryConfig?.configuration?.primaryTaxName || "Intra-State Tax"} (%)`}
                      placeholder="0.00"
                      step={0.01}
                      min={0}
                      max={100}
                    />
                    <NumberFieldFormElement
                      control={form.control}
                      name="interStateTaxRate"
                      label={`${countryConfig?.configuration?.secondaryTaxName || "Inter-State Tax"} (%)`}
                      placeholder="0.00"
                      step={0.01}
                      min={0}
                      max={100}
                    />
                  </div>
                </div>

                {/* Adjustments */}
                <div className="space-y-4 mb-6">
                  <NumberFieldFormElement
                    control={form.control}
                    name="adjustments"
                    label={`Adjustments (${countryConfig?.configuration?.currencySymbol || "$"})`}
                    placeholder="0.00"
                    step={0.01}
                  />
                </div>

                {/* Manual Grand Total Override */}
                <div className="space-y-4 mb-6">
                  <div className="flex items-center space-x-2">
                    <FormField
                      control={form.control}
                      name="isManualGrandTotal"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4 rounded border-gray-300"
                            />
                          </FormControl>
                          <FormLabel className="text-sm font-medium">
                            Manual Grand Total
                          </FormLabel>
                        </FormItem>
                      )}
                    />
                  </div>
                  {form.watch("isManualGrandTotal") && (
                    <NumberFieldFormElement
                      control={form.control}
                      name="manualGrandTotal"
                      label={`Grand Total Override (${countryConfig?.configuration?.currencySymbol || "$"})`}
                      placeholder="0.00"
                      step={0.01}
                      min={0}
                    />
                  )}
                </div>

                {/* Totals Summary */}
                <div className="space-y-3 pt-4 border-t">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal:</span>
                    <span className="font-medium">
                      {formatCurrency(form.watch("subtotal") || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Quote Discount:</span>
                    <span className="font-medium text-red-600">
                      -{formatCurrency(form.watch("totalDiscount") || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>
                      {countryConfig?.configuration?.primaryTaxName || "Tax"}:
                    </span>
                    <span className="font-medium">
                      {formatCurrency(form.watch("totalTax") || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Adjustments:</span>
                    <span className="font-medium">
                      {formatCurrency(
                        safeParseNumber(form.watch("adjustments"), 0),
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t pt-2">
                    <span>Grand Total:</span>
                    <span>{formatCurrency(form.watch("grandTotal") || 0)}</span>
                  </div>
                </div>
              </div>

              {/* Quote Actions */}
              <div className="bg-white rounded-lg p-6 border space-y-4">
                <h3 className="text-lg font-semibold">Actions</h3>

                <div className="space-y-3">
                  <EnhancedQuoteGeneratorButton
                    quote={{
                      quote_number: `QUO-${Date.now()}`,
                      date_issued: new Date().toISOString(),
                      valid_until: form.watch("validUntil")
                        ? new Date(
                            form.watch("validUntil") as string,
                          ).toISOString()
                        : new Date(
                            Date.now() + 30 * 24 * 60 * 60 * 1000,
                          ).toISOString(),
                      customer_name: form.watch("name") || "Customer",
                      customer_address: "Customer Address",
                      payment_terms: form.watch("paymentTerms") || "Net 30",
                      // Always provide arrays for line_items and package_items
                      line_items:
                        Array.isArray(lineItems) && lineItems.length > 0
                          ? lineItems.map((item) => ({
                              description:
                                item.description || "Service/Product",
                              quantity: item.quantity || 1,
                              unit_price: item.unitPrice || 0,
                              subtotal: item.subtotal || 0,
                              discount_type: item.discountType || "PERCENTAGE",
                              discount_value: item.discountValue || 0,
                              tax_rate: item.taxRate || 0,
                              is_manual_pricing: item.isManualPricing || false,
                              manual_line_total:
                                item.manualLineTotal || undefined,
                              item_type: "PRODUCT" as const,
                              unit_type: item.unitType || undefined,
                            }))
                          : [],
                      package_items:
                        Array.isArray(packageItems) && packageItems.length > 0
                          ? packageItems.map((item) => ({
                              package_name: item.packageName || "Package",
                              description: item.description || "",
                              quantity: item.quantity || 1,
                              unit_price: item.unitPrice || 0,
                              subtotal: item.subtotal || 0,
                              discount_type: item.discountType || "PERCENTAGE",
                              discount_value: item.discountValue || 0,
                              tax_rate: item.taxRate || 0,
                              is_manual_pricing: false, // Packages don't use manual pricing in PDF
                              package_id: item.packageId || "",
                            }))
                          : [],
                      // Enhanced pricing fields
                      quote_discount_type: (form.watch("quoteDiscountType") ||
                        "PERCENTAGE") as "PERCENTAGE" | "FLAT",
                      quote_discount_value:
                        form.watch("quoteDiscountValue") || 0,
                      intra_state_tax_rate:
                        form.watch("intraStateTaxRate") || 0,
                      inter_state_tax_rate:
                        form.watch("interStateTaxRate") || 0,
                      adjustments: form.watch("adjustments") || 0,
                      is_manual_total: form.watch("isManualTotal") || false,
                      manual_total_value:
                        form.watch("manualTotalValue") || undefined,
                      is_manual_grand_total:
                        form.watch("isManualGrandTotal") || false,
                      manual_grand_total:
                        form.watch("manualGrandTotal") || undefined,
                      quotation_from: {
                        business_name:
                          form.watch("quotationFromBusinessName") ||
                          "Your Business",
                        address:
                          form.watch("quotationFromAddress") || undefined,
                        city: form.watch("quotationFromCity") || undefined,
                        state: form.watch("quotationFromState") || undefined,
                        postal_code:
                          form.watch("quotationFromPostalCode") || undefined,
                        country:
                          form.watch("quotationFromCountry") || undefined,
                        gstin: form.watch("quotationFromGSTIN") || undefined,
                      },
                      quotation_to: {
                        business_name:
                          form.watch("quotationToBusinessName") ||
                          form.watch("name") ||
                          "Customer",
                        address: form.watch("quotationToAddress") || undefined,
                        city: form.watch("quotationToCity") || undefined,
                        state: form.watch("quotationToState") || undefined,
                        postal_code:
                          form.watch("quotationToPostalCode") || undefined,
                        country: form.watch("quotationToCountry") || undefined,
                        gstin: form.watch("quotationToGSTIN") || undefined,
                      },
                    }}
                    templateId={templateId}
                    variant="download"
                    size="default"
                    className="w-full"
                    countryConfig={countryConfig?.configuration as any}
                    options={{
                      company_name:
                        organizationData?.data?.organization?.name ||
                        "Your Company",
                      company_logo:
                        organizationData?.data?.organization?.logo || undefined,
                      company_address:
                        form.watch("quotationFromAddress") ||
                        "Your Company Address",
                    }}
                  >
                    Generate PDF {templateId && `(${templateName})`}
                  </EnhancedQuoteGeneratorButton>

                  <Button
                    onClick={form.handleSubmit(onSubmit)}
                    disabled={isSavingFromHook}
                    className="w-full"
                  >
                    {isSavingFromHook ? (
                      <>
                        <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        {isEditMode ? "Update Quotation" : "Create Quotation"}
                      </>
                    )}
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push("/quotes")}
                    className="w-full"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
