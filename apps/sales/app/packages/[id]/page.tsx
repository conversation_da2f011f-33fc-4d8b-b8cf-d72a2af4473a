import { NewPackageForm } from "./page-client";
import { getPackageById } from "./actions";

import { getFieldPermissionsFromSession } from "@flinkk/shared-auth/utils/field-permissions-utils";
import { validateAction } from "@flinkk/shared-rbac";
import { createRBACPrismaClient } from "@flinkk/shared-rbac";
import { getServerSession } from "@flinkk/shared-auth/server-session";

export default async function NewPackagePage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = await params;
  const isNewRecord = id === "new";

  let initialData;
  let customFields: any[] = [];

  if (!isNewRecord) {
    initialData = await getPackageById(id);
  }

  // Validate permissions based on the operation
  if (isNewRecord) {
    // Validate create permission for new packages
    await validateAction(
      "package",
      "create",
      "You do not have permission to create packages."
    );
  } else {
    // Validate update permission for existing packages
    await validateAction(
      "package",
      "update",
      "You do not have permission to view package details."
    );
  }

  // Fetch field permissions at server level
  const fieldPermissionsData = await getFieldPermissionsFromSession("Package");

  // Fetch custom fields for packages with RBAC
  const { tenantId } = await getServerSession();
  if (tenantId) {
    try {
      // Create RBAC-enhanced Prisma client
      const rbacPrisma = await createRBACPrismaClient();

      customFields = await rbacPrisma.customField.findMany({
        where: {
          tenantId: tenantId,
          entityType: "Package",
        },
        orderBy: {
          order: "asc",
        },
      });
      console.log("Package custom fields fetched:", customFields.length);
    } catch (error) {
      console.error("Error fetching custom fields:", error);
      customFields = [];
    }
  }

  return (
    <NewPackageForm
      id={id}
      initialData={initialData}
      fieldPermissionsData={fieldPermissionsData}
      customFields={customFields}
    />
  );
}
