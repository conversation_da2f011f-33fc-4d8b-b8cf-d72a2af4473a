"use client";

// import React, { useState } from "react";
import { Button } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@flinkk/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import { PlusIcon, TrashIcon, PackageIcon } from "lucide-react";
import { Checkbox } from "@flinkk/components/ui/checkbox";
import { ProductAutocomplete } from "../../quotes/[id]/_components/product-autocomplete";
import { ProductPriceBookSelector } from "../../opportunities/[id]/view/@lines/components/product-price-book-selector";
import { UnitSelector } from "./unit-selector";
import toast from "react-hot-toast";
import type { DiscountType } from "@/types/package";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>T<PERSON><PERSON>,
  CardDescription,
} from "@flinkk/components/ui/card";

// Helper function to safely parse number values
const safeParseNumber = (value: any, defaultValue: number = 0): number => {
  if (value === null || value === undefined || value === "") {
    return defaultValue;
  }
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
};

interface EnhancedLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  lineTotal: number;
  productId?: string;
  priceBookId?: string;
  productSku?: string;
  productDescription?: string;
  unitId?: string;
  unitName?: string;
  unitDisplayName?: string;
  unitType?: string; // Unit selector (pcs, kg, hrs, etc.)
  listPrice?: number;
  discount?: number;
  discountType?: DiscountType; // Per-line discount type
  discountValue?: number; // Discount amount
  taxRate?: number; // Override tax rate per item
  subtotal?: number; // Before discount/tax
  lineTotalBeforeDiscount?: number;
  isPrePopulated?: boolean;
  sourceType?: "product" | "package";
  packageName?: string;
  isFromPackage?: boolean;
  // Manual pricing fields
  isManualPricing?: boolean;
  manualLineTotal?: number;
}

interface EnhancedLineItemsTableProps {
  lineItems: EnhancedLineItem[];
  setLineItems: (items: EnhancedLineItem[]) => void;
  onCalculateTotal: (updatedLineItems?: EnhancedLineItem[]) => void;
  showUnits?: boolean;
  showDiscounts?: boolean;
  showListPrice?: boolean;
  showTaxRate?: boolean;
  title?: string;
}

export function EnhancedLineItemsTable({
  lineItems,
  setLineItems,
  onCalculateTotal,
  showUnits = true,
  showDiscounts = true,
  showListPrice = false,
  showTaxRate = false,
  title = "Line Items",
}: EnhancedLineItemsTableProps) {
  // const [units, setUnits] = useState<any[]>([]);

  // Add a new line item
  const addLineItem = () => {
    const newItem: EnhancedLineItem = {
      id: crypto.randomUUID(),
      description: "",
      quantity: 1,
      unitPrice: 0,
      lineTotal: 0,
      productId: "",
      priceBookId: "",
      discount: 0,
      discountType: "PERCENTAGE",
      discountValue: 0,
      lineTotalBeforeDiscount: 0,
      subtotal: 0,
      taxRate: 0,
      isManualPricing: false,
      manualLineTotal: 0,
    };
    setLineItems([...lineItems, newItem]);
  };

  // Remove a line item
  const removeLineItem = (id: string) => {
    if (lineItems.length > 1) {
      const updatedItems = lineItems.filter((item) => item.id !== id);

      // Update state and calculate totals with the new items
      setLineItems(updatedItems);

      // Call calculation immediately with the updated items
      onCalculateTotal(updatedItems);
    }
  };

  // Update a line item field
  const updateLineItem = (
    id: string,
    field: keyof EnhancedLineItem,
    value: any
  ) => {
    const updatedItems = lineItems.map((item) => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };

        // Recalculate line totals when relevant fields change (unless manual pricing is enabled)
        if (
          [
            "quantity",
            "unitPrice",
            "discountType",
            "discountValue",
            "taxRate",
            "isManualPricing",
            "manualLineTotal",
          ].includes(field as string)
        ) {
          const quantity = safeParseNumber(updatedItem.quantity, 1);
          const unitPrice = safeParseNumber(updatedItem.unitPrice, 0);
          const discountValue = safeParseNumber(updatedItem.discountValue, 0);
          const taxRate = safeParseNumber(updatedItem.taxRate, 0);
          const isManualPricing = updatedItem.isManualPricing || false;
          const manualLineTotal = safeParseNumber(
            updatedItem.manualLineTotal,
            0
          );

          const subtotal = quantity * unitPrice;
          let discount = 0;
          let taxAmount = 0;

          // Calculate discount
          if (showDiscounts && discountValue > 0) {
            if (updatedItem.discountType === "PERCENTAGE") {
              discount = (subtotal * discountValue) / 100;
            } else {
              discount = discountValue;
            }
          }

          // Calculate tax on the amount after discount
          const amountAfterDiscount = subtotal - discount;
          if (showTaxRate && taxRate > 0) {
            taxAmount = (amountAfterDiscount * taxRate) / 100;
          }

          updatedItem.quantity = quantity;
          updatedItem.unitPrice = unitPrice;
          updatedItem.discountValue = discountValue;
          updatedItem.taxRate = taxRate;
          updatedItem.subtotal = subtotal;
          updatedItem.lineTotalBeforeDiscount = subtotal;
          updatedItem.discount = discount;

          // Use manual line total if manual pricing is enabled, otherwise use calculated total
          if (isManualPricing && manualLineTotal > 0) {
            updatedItem.lineTotal = manualLineTotal;
          } else {
            updatedItem.lineTotal = amountAfterDiscount + taxAmount;
          }
        }

        return updatedItem;
      }
      return item;
    });

    // Update state and calculate totals with the new items
    setLineItems(updatedItems);

    // Call calculation immediately with the updated items
    onCalculateTotal(updatedItems);
  };

  // Handle product selection
  const handleProductSelect = (id: string, product: any) => {
    if (product) {
      const updatedItems = lineItems.map((item) => {
        if (item.id === id) {
          const updatedItem = {
            ...item,
            productId: product.value, // Use product.value from dynamic select
            priceBookId: "", // Reset price book when product changes
            productSku: product.sku,
            productDescription: product.description,
            description: product.label, // Use product.label from dynamic select
            unitPrice: product.sellingPrice || product.price || 0, // Use sellingPrice first
            listPrice: product.sellingPrice || product.price || 0, // Use sellingPrice first
            unitId: product.unit?.id || "",
            unitName: product.unit?.name || "",
            unitDisplayName: product.unit?.displayName || "",
          };

          // Recalculate totals immediately
          const quantity = safeParseNumber(updatedItem.quantity, 1);
          const unitPrice = safeParseNumber(updatedItem.unitPrice, 0);
          const discountValue = safeParseNumber(updatedItem.discountValue, 0);
          const taxRate = safeParseNumber(updatedItem.taxRate, 0);

          const subtotal = quantity * unitPrice;
          let discount = 0;
          let taxAmount = 0;

          // Calculate discount
          if (showDiscounts && discountValue > 0) {
            if (updatedItem.discountType === "PERCENTAGE") {
              discount = (subtotal * discountValue) / 100;
            } else {
              discount = discountValue;
            }
          }

          // Calculate tax on the amount after discount
          const amountAfterDiscount = subtotal - discount;
          if (showTaxRate && taxRate > 0) {
            taxAmount = (amountAfterDiscount * taxRate) / 100;
          }

          updatedItem.subtotal = subtotal;
          updatedItem.lineTotalBeforeDiscount = subtotal;
          updatedItem.discount = discount;
          updatedItem.lineTotal = amountAfterDiscount + taxAmount;

          return updatedItem;
        }
        return item;
      });

      // Update state and calculate totals with the new items
      setLineItems(updatedItems);

      // Call calculation immediately with the updated items
      onCalculateTotal(updatedItems);
    } else {
      // Clear product selection
      updateLineItem(id, "productId", "");
      updateLineItem(id, "productSku", "");
      updateLineItem(id, "productDescription", "");
    }
  };

  // Handle unit selection
  const handleUnitSelect = (id: string, unitId: string, unit: any) => {
    updateLineItem(id, "unitId", unitId);
    updateLineItem(id, "unitName", unit?.name || "");
    updateLineItem(id, "unitDisplayName", unit?.displayName || "");
  };

  // Handle price book selection
  const handlePriceBookSelect = (id: string, priceBookData: any) => {
    if (priceBookData) {
      const updatedItems = lineItems.map((item) => {
        if (item.id === id) {
          const updatedItem = {
            ...item,
            priceBookId: priceBookData.value,
            unitPrice: priceBookData.entry?.basePrice || item.unitPrice,
            listPrice:
              priceBookData.entry?.listPrice ||
              priceBookData.entry?.basePrice ||
              item.listPrice,
          };

          // Recalculate totals immediately
          const quantity = safeParseNumber(updatedItem.quantity, 1);
          const unitPrice = safeParseNumber(updatedItem.unitPrice, 0);
          const discountValue = safeParseNumber(updatedItem.discountValue, 0);
          const taxRate = safeParseNumber(updatedItem.taxRate, 0);

          const subtotal = quantity * unitPrice;
          let discount = 0;
          let taxAmount = 0;

          // Calculate discount
          if (showDiscounts && discountValue > 0) {
            if (updatedItem.discountType === "PERCENTAGE") {
              discount = (subtotal * discountValue) / 100;
            } else {
              discount = discountValue;
            }
          }

          // Calculate tax on the amount after discount
          const amountAfterDiscount = subtotal - discount;
          if (showTaxRate && taxRate > 0) {
            taxAmount = (amountAfterDiscount * taxRate) / 100;
          }

          updatedItem.subtotal = subtotal;
          updatedItem.lineTotalBeforeDiscount = subtotal;
          updatedItem.discount = discount;
          updatedItem.lineTotal = amountAfterDiscount + taxAmount;

          return updatedItem;
        }
        return item;
      });

      // Update state and calculate totals with the new items
      setLineItems(updatedItems);
      onCalculateTotal(updatedItems);
    }
  };

  // Check if line item can be converted to product
  const canCreateProduct = (item: EnhancedLineItem): boolean => {
    return (
      item.description &&
      item.description.trim().length > 0 &&
      item.quantity > 0 &&
      item.unitPrice > 0 &&
      !item.productId
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <CardTitle>Package Line Items</CardTitle>
          <CardDescription className="mt-2">
            Products and services included in this package.
          </CardDescription>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addLineItem}
          className="h-9 px-4 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors shadow-sm"
        >
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Item
        </Button>
      </div>

      <div className="border border-gray-200 rounded-xl overflow-hidden shadow-sm bg-white">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow className="h-12 bg-gray-50/80 border-b border-gray-200">
              <TableHead className="min-w-[200px] px-4 py-3 text-left font-semibold text-gray-700 text-sm">
                Product/Service
              </TableHead>
              {showUnits && (
                <TableHead className="min-w-[80px] text-center px-3 py-3 font-semibold text-gray-700 text-sm">
                  Unit
                </TableHead>
              )}
              <TableHead className="min-w-[70px] text-center px-3 py-3 font-semibold text-gray-700 text-sm">
                Qty
              </TableHead>
              {showListPrice && (
                <TableHead className="min-w-[100px] text-right px-3 py-3 font-semibold text-gray-700 text-sm">
                  List Price
                </TableHead>
              )}
              <TableHead className="min-w-[100px] text-right px-3 py-3 font-semibold text-gray-700 text-sm">
                Unit Price
              </TableHead>
              {showDiscounts && (
                <TableHead className="min-w-[110px] text-right px-3 py-3 font-semibold text-gray-700 text-sm">
                  Discount
                </TableHead>
              )}
              {showTaxRate && (
                <TableHead className="min-w-[80px] text-right px-3 py-3 font-semibold text-gray-700 text-sm">
                  Tax %
                </TableHead>
              )}
              <TableHead className="min-w-[100px] text-right px-3 py-3 font-semibold text-gray-700 text-sm">
                Line Total
              </TableHead>
              <TableHead className="min-w-[60px] text-center px-3 py-3 font-semibold text-gray-700 text-sm">
                Manual
              </TableHead>
              <TableHead className="min-w-[80px] text-center px-3 py-3 font-semibold text-gray-700 text-sm">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lineItems.map((item, index) => (
              <TableRow
                key={item.id}
                className={`
                  border-b border-gray-100 hover:bg-gray-50/50 transition-colors duration-200
                  ${item.isPrePopulated ? "bg-blue-50/30 border-blue-200/50" : ""}
                  ${index % 2 === 0 ? "bg-white" : "bg-gray-50/20"}
                  h-16
                `}
              >
                <TableCell className="px-4 py-3">
                  <div className="space-y-2 min-w-[200px]">
                    <ProductPriceBookSelector
                      productValue={item.productId || ""}
                      priceBookValue={item.priceBookId || ""}
                      onProductChange={(value, product) => {
                        updateLineItem(
                          item.id,
                          "description",
                          product?.label || value
                        );
                        handleProductSelect(item.id, product);
                      }}
                      onPriceBookChange={(value, priceBookData) => {
                        handlePriceBookSelect(item.id, priceBookData);
                      }}
                      compact={true}
                    />
                    {item.isPrePopulated && (
                      <div className="flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-md w-fit">
                        <PackageIcon className="h-3 w-3" />
                        <span className="font-medium">From Opportunity</span>
                      </div>
                    )}
                  </div>
                </TableCell>

                {showUnits && (
                  <TableCell className="px-3 py-2">
                    <UnitSelector
                      value={item.unitId || ""}
                      onChange={(unitId, unit) =>
                        handleUnitSelect(item.id, unitId, unit)
                      }
                      placeholder="Unit"
                      className="w-full min-w-[70px] text-sm h-8"
                    />
                  </TableCell>
                )}

                <TableCell className="px-3 py-2">
                  <Input
                    type="number"
                    min="1"
                    step="1"
                    value={item.quantity}
                    onChange={(e) =>
                      updateLineItem(
                        item.id,
                        "quantity",
                        parseInt(e.target.value) || 1
                      )
                    }
                    className="text-center border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 w-full min-w-[60px] text-sm font-medium bg-white h-8"
                  />
                </TableCell>

                {showListPrice && (
                  <TableCell className="px-3 py-2">
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.listPrice || 0}
                      onChange={(e) =>
                        updateLineItem(
                          item.id,
                          "listPrice",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="text-right border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 w-full min-w-[90px] text-sm font-medium bg-white h-8"
                    />
                  </TableCell>
                )}

                <TableCell className="px-3 py-2">
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.unitPrice}
                    onChange={(e) =>
                      updateLineItem(
                        item.id,
                        "unitPrice",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    className="text-right border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 w-full min-w-[90px] text-sm font-medium bg-white h-8"
                  />
                </TableCell>

                {showDiscounts && (
                  <TableCell className="px-3 py-2">
                    <div className="flex gap-1 min-w-[100px]">
                      <Select
                        value={item.discountType || "PERCENTAGE"}
                        onValueChange={(value: DiscountType) =>
                          updateLineItem(item.id, "discountType", value)
                        }
                      >
                        <SelectTrigger className="w-12 h-8 text-xs border border-gray-200 rounded-lg shadow-sm">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="FLAT">$</SelectItem>
                          <SelectItem value="PERCENTAGE">%</SelectItem>
                        </SelectContent>
                      </Select>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        value={item.discountValue || 0}
                        onChange={(e) =>
                          updateLineItem(
                            item.id,
                            "discountValue",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="text-right border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 h-8 text-sm font-medium bg-white flex-1"
                      />
                    </div>
                  </TableCell>
                )}

                {showTaxRate && (
                  <TableCell className="px-3 py-2">
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={item.taxRate || 0}
                      onChange={(e) =>
                        updateLineItem(
                          item.id,
                          "taxRate",
                          safeParseNumber(e.target.value, 0)
                        )
                      }
                      onBlur={(e) => {
                        // Ensure empty fields are set to 0
                        if (e.target.value === "") {
                          updateLineItem(item.id, "taxRate", 0);
                        }
                      }}
                      className="text-right border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 w-full min-w-[70px] text-sm font-medium bg-white h-8"
                      placeholder="0.00"
                    />
                  </TableCell>
                )}

                <TableCell className="px-3 py-2">
                  {item.isManualPricing ? (
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.manualLineTotal || 0}
                      onChange={(e) =>
                        updateLineItem(
                          item.id,
                          "manualLineTotal",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="text-right border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 w-full min-w-[90px] text-sm font-medium bg-white h-8"
                      placeholder="0.00"
                    />
                  ) : (
                    <div className="text-right font-semibold text-sm min-w-[90px] text-gray-900 bg-gray-50 px-2 py-1 rounded-lg">
                      $
                      {(item.lineTotal || 0).toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </div>
                  )}
                </TableCell>

                <TableCell className="px-3 py-2">
                  <div className="flex justify-center">
                    <Checkbox
                      checked={item.isManualPricing || false}
                      onCheckedChange={(checked) => {
                        updateLineItem(item.id, "isManualPricing", checked);
                        if (checked) {
                          // Set manual line total to current calculated total when enabling manual mode
                          updateLineItem(
                            item.id,
                            "manualLineTotal",
                            item.lineTotal || 0
                          );
                        }
                      }}
                      className="h-3 w-3 border-2 border-gray-300 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                      title="Enable manual pricing override"
                    />
                  </div>
                </TableCell>

                <TableCell className="px-3 py-2">
                  <div className="flex items-center justify-center gap-1 min-w-[70px]">
                    {canCreateProduct(item) && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // TODO: Implement product creation from line item
                          toast.success(
                            "Product creation feature coming soon!"
                          );
                        }}
                        className="h-7 w-7 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Create product from this line item"
                      >
                        <PackageIcon className="h-3 w-3" />
                      </Button>
                    )}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLineItem(item.id)}
                      disabled={lineItems.length === 1}
                      className="h-7 w-7 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Remove line item"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {lineItems.length === 0 && (
        <div className="text-center py-12 bg-gray-50/50 rounded-xl border border-gray-200">
          <div className="max-w-sm mx-auto">
            <PackageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No line items yet
            </h3>
            <p className="text-sm text-gray-500 mb-6">
              Add products or services to get started with your line items.
            </p>
            <Button
              type="button"
              variant="default"
              onClick={addLineItem}
              className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
            >
              <PlusIcon className="mr-2 h-4 w-4" />
              Add First Item
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
