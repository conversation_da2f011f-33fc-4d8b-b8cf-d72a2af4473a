"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { Loader2Icon } from "lucide-react";
import toast from "react-hot-toast";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { Card } from "@flinkk/components/ui/card";

// Import dynamic form elements
import {
  TextFieldFormElement,
  TextAreaFieldFormElement,
  NumberFieldFormElement,
  SelectFieldFormElement,
  CheckboxFieldFormElement,
} from "@flinkk/dynamic-form/form-elements";

// Import section wrapper
import { SectionWrapper } from "@flinkk/dynamic-form/components";

// Import the enhanced line items table
import { EnhancedLineItemsTable } from "./enhanced-line-items-table";

// Import package types
import type { Package, DiscountType } from "../../../src/types/package";

// Simple Custom Fields Component (inline to avoid import issues)
interface SimpleCustomField {
  id: string;
  name: string;
  label: string;
  type: string;
  isRequired: boolean;
  value?: string | null;
  placeholder?: string | null;
  helpText?: string | null;
}

function SimpleCustomFieldsForm({
  customFields,
  onChange
}: {
  customFields: SimpleCustomField[];
  onChange: (fieldId: string, value: string | null) => void;
}) {
  if (!customFields || customFields.length === 0) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {customFields.map((field) => (
        <div key={field.id} className="space-y-2">
          <label htmlFor={`custom_${field.id}`} className="text-sm font-medium">
            {field.label}
            {field.isRequired && <span className="text-red-500">*</span>}
          </label>
          <input
            id={`custom_${field.id}`}
            type={field.type === 'NUMBER' ? 'number' : 'text'}
            value={field.value || ''}
            onChange={(e) => onChange(field.id, e.target.value)}
            placeholder={field.placeholder || ''}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
          {field.helpText && (
            <p className="text-xs text-muted-foreground">
              {field.helpText}
            </p>
          )}
        </div>
      ))}
    </div>
  );
}

// Package form schema
const packageFormSchema = z.object({
  name: z.string().min(1, "Package name is required"),
  description: z.string().optional(),
  sku: z.string().optional(),
  isActive: z.boolean().optional(),
  taxPercentage: z.number().min(0).max(100).optional(),
  preferredVendor: z.string().optional(),

  // Package-level discount
  discountType: z.string().optional(),
  discountValue: z.number().min(0).optional(),

  // Manual price override
  manualPriceOverride: z.boolean().optional(),
  manualPrice: z.number().min(0).optional(),

  // Calculated totals (read-only)
  subtotal: z.number().optional(),
  packageDiscount: z.number().optional(),
  totalAfterDiscount: z.number().optional(),
  totalTax: z.number().optional(),
  grandTotal: z.number().optional(),
});

type PackageFormValues = z.infer<typeof packageFormSchema>;

// Define CustomField interface locally (using SimpleCustomField)
type CustomField = SimpleCustomField;

interface PackageFormProps {
  mode: "create" | "edit";
  packageId?: string;
  initialData?: Package | null;
  customFields?: CustomField[];
}

export function PackageForm({
  mode,
  packageId,
  initialData,
  customFields = [],
}: PackageFormProps) {
  const router = useRouter();
  const [lineItems, setLineItems] = useState<any[]>([]);

  // Custom fields state management
  const [customFieldsState, setCustomFieldsState] = useState<CustomField[]>(
    customFields.map((field) => ({
      ...field,
      value: field.value || null,
    }))
  );

  // Debug log
  console.log("PackageForm received custom fields:", customFields.length, customFields);

  // Update custom fields state when customFields prop changes
  useEffect(() => {
    setCustomFieldsState(
      customFields.map((field) => ({
        ...field,
        value: field.value || null,
      }))
    );
  }, [customFields]);

  // Use the new useSaveFormData hook
  const { save: savePackage, isLoading: isSaving } = useSaveFormData({
    model: "package",
    onSuccess: () => {
      // Navigate back to packages list
      router.push("/packages");
    },
  });

  // Initialize form with default values
  const form = useForm<PackageFormValues>({
    resolver: zodResolver(packageFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      sku: initialData?.sku || "",
      isActive: initialData?.isActive ?? true,
      taxPercentage: initialData?.taxPercentage || 0,
      preferredVendor: initialData?.preferredVendor || "",
      discountType: initialData?.discountType || "PERCENTAGE",
      discountValue: initialData?.discountValue || 0,
      manualPriceOverride: initialData?.manualPriceOverride || false,
      manualPrice: initialData?.manualPrice || 0,
      subtotal: initialData?.subtotal || 0,
      packageDiscount: initialData?.packageDiscount || 0,
      totalAfterDiscount: initialData?.totalAfterDiscount || 0,
      totalTax: initialData?.totalTax || 0,
      grandTotal: initialData?.grandTotal || 0,
    },
  });

  // Initialize line items from initial data
  useEffect(() => {
    if (initialData?.lines) {
      const formattedLines = initialData.lines.map((line) => ({
        id: line.id,
        description: line.description,
        quantity: line.quantity,
        unitPrice: line.unitPrice,
        lineTotal: line.lineTotal,
        productId: line.productId || "",
        productSku: line.product?.sku || "",
        productDescription: line.product?.name || "",
        unitId: line.unitId || "",
        unitName: line.unit?.name || "",
        unitDisplayName: line.unit?.displayName || "",
        listPrice: line.listPrice || 0,
        discount: line.discount || 0,
        discountType: line.discountType || "PERCENTAGE",
        discountValue: line.discountValue || 0,
        lineTotalBeforeDiscount: line.lineTotalBeforeDiscount || 0,
      }));
      setLineItems(formattedLines);
    } else {
      // Add one empty line item for new packages
      setLineItems([
        {
          id: crypto.randomUUID(),
          description: "",
          quantity: 1,
          unitPrice: 0,
          lineTotal: 0,
          productId: "",
          discount: 0,
          discountType: "PERCENTAGE",
          discountValue: 0,
          lineTotalBeforeDiscount: 0,
        },
      ]);
    }
  }, [initialData]);

  // Calculate totals when line items or form values change
  const calculateTotals = React.useCallback(
    (updatedLineItems?: typeof lineItems) => {
      // Use provided line items or current state
      const itemsToCalculate = updatedLineItems || lineItems;
      const subtotal = itemsToCalculate.reduce(
        (sum, item) => sum + (item.lineTotal || 0),
        0
      );

      // Apply package-level discount
      const discountType = form.getValues("discountType") as DiscountType;
      const discountValue = form.getValues("discountValue") || 0;
      let packageDiscount = 0;

      if (discountValue > 0) {
        if (discountType === "PERCENTAGE") {
          packageDiscount = (subtotal * discountValue) / 100;
        } else {
          packageDiscount = discountValue;
        }
      }

      const totalAfterDiscount = subtotal - packageDiscount;
      const taxPercentage = form.getValues("taxPercentage") || 0;
      const totalTax = (totalAfterDiscount * taxPercentage) / 100;

      // Use manual price if override is enabled
      const manualPriceOverride = form.getValues("manualPriceOverride");
      const manualPrice = form.getValues("manualPrice") || 0;
      const grandTotal =
        manualPriceOverride && manualPrice > 0
          ? manualPrice
          : totalAfterDiscount + totalTax;

      form.setValue("subtotal", subtotal);
      form.setValue("packageDiscount", packageDiscount);
      form.setValue("totalAfterDiscount", totalAfterDiscount);
      form.setValue("totalTax", totalTax);
      form.setValue("grandTotal", grandTotal);
    },
    [lineItems, form]
  );

  // Watch for changes in discount and tax fields
  const discountType = form.watch("discountType");
  const discountValue = form.watch("discountValue");
  const taxPercentage = form.watch("taxPercentage");
  const manualPriceOverride = form.watch("manualPriceOverride");
  const manualPrice = form.watch("manualPrice");

  // Update totals when line items change
  React.useEffect(() => {
    calculateTotals();
  }, [lineItems, calculateTotals]);

  // Update totals when relevant fields change
  React.useEffect(() => {
    calculateTotals();
  }, [
    calculateTotals,
    discountType,
    discountValue,
    taxPercentage,
    manualPriceOverride,
    manualPrice,
  ]);

  // Additional effect to ensure calculations are triggered when line item totals change
  React.useEffect(() => {
    const totalLineTotal = lineItems.reduce(
      (sum, item) => sum + (item.lineTotal || 0),
      0
    );
    if (totalLineTotal !== form.getValues("subtotal")) {
      calculateTotals();
    }
  }, [
    lineItems.map((item) => item.lineTotal).join(","),
    calculateTotals,
    form,
  ]);

  // Force recalculation when any line item property changes
  React.useEffect(() => {
    calculateTotals();
  }, [
    lineItems
      .map(
        (item) =>
          `${item.id}-${item.quantity}-${item.unitPrice}-${item.lineTotal}`
      )
      .join(","),
    calculateTotals,
  ]);

  // Handle custom field changes
  const handleCustomFieldChange = (fieldId: string, value: string | null) => {
    setCustomFieldsState((prev) =>
      prev.map((field) => (field.id === fieldId ? { ...field, value } : field)),
    );
  };

  // Handle form submission using the new hook
  const onSubmit = async (data: PackageFormValues) => {
    if (lineItems.length === 0) {
      toast.error("Please add at least one line item");
      return;
    }

    // Prepare custom fields data for submission
    const customFieldsData = customFieldsState
      .filter((field) => field.value !== null && field.value !== "")
      .map((field) => ({
        id: field.id,
        value: field.value,
      }));

    // Prepare the data for submission
    const submissionData = {
      ...data,
      lines: lineItems.map((item) => ({
        productId: item.productId || null,
        priceBookId: item.priceBookId || null,
        description: item.description,
        quantity: item.quantity,
        unitId: item.unitId || null,
        unitPrice: item.unitPrice,
        listPrice: item.listPrice || null,
        discountType: item.discountType || "FLAT",
        discountValue: item.discountValue || 0,
      })),
      ...(customFieldsData.length > 0 && { customFields: customFieldsData }),
    };

    // Use the new save function from the hook
    await savePackage(packageId || "new", submissionData);
  };

  return (
    <div className="h-full flex flex-col">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Package Information Section */}
          <SectionWrapper
            title="Package Information"
            description="Basic details of the package."
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <TextFieldFormElement
                name="name"
                label="Package Name"
                control={form.control as any}
                placeholder="e.g. Complete Website Package"
                required
              />
              <TextFieldFormElement
                name="sku"
                label="SKU"
                control={form.control as any}
                placeholder="Auto-generated if empty"
              />
              <CheckboxFieldFormElement
                name="isActive"
                label="Active Package"
                control={form.control as any}
                helperText="Enable this package for use in opportunities and quotes"
              />
            </div>
            <div className="mt-6">
              <TextAreaFieldFormElement
                name="description"
                label="Description"
                control={form.control as any}
                placeholder="Describe what this package includes..."
              />
            </div>
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <TextFieldFormElement
                name="preferredVendor"
                label="Preferred Vendor"
                control={form.control as any}
                placeholder="Optional vendor/supplier name"
              />
            </div>
          </SectionWrapper>

          {/* Line Items Section */}
          <Card className="p-4"
          >
            <EnhancedLineItemsTable
              lineItems={lineItems}
              setLineItems={setLineItems}
              onCalculateTotal={calculateTotals}
              showUnits={true}
              showDiscounts={true}
              showListPrice={true}
              title="Package Line Items"
            />
          </Card>

          {/* Package Pricing Section */}
          <SectionWrapper
            title="Package Pricing"
            description="Pricing, discounts, and tax settings for the package."
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <SelectFieldFormElement
                name="discountType"
                label="Package Discount Type"
                control={form.control as any}
                options={[
                  { value: "FLAT", label: "Flat Amount ($)" },
                  { value: "PERCENTAGE", label: "Percentage (%)" },
                ]}
              />
              <NumberFieldFormElement
                name="discountValue"
                label="Discount Value"
                control={form.control as any}
                placeholder="0"
                min={0}
              />
              <NumberFieldFormElement
                name="taxPercentage"
                label="Tax Percentage (%)"
                control={form.control as any}
                placeholder="0"
                min={0}
                max={100}
              />
            </div>

            {/* Manual Price Override */}
            <div className="mt-6 p-4 border rounded-lg bg-muted/50">
              <div className="flex items-center space-x-2 mb-4">
                <input
                  type="checkbox"
                  id="manualPriceOverride"
                  checked={form.watch("manualPriceOverride")}
                  onChange={(e) =>
                    form.setValue("manualPriceOverride", e.target.checked)
                  }
                  className="rounded"
                />
                <label
                  htmlFor="manualPriceOverride"
                  className="text-sm font-medium"
                >
                  Override calculated price with manual price
                </label>
              </div>
              {form.watch("manualPriceOverride") && (
                <NumberFieldFormElement
                  name="manualPrice"
                  label="Manual Price"
                  control={form.control as any}
                  placeholder="Enter manual price"
                  min={0}
                />
              )}
            </div>

            {/* Pricing Summary */}
            <div className="mt-6 p-4 border rounded-lg bg-background">
              <h4 className="font-medium mb-4">Pricing Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${(form.watch("subtotal") || 0).toFixed(2)}</span>
                </div>
                {(form.watch("packageDiscount") || 0) > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Package Discount:</span>
                    <span>-${(form.watch("packageDiscount") || 0).toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Total After Discount:</span>
                  <span>${(form.watch("totalAfterDiscount") || 0).toFixed(2)}</span>
                </div>
                {(form.watch("totalTax") || 0) > 0 && (
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>${(form.watch("totalTax") || 0).toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between font-medium text-lg border-t pt-2">
                  <span>Grand Total:</span>
                  <span>${(form.watch("grandTotal") || 0).toFixed(2)}</span>
                </div>
                {(form.watch("manualPriceOverride") || false) && (
                  <div className="text-xs text-muted-foreground">
                    * Using manual price override
                  </div>
                )}
              </div>
            </div>
          </SectionWrapper>

          {/* Additional Information Section - Custom Fields */}
          {customFieldsState.length > 0 && (
            <SectionWrapper
              title="Additional Information"
              description="Custom fields for additional package details."
            >
              <SimpleCustomFieldsForm
                customFields={customFieldsState}
                onChange={handleCustomFieldChange}
              />
            </SectionWrapper>
          )}

        </form>
      </Form>

      {/* Fixed Form Actions */}
        <div className="flex justify-end space-x-4 my-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSaving}
            onClick={form.handleSubmit(onSubmit)}
          >
            {isSaving && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
            {mode === "create" ? "Create Package" : "Update Package"}
          </Button>
        </div>
    </div>
  );
}
