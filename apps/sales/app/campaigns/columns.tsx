"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { Campaign } from "@/types/campaign";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";

import { Checkbox } from "@flinkk/components/ui/checkbox";
import { DataTableColumnHeader } from "@flinkk/data-table/component/data-table-column-header";
import { Badge } from "@flinkk/components/ui/badge";
import { Button } from "@flinkk/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@flinkk/components/ui/dropdown-menu";
import { MoreHorizontal, Circle } from "lucide-react";

// Helper function to format date with better fallbacks
function formatDate(dateString: string | null | undefined): string {
  if (!dateString) {
    return "Not Set";
  }

  const date = new Date(dateString);

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return "Invalid Date";
  }

  // Format the date as "MMM DD, YYYY"
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
}

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface GetCampaignsTableColumnsProps {
  setRowAction?: (action: DataTableRowAction<Campaign> | null) => void;
  permissions?: ModelPermissions;
}

// Helper function to format currency with dynamic currency support
function formatCurrency(value: number | null | undefined, currency: string = "USD"): string {
  if (!value) {
    return "—";
  }

  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 0,
  }).format(value);
}

// Helper function to get status badge variant
function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "ACTIVE":
      return "default";
    case "DRAFT":
      return "secondary";
    case "PAUSED":
      return "outline";
    case "COMPLETED":
      return "default";
    case "CANCELLED":
      return "destructive";
    default:
      return "secondary";
  }
}

// Helper function to get type badge variant
function getTypeBadgeVariant(type: string) {
  switch (type) {
    case "EMAIL":
      return "default";
    case "SMS":
      return "secondary";
    case "SOCIAL_MEDIA":
      return "outline";
    case "DIRECT_MAIL":
      return "default";
    case "WEBINAR":
      return "secondary";
    case "EVENT":
      return "outline";
    default:
      return "secondary";
  }
}

export function getCampaignsTableColumns({
  setRowAction,
  permissions = {
    canView: true,
    canCreate: false,
    canEdit: false,
    canDelete: false,
  },
}: GetCampaignsTableColumnsProps): ColumnDef<Campaign>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-0.5 rounded"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-0.5 rounded"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 40,
    },
    {
      id: "name",
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Campaign Name" />
      ),
      cell: ({ row }) => (
        <div className="font-medium w-[160px]">{row.getValue("name")}</div>
      ),
      enableSorting: true,
      enableHiding: false,
      meta: {
        label: "Campaign Name",
        variant: "text",
      },
      enableColumnFilter: true,
    },
    {
      id: "type",
      accessorKey: "type",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Type" />
      ),
      cell: ({ row }) => {
        const type = row.getValue("type") as string;
        return (
          <Badge variant={getTypeBadgeVariant(type)}>
            {type.replace("_", " ")}
          </Badge>
        );
      },
    },
    {
      id: "status",
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const isActive = status === "ACTIVE";
        const isPaused = status === "PAUSED";
        const isCompleted = status === "COMPLETED";

        return (
          <div className="flex items-center gap-2">
            {isActive && (
              <Circle className="h-2 w-2 fill-green-500 text-green-500" />
            )}
            {isPaused && (
              <Circle className="h-2 w-2 fill-yellow-500 text-yellow-500" />
            )}
            {isCompleted && (
              <Circle className="h-2 w-2 fill-blue-500 text-blue-500" />
            )}
            <Badge variant={getStatusBadgeVariant(status)}>{status}</Badge>
          </div>
        );
      },
    },

    {
      id: "budget",
      accessorKey: "budget",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Budget" />
      ),
      cell: ({ row }) => {
        const budget = row.getValue("budget") as number;
        const currency = (row.original as any).currency || "USD";
        return <div>{formatCurrency(budget, currency)}</div>;
      },
    },
    {
      id: "startDate",
      accessorKey: "startDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Start Date" />
      ),
      cell: ({ row }) => {
        const startDate = row.getValue("startDate") as string;
        return <div className="text-sm w-[120px]">{formatDate(startDate)}</div>;
      },
    },
    {
      id: "endDate",
      accessorKey: "endDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="End Date" />
      ),
      cell: ({ row }) => {
        const endDate = row.getValue("endDate") as string;
        return <div className="text-sm w-[120px]">{formatDate(endDate)}</div>;
      },
    },
    {
      id: "targetAudience",
      accessorKey: "targetAudience",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Target Audience" />
      ),
      cell: ({ row }) => {
        const targetAudience = row.getValue("targetAudience") as string;
        return (
          <div className="text-sm w-[200px] truncate">
            {targetAudience || "Not specified"}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      id: "description",
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      cell: ({ row }) => {
        const description = row.getValue("description") as string;
        return (
          <div className="text-sm w-[250px] truncate">
            {description || "No description"}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      id: "createdAt",
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const createdAt = row.getValue("createdAt") as string;
        return <div>{formatDate(createdAt)}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const campaign = row.original;

        // Don't show actions column if user has no view, edit or delete permissions
        if (!permissions.canView && !permissions.canEdit && !permissions.canDelete) {
          return null;
        }

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {permissions.canView && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    setRowAction?.({
                      type: "view",
                      row: row.original,
                    } as any);
                  }}
                >
                  View
                </DropdownMenuItem>
              )}
              {permissions.canEdit && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    setRowAction?.({
                      type: "edit",
                      row: row.original,
                    } as any);
                  }}
                >
                  Edit
                </DropdownMenuItem>
              )}
              {permissions.canDelete && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    setRowAction?.({
                      type: "delete",
                      row: row.original,
                    } as any);
                  }}
                >
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 40,
    },
  ];
}
