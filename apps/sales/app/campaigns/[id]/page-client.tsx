"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { Loader2Icon } from "lucide-react";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { useFormCancellation } from "@flinkk/hooks/form";
import useFormPersist from "react-hook-form-persist";

// Import our dynamic form elements
import {
  TextFieldFormElement,
  TextAreaFieldFormElement,
  SelectFieldFormElement,
  NumberFieldFormElement,
  DateFieldFormElement,
} from "@flinkk/dynamic-form/form-elements";

// Import components
import { SectionWrapper } from "@flinkk/dynamic-form/components";

// Define the form schema with enhanced validation
const campaignFormSchema = z.object({
  name: z.string().min(1, "Campaign name is required"),
  type: z.enum([
    "EMAIL",
    "SMS",
    "SOCIAL_MEDIA",
    "DIRECT_MAIL",
    "WEBINAR",
    "EVENT",
  ]),
  description: z.string().optional(),
  status: z.enum(["DRAFT", "SCHEDULED", "ACTIVE", "PAUSED", "COMPLETED", "CANCELLED"]).default("DRAFT"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  budget: z.number().min(0, "Budget must be positive").optional(),
  currency: z.string().default("USD"),
  targetAudience: z.string().optional(),
  // userId: z.string().optional(), // Campaign owner - temporarily disabled
}).refine((data) => {
  // Enhanced validation: End date must be after start date if both are provided
  if (data.startDate && data.endDate) {
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);
    return endDate >= startDate;
  }
  return true;
}, {
  message: "End date must be after start date",
  path: ["endDate"],
});

type CampaignFormValues = z.infer<typeof campaignFormSchema>;

interface NewCampaignFormProps {
  id?: string;
  initialData?: any;
  isEditMode?: boolean;
}

export function NewCampaignForm({ id, initialData, isEditMode: explicitEditMode }: NewCampaignFormProps) {
  const router = useRouter();
  const isEditMode = explicitEditMode || (id !== "new" && initialData);

  // Storage key for form persistence
  const storageKey = `campaign-form-${id}`;
  const shouldPersist = id === "new";

  // Use the standardized useSaveFormData hook
  const { save: saveCampaign, isLoading: isSaving } = useSaveFormData({
    model: "campaign",
    clearFormPersistence: shouldPersist
      ? () => {
          if (typeof window !== "undefined") {
            sessionStorage.removeItem(storageKey);
          }
        }
      : undefined,
    onSuccess: () => {
      // Navigate back to campaigns list
      router.push("/campaigns");
    },
  });

  // Initialize React Hook Form with Zod validation
  const form = useForm<CampaignFormValues>({
    resolver: zodResolver(campaignFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      type: initialData?.type || "EMAIL",
      description: initialData?.description || "",
      status: initialData?.status || "DRAFT",
      startDate: initialData?.startDate
        ? new Date(initialData.startDate).toISOString().split("T")[0]
        : new Date().toISOString().split("T")[0], // Default to today
      endDate: initialData?.endDate
        ? new Date(initialData.endDate).toISOString().split("T")[0]
        : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // Default to 7 days from now
      budget: initialData?.budget || undefined,
      currency: initialData?.currency || "USD",
      targetAudience: initialData?.targetAudience || "",
      // userId: initialData?.userId || "", // temporarily disabled
    },
  });

  // Set up form persistence for new campaigns
  useFormPersist(storageKey, {
    watch: form.watch,
    setValue: form.setValue,
    storage:
      shouldPersist && typeof window !== "undefined"
        ? window.sessionStorage
        : undefined,
  });

  // Handle form cancellation with navigation cleanup
  const { handleCancel } = useFormCancellation({
    recordId: id,
    redirectPath: "/campaigns",
    clearFormPersistence: shouldPersist
      ? () => {
          if (typeof window !== "undefined") {
            sessionStorage.removeItem(storageKey);
          }
        }
      : undefined,
  });

  // Handle form submission using the new hook
  const onSubmit = async (data: CampaignFormValues) => {
    // Prepare the data for submission
    const submissionData = {
      ...data,
      startDate: data.startDate ? new Date(data.startDate).toISOString() : null,
      endDate: data.endDate ? new Date(data.endDate).toISOString() : null,
    };

    // Use the new save function from the hook
    await saveCampaign(id, submissionData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">

        {/* General Information Section */}
        <SectionWrapper
          title="General Information"
          description="Basic campaign details and configuration"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <TextFieldFormElement
              control={form.control}
              name="name"
              label="Campaign Name"
              required={true}
              placeholder="Summer Sale Campaign"
            />

            <SelectFieldFormElement
              control={form.control}
              name="type"
              label="Campaign Type"
              required={true}
              options={[
                { label: "Email", value: "EMAIL" },
                { label: "SMS", value: "SMS" },
                { label: "Social Media", value: "SOCIAL_MEDIA" },
                { label: "Direct Mail", value: "DIRECT_MAIL" },
                { label: "Webinar", value: "WEBINAR" },
                { label: "Event", value: "EVENT" },
              ]}
            />

            <SelectFieldFormElement
              control={form.control}
              name="status"
              label="Campaign Status"
              required={true}
              options={[
                { label: "Draft", value: "DRAFT" },
                { label: "Scheduled", value: "SCHEDULED" },
                { label: "Active", value: "ACTIVE" },
                { label: "Paused", value: "PAUSED" },
                { label: "Completed", value: "COMPLETED" },
                { label: "Cancelled", value: "CANCELLED" },
              ]}
            />

            {/* Temporarily disabled - Campaign Owner field
            <DynamicSelectFieldFormElement
              control={form.control}
              name="userId"
              label="Campaign Owner"
              required={false}
              placeholder="Select campaign owner"
              dynamicKey="tenant-members"
            />
            */}
          </div>

          <div className="grid grid-cols-1 gap-4 mt-4">
            <TextAreaFieldFormElement
              control={form.control}
              name="description"
              label="Campaign Description"
              required={false}
              placeholder="Briefly describe the campaign's purpose, objectives, and strategy..."
            />
          </div>
        </SectionWrapper>

        {/* Scheduling Section */}
        <SectionWrapper
          title="Scheduling"
          description="Campaign timeline and duration settings"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <DateFieldFormElement
              control={form.control}
              name="startDate"
              label="Start Date"
              required={true}
              placeholder="Select start date"
            />

            <DateFieldFormElement
              control={form.control}
              name="endDate"
              label="End Date"
              required={true}
              placeholder="Select end date"
            />
          </div>
        </SectionWrapper>

        {/* Budget & Performance Section */}
        <SectionWrapper
          title="Budget & Performance"
          description="Financial planning and performance tracking"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <NumberFieldFormElement
              control={form.control}
              name="budget"
              label="Campaign Budget"
              required={false}
              placeholder="10000.00"
            />

            <SelectFieldFormElement
              control={form.control}
              name="currency"
              label="Currency"
              required={true}
              options={[
                { label: "USD ($)", value: "USD" },
                { label: "EUR (€)", value: "EUR" },
                { label: "GBP (£)", value: "GBP" },
                { label: "CAD (C$)", value: "CAD" },
                { label: "AUD (A$)", value: "AUD" },
                { label: "JPY (¥)", value: "JPY" },
              ]}
            />
          </div>
        </SectionWrapper>

        {/* Targeting Section */}
        <SectionWrapper
          title="Targeting"
          description="Define your campaign audience and targeting criteria"
        >
          <div className="grid grid-cols-1 gap-4">
            <TextFieldFormElement
              control={form.control}
              name="targetAudience"
              label="Target Audience"
              required={false}
              placeholder="e.g., Small to medium businesses in technology sector, Enterprise customers, Retail leads"
            />
          </div>
        </SectionWrapper>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSaving}
          >
            Cancel
          </Button>

          {/* Save & Activate Button - only show for draft campaigns */}
          {form.watch("status") === "DRAFT" && (
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                // Set status to ACTIVE and submit
                form.setValue("status", "ACTIVE");
                form.handleSubmit(onSubmit)();
              }}
              disabled={isSaving}
            >
              {isSaving && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
              Save & Activate
            </Button>
          )}

          <Button type="submit" disabled={isSaving}>
            {isSaving && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
            {isEditMode ? "Update Campaign" : "Save Campaign"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
