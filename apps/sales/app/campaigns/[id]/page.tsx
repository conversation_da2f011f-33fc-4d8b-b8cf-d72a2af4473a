import { NewCampaignForm } from "./page-client";
import { getCampaignById } from "./actions";
import { validateAction } from "@flinkk/shared-rbac";

export async function generateMetadata(props: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ title?: string }>;
}) {
  const params = await props.params;
  const searchParams = await props.searchParams;

  // If title is provided in search params, use it (for edit mode)
  if (searchParams.title) {
    return {
      title: searchParams.title,
      description: `Campaign management`,
    };
  }

  const { id } = params;
  const isNewRecord = id === "new";

  if (isNewRecord) {
    return {
      title: "New Campaign",
      description: "Create a new campaign",
    };
  }

  try {
    const campaignData = await getCampaignById(id);
    return {
      title: campaignData?.name || "Campaign Details",
      description: `View campaign: ${campaignData?.name || "Unnamed Campaign"}`,
    };
  } catch (error) {
    return {
      title: "Campaign Details",
      description: "View campaign details",
    };
  }
}

export default async function CampaignDetailPage(props: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ title?: string }>;
}) {
  const params = await props.params;
  const searchParams = await props.searchParams;
  const { id } = params;
  const isNewRecord = id === "new";

  // Extract mode from search params
  const mode =
    typeof searchParams.mode === "string"
      ? searchParams.mode
      : "edit"; // Default to edit mode

  let initialData;

  if (!isNewRecord) {
    initialData = await getCampaignById(id);
  }

  // Validate permissions based on the operation and mode
  if (isNewRecord) {
    // Validate create permission for new campaigns
    await validateAction(
      "campaign",
      "create",
      "You do not have permission to create campaigns.",
    );
  } else {
    // Validate appropriate permission based on mode
    if (mode === "view") {
      await validateAction(
        "campaign",
        "read",
        "You do not have permission to view campaign details.",
      );
    } else {
      await validateAction(
        "campaign",
        "update",
        "You do not have permission to edit campaign details.",
      );
    }
  }

  return (
    <NewCampaignForm
      id={id}
      initialData={initialData}
      isEditMode={searchParams.title?.startsWith('Edit')}
    />
  );
}
