import { getActivityByEntity } from "@flinkk/shared-slots-activity/page-actions";
import { PageClient } from "@flinkk/shared-slots-activity/page-client";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { getContactById } from "../../actions";
import { QuotesSection } from "../_components/quotes-section";
import { OpportunitiesSection } from "../_components/opportunities-section";


export default async function ContactActivityPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;

  const { tenantId, userId } = await getServerSession();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const contactId = params.id;
  const [activityLogs, contact] = await Promise.all([
    getActivityByEntity("CONTACT", contactId, tenantId),
    getContactById(contactId),
  ]);

  if (!contact) {
    throw new Error("Contact not found");
  }

  const contactName = `${contact.firstName} ${contact.lastName}`.trim() || "Unnamed Contact";

  return (
    <div className="space-y-4">
      {/* Quotes Section */}
      <QuotesSection contactId={contactId} contactName={contactName} />

      {/* Opportunities Section */}
      <OpportunitiesSection contactId={contactId} contactName={contactName} />

      {/* Activity Log */}
      <PageClient
        entityType="Contact"
        entityId={contactId}
        activityLogs={activityLogs}
        userId={userId}
      />
    </div>
  );
}
