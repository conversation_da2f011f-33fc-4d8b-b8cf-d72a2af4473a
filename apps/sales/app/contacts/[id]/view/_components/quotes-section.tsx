"use client";

import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@flinkk/components/ui/card";
import { Typography } from "@flinkk/components/ui/typography";
import { Badge } from "@flinkk/components/ui/badge";
import { PlusIcon, FileTextIcon, ExternalLinkIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Quote {
  id: string;
  quoteNumber: string;
  name: string;
  description?: string;
  status: string;
  version: number;
  subtotal: number;
  totalDiscount: number;
  totalTax: number;
  grandTotal: number;
  currency: string;
  validUntil?: string;
  createdAt: string;
  updatedAt: string;
  opportunity?: {
    id: string;
    dealName: string;
  };
  account?: {
    id: string;
    name: string;
  };
}

interface QuotesSectionProps {
  contactId: string;
  contactName: string;
}

export const QuotesSection = ({ contactId, contactName }: QuotesSectionProps) => {
  const router = useRouter();
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch quotes for this contact
  useEffect(() => {
    const fetchQuotes = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/contacts/${contactId}/quotes`);

        if (!response.ok) {
          throw new Error('Failed to fetch quotes');
        }

        const data = await response.json();
        setQuotes(data.data || []);
      } catch (err) {
        console.error('Error fetching quotes:', err);
        setError('Failed to load quotes');
      } finally {
        setLoading(false);
      }
    };

    if (contactId) {
      fetchQuotes();
    }
  }, [contactId]);

  const handleCreateQuote = () => {
    // Navigate to quotation creation with contact pre-filled
    router.push(`/quotes/new?contactId=${contactId}&title=${encodeURIComponent(`New Quote for ${contactName}`)}`);
  };

  const handleViewQuote = (quoteId: string) => {
    router.push(`/quotes/${quoteId}/view?title=${encodeURIComponent(contactName)}`);
  };

  const formatCurrency = (value: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(value);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Typography type="h3" className="font-semibold">
              Quotes
            </Typography>
            <Button
              onClick={handleCreateQuote}
              size="sm"
              className="gap-2 bg-teal-600 hover:bg-teal-700 text-white"
            >
              <PlusIcon className="h-4 w-4" />
              Create Quote
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Typography type="p" className="text-sm text-gray-500">
              Loading quotes...
            </Typography>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Typography type="h3" className="font-semibold">
              Quotes
            </Typography>
            <Button
              onClick={handleCreateQuote}
              size="sm"
              className="gap-2 bg-teal-600 hover:bg-teal-700 text-white"
            >
              <PlusIcon className="h-4 w-4" />
              Create Quote
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Typography type="p" className="text-sm text-red-500">
              {error}
            </Typography>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-fit">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <Typography type="h3" className="font-semibold">
            Quotes ({quotes.length})
          </Typography>
          <Button
            onClick={handleCreateQuote}
            size="sm"
            className="gap-2 bg-teal-600 hover:bg-teal-700 text-white"
          >
            <PlusIcon className="h-4 w-4" />
            Create Quote
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {quotes.length === 0 ? (
          /* Empty state */
          <div className="flex flex-col items-center justify-center p-8 space-y-4 border-2 border-dashed border-gray-200 rounded-lg">
            <div className="p-3 bg-gray-100 rounded-lg">
              <FileTextIcon className="h-8 w-8 text-gray-400" />
            </div>
            <div className="text-center space-y-2">
              <Typography type="h4" className="font-medium text-gray-900">
                No quotes created yet
              </Typography>
              <Typography type="p" className="text-sm text-gray-500 max-w-sm">
                Create your first quote for this contact to get started with the sales process.
              </Typography>
            </div>
          </div>
        ) : (
          /* Quotes list */
          <div className="space-y-3">
            {quotes.map((quote) => (
              <div
                key={quote.id}
                className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors cursor-pointer"
                onClick={() => handleViewQuote(quote.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Typography type="h4" className="font-medium text-gray-900">
                        {quote.name}
                      </Typography>
                      <Badge className={getStatusColor(quote.status)}>
                        {quote.status}
                      </Badge>
                    </div>

                    <div className="flex items-center gap-2 mb-2">
                      <Typography type="p" className="text-sm text-gray-600">
                        Quote #{quote.quoteNumber}
                      </Typography>
                      <span className="text-gray-400">•</span>
                      <Typography type="p" className="text-sm text-gray-600">
                        Version {quote.version}
                      </Typography>
                    </div>

                    {quote.description && (
                      <Typography type="p" className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {quote.description}
                      </Typography>
                    )}

                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>Total: {formatCurrency(quote.grandTotal, quote.currency)}</span>
                      {quote.validUntil && (
                        <span>Valid until: {new Date(quote.validUntil).toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>

                  <ExternalLinkIcon className="h-4 w-4 text-gray-400 ml-2 flex-shrink-0" />
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
