"use client";


import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { Typography } from "@flinkk/components/ui/typography";
import { Card, CardContent } from "@flinkk/components/ui/card";
import {
  BoringAvatar,
  avatarPresets,
} from "@flinkk/components/ui/boring-avatar";
import {
  EditIcon,
  BuildingIcon,
  MailIcon,
  PhoneIcon,
} from "lucide-react";
import Link from "next/link";

interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email?: string | null;
  phoneNumber?: string | null;
  role?: string | null;
  accountId?: string | null;
  account?: {
    id: string;
    name?: string | null;
  } | null;
  createdAt: string;
  updatedAt: string;
  userId: string;
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  } | null;
}

interface HeaderProps {
  contact: Contact;
  contactId: string;
}

export const Header = ({ contact, contactId }: HeaderProps) => {
  // Get contact full name
  const getContactFullName = () => {
    if (contact.firstName && contact.lastName) {
      return `${contact.firstName} ${contact.lastName}`;
    } else if (contact.firstName) {
      return contact.firstName;
    } else if (contact.lastName) {
      return contact.lastName;
    } else if (contact.email) {
      return contact.email;
    }
    return "Unnamed Contact";
  };

  return (
    <div className="sticky top-0 z-10">
      <Card className="border-0 shadow-none bg-transparent p-0">
        <CardContent className="p-0">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            {/* Left Side - Contact Information Card */}
            <div className="flex items-center gap-6">
              {/* Contact Avatar */}
              <BoringAvatar
                name={
                  contact.email ||
                  `${contact.firstName} ${contact.lastName}` ||
                  `contact-${contact.id}`
                }
                size="xl"
                {...avatarPresets.profile}
                border="primary"
                shadow="lg"
              />

              {/* Contact Details */}
              <div className="min-w-0 flex-1 space-y-2">
                <div className="flex items-center gap-3 flex-wrap">
                  <Typography
                    type="h1"
                    className="font-bold tracking-tight text-lg lg:text-xl truncate"
                  >
                    {getContactFullName()}
                  </Typography>
                </div>

                {/* Quick Contact Info */}
                <div className="flex items-center gap-4 text-muted-foreground">
                  {contact.account?.name && (
                    <div className="flex items-center gap-1.5">
                      <BuildingIcon className="h-3.5 w-3.5 text-muted-foreground" />
                      <span className="text-xs truncate max-w-[180px]">
                        {contact.account.name}
                      </span>
                    </div>
                  )}
                  {contact.email && (
                    <div className="flex items-center gap-1.5">
                      <MailIcon className="h-3.5 w-3.5 text-muted-foreground" />
                      <span className="text-xs truncate max-w-[180px]">
                        {contact.email}
                      </span>
                    </div>
                  )}
                  {contact.phoneNumber && (
                    <div className="flex items-center gap-1.5">
                      <PhoneIcon className="h-3.5 w-3.5 text-muted-foreground" />
                      <span className="text-xs">{contact.phoneNumber}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Side - Action Buttons */}
            <div className="flex flex-wrap gap-3 shrink-0">
              {/* Edit Contact Button */}
              <Button
                variant="outline"
                size="default"
                asChild
                className="gap-2"
              >
                <Link href={`/contacts/${contactId}?title=${encodeURIComponent(`Edit ${getContactFullName()}`)}`}>
                  <EditIcon className="h-4 w-4" />
                  Edit Contact
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
