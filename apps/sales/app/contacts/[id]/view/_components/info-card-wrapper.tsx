"use client";

import {
  <PERSON>,
  <PERSON><PERSON>eader,
  CardContent,
} from "@flinkk/components/ui";
import { Typography } from "@flinkk/components/ui/typography";
import { Button } from "@flinkk/components/ui/button";
import { EditIcon } from "lucide-react";
import { FormFields } from "./form-fields";
import { useState } from "react";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { useRouter } from "next/navigation";

interface InfoCardWrapperProps {
  title: string;
  children?: React.ReactNode;
  contactId?: string;
  gridColumns?: 1 | 2 | 3;
  formFields?: Array<{
    name: string;
    label: string;
    type:
      | "text"
      | "email"
      | "phone"
      | "url"
      | "textarea"
      | "select"
      | "dynamic-select";
    value: string | null | undefined;
    displayValue?: string | null | undefined;
    icon?: string;
    required?: boolean;
    placeholder?: string;
    options?: Array<{ value: string; label: string }>;
    dynamicKey?: string;
    showEmpty?: boolean;
  }>;
}

export const InfoCardWrapper = ({
  title,
  children,
  formFields,
  contactId,
  gridColumns = 1,
}: InfoCardWrapperProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const router = useRouter();

  // Use the useSaveFormData hook for updating contact data
  const { save: updateContact, isLoading: isSaving } = useSaveFormData({
    model: "contact",
    successMessage: "Contact updated successfully",
    errorMessage: "Failed to update contact. Please try again.",
    onSuccess: () => {
      setIsEditing(false);
      router.refresh();
    },
  });

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async (data: Record<string, any>) => {
    if (contactId) {
      try {
        await updateContact(contactId, data);
      } catch (error) {
        console.error("Error updating contact:", error);
      }
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  return (
    <Card className="shadow-sm group gap-0 p-3 py-2">
      <CardHeader className="pb-0 px-0 flex items-center justify-between">
        <Typography type="h4">{title}</Typography>
        {formFields && formFields.length > 0 && !isEditing && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            disabled={isEditing || isSaving}
            className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <EditIcon className="h-4 w-4" />
          </Button>
        )}
      </CardHeader>
      <CardContent className="py-0 space-y-1 p-0">
        {formFields ? (
          <FormFields
            fields={formFields}
            isEdit={isEditing}
            onSave={handleSave}
            onCancel={handleCancel}
            isLoading={isSaving}
            gridColumns={gridColumns}
          />
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
};
