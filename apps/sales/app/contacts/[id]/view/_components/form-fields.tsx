"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { Typography } from "@flinkk/components/ui/typography";
import { SaveIcon, XIcon } from "lucide-react";
import {
  TextFieldFormElement,
  TextAreaFieldFormElement,
  EmailFieldFormElement,
  SelectFieldFormElement,
  PhoneFieldFormElement,
  URLFieldFormElement,
  DynamicSelectFieldFormElement,
} from "@flinkk/dynamic-form/form-elements";

interface FormFieldConfig {
  name: string;
  label: string;
  type:
    | "text"
    | "email"
    | "phone"
    | "url"
    | "textarea"
    | "select"
    | "dynamic-select";
  value: string | null | undefined;
  displayValue?: string | null | undefined;
  icon?: string;
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  dynamicKey?: string;
  showEmpty?: boolean;
}

interface FormFieldsProps {
  fields: FormFieldConfig[];
  isEdit?: boolean;
  onSave?: (data: Record<string, any>) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
  gridColumns?: 1 | 2 | 3;
}

// Create a dynamic schema based on the fields
const createFormSchema = (fields: FormFieldConfig[]) => {
  const schemaObject: Record<string, any> = {};

  fields.forEach((field) => {
    let fieldSchema: any = z.string();

    if (field.type === "email") {
      fieldSchema = z.string().email("Invalid email address");
    } else if (field.type === "url") {
      fieldSchema = z.string().url("Invalid URL").or(z.literal(""));
    } else if (field.type === "dynamic-select") {
      fieldSchema = z.string();
    }

    if (!field.required) {
      fieldSchema = fieldSchema.optional();
    }

    schemaObject[field.name] = fieldSchema;
  });

  return z.object(schemaObject);
};

export const FormFields: React.FC<FormFieldsProps> = ({
  fields,
  isEdit = false,
  onSave,
  onCancel,
  isLoading = false,
  className = "",
  gridColumns = 1,
}) => {
  // Create form schema and default values
  const formSchema = createFormSchema(fields);
  const defaultValues = fields.reduce(
    (acc, field) => {
      acc[field.name] = field.value || "";
      return acc;
    },
    {} as Record<string, any>,
  );

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues,
  });



  const handleCancel = () => {
    form.reset(defaultValues);
    if (onCancel) {
      onCancel();
    }
  };

  const handleSave = async (data: Record<string, any>) => {
    if (!onSave) return;

    try {
      await onSave(data);
    } catch (error) {
      console.error("Error saving data:", error);
    }
  };

  const renderFormField = (field: FormFieldConfig) => {
    const commonProps = {
      control: form.control,
      name: field.name,
      label: field.label,
      placeholder: field.placeholder || `Enter ${field.label.toLowerCase()}`,
      required: field.required,
    };

    switch (field.type) {
      case "email":
        return <EmailFieldFormElement {...commonProps} />;
      case "phone":
        return <PhoneFieldFormElement {...commonProps} />;
      case "url":
        return <URLFieldFormElement {...commonProps} />;
      case "textarea":
        return <TextAreaFieldFormElement {...commonProps} rows={3} />;
      case "select":
        return (
          <SelectFieldFormElement
            {...commonProps}
            options={field.options || []}
          />
        );
      case "dynamic-select":
        return (
          <DynamicSelectFieldFormElement
            {...commonProps}
            dynamicKey={field.dynamicKey || ""}
            allowNone={true}
          />
        );
      default:
        return <TextFieldFormElement {...commonProps} />;
    }
  };

  const renderDisplayField = (field: FormFieldConfig) => {
    const displayValue = field.displayValue || field.value;

    if (!displayValue) return null;

    // For multi-column layouts, use a different structure
    if (gridColumns > 1) {
      return (
        <div className="flex flex-col space-y-1 p-3 bg-muted/30 rounded-md">
          <Typography
            type="label"
            className="text-muted-foreground text-xs font-medium"
          >
            {field.label}
          </Typography>
          <Typography
            type="label"
            className={`text-sm ${
              field.value ? "font-medium" : "text-muted-foreground italic"
            }`}
          >
            {displayValue}
          </Typography>
        </div>
      );
    }

    // Single column layout (original design)
    return (
      <div className="flex items-center justify-between py-2 border-b border-border/50 last:border-b-0">
        <div className="flex items-center gap-2 mr-2">
          <Typography type="label" className="text-muted-foreground text-sm">
            {field.label}
          </Typography>
        </div>
        <Typography
          type="label"
          className={`text-sm max-w-[200px] truncate ${
            field.value ? "font-medium" : "text-muted-foreground italic"
          }`}
        >
          {displayValue}
        </Typography>
      </div>
    );
  };

  // Helper function to get grid classes based on gridColumns
  const getGridClasses = () => {
    switch (gridColumns) {
      case 2:
        return "grid grid-cols-1 md:grid-cols-2 gap-3";
      case 3:
        return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3";
      default:
        return "space-y-1";
    }
  };

  if (isEdit) {
    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSave)} className={className}>
          <div className={getGridClasses()}>
            {fields.map((field) => (
              <div key={field.name}>
                {renderFormField(field)}
              </div>
            ))}
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              <XIcon className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <SaveIcon className="h-4 w-4 mr-2" />
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </div>
        </form>
      </Form>
    );
  }

  // Display mode
  return (
    <div className={`${getGridClasses()} ${className}`}>
      {fields.map((field) => {
        const shouldShow = field.showEmpty || field.displayValue || field.value;
        if (!shouldShow) return null;

        return (
          <div key={field.name}>
            {renderDisplayField(field)}
          </div>
        );
      })}
    </div>
  );
};
