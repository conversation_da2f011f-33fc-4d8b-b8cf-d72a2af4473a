import { getContactById } from "../actions";
import { Header } from "./_components/header";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@flinkk/components/ui/resizable";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@flinkk/components/ui/tabs";
import { CopilotKit } from "@copilotkit/react-core";
import { CollapsibleLeadScreen } from "@flinkk/copilot-wrapper";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { CustomSession } from "@flinkk/shared-auth/options";

export default async function FormPageLayout({
  children,
  tasks,
  activity,
  notes,
  email,
  whatsapp,
  params,
}: {
  children: React.ReactNode;
  tasks: React.ReactNode;
  activity: React.ReactNode;
  notes: React.ReactNode;
  email: React.ReactNode;
  whatsapp: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const contactDetails = await getContactById(id);

  if (!contactDetails) {
    throw new Error("Contact not found");
  }

  const session = (await getServerSession()) as CustomSession;

  return (
    <CopilotKit runtimeUrl="/api/copilot/contact-agent">
      <div className="w-full h-svh flex overflow-hidden">
        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden px-4">
          {/* Fixed Header Section */}
          <div className="flex-shrink-0 mt-4 space-y-4">
            <Header contact={contactDetails} contactId={id} />
          </div>

          <div className="flex-1 overflow-hidden pt-4">
            <ResizablePanelGroup
              direction="horizontal"
              className="h-full w-full px-2 sm:px-4 space-x-1 sm:space-x-1.5"
            >
              {/* Left Column (25%): Contact Information */}
              <ResizablePanel defaultSize={25} minSize={20} maxSize={30}>
                <div className="h-full overflow-y-auto scrollbar-none">
                  {children}
                </div>
              </ResizablePanel>

              <ResizableHandle withHandle className="invisible" />

              {/* Center Column (50%): Tabbed Interface */}
              <ResizablePanel defaultSize={50} minSize={40} maxSize={60}>
                <div className="h-full overflow-hidden space-y-4">
                  <div className="h-full flex flex-col overflow-scroll">
                    <Tabs
                      defaultValue="notes"
                      className="w-full h-full flex flex-col"
                    >
                      {/* Fixed Tab Headers */}
                      <div className="flex-shrink-0">
                        <TabsList className="grid w-full grid-cols-4 mb-6 bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-1 shadow-inner">
                          <TabsTrigger
                            value="notes"
                            className="text-xs sm:text-sm rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:shadow-gray-500/20 transition-all duration-200 font-medium"
                          >
                            Notes
                          </TabsTrigger>
                          <TabsTrigger
                            value="emails"
                            className="text-xs sm:text-sm rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:shadow-gray-500/20 transition-all duration-200 font-medium"
                          >
                            Emails
                          </TabsTrigger>
                          <TabsTrigger
                            value="tasks"
                            className="text-xs sm:text-sm rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:shadow-gray-500/20 transition-all duration-200 font-medium"
                          >
                            Tasks
                          </TabsTrigger>
                          <TabsTrigger
                            value="whatsapp"
                            className="text-xs sm:text-sm rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:shadow-gray-500/20 transition-all duration-200 font-medium"
                          >
                            WhatsApp
                          </TabsTrigger>
                        </TabsList>
                      </div>

                      {/* Scrollable Tab Content */}
                      <div className="flex-1 overflow-hidden">
                        <TabsContent
                          value="notes"
                          className="h-full overflow-y-auto scrollbar-none mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                        >
                          <div className="flex-1 overflow-y-auto scrollbar-none">
                            {notes}
                          </div>
                        </TabsContent>

                        <TabsContent
                          value="emails"
                          className="h-full overflow-y-auto scrollbar-none mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                        >
                          <div className="flex-1 overflow-y-auto scrollbar-none">
                            {email}
                          </div>
                        </TabsContent>

                        <TabsContent
                          value="tasks"
                          className="h-full overflow-y-auto scrollbar-none mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                        >
                          <div className="flex-1 overflow-y-auto scrollbar-none">
                            {tasks}
                          </div>
                        </TabsContent>

                        <TabsContent
                          value="whatsapp"
                          className="h-full overflow-y-auto scrollbar-none mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                        >
                          <div className="flex-1 overflow-y-auto scrollbar-none">
                            {whatsapp}
                          </div>
                        </TabsContent>
                      </div>
                    </Tabs>
                  </div>
                </div>
              </ResizablePanel>

              <ResizableHandle withHandle className="invisible" />

              {/* Right Column (25%): Business Actions */}
              <ResizablePanel defaultSize={35} minSize={20} maxSize={30}>
                <div className="h-full overflow-y-auto scrollbar-none space-y-4">
                  {activity}
                </div>
              </ResizablePanel>
            </ResizablePanelGroup>
          </div>
        </div>

        {/* Collapsible Bailey AI */}
        <CollapsibleLeadScreen session={session} leadId={id} />
      </div>
    </CopilotKit>
  );
}
