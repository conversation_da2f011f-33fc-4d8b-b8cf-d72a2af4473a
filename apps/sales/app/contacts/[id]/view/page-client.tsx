"use client";

import React from "react";
import { InfoCardWrapper } from "./_components/info-card-wrapper";

interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email?: string | null;
  phoneNumber?: string | null;
  additionalPhone?: string | null;
  role?: string | null;
  accountId?: string | null;
  account?: {
    id: string;
    name?: string | null;
  } | null;
  createdAt: string;
  updatedAt: string;

  userId: string;
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  } | null;
}

interface ContactDetailProps {
  contact: Contact;
  contactId: string;
}

export function ContactDetail({ contact, contactId }: ContactDetailProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-4 pb-4">
      {/* Contact Information Card */}
      <InfoCardWrapper
        title="Contact Information"
        contactId={contactId}
        gridColumns={2}
        formFields={[
          {
            name: "firstName",
            label: "First Name",
            type: "text",
            value: contact.firstName,
            required: true,
          },
          {
            name: "lastName",
            label: "Last Name",
            type: "text",
            value: contact.lastName,
            required: true,
          },
          {
            name: "email",
            label: "Email",
            type: "email",
            value: contact.email,
            showEmpty: true,
          },
          {
            name: "phoneNumber",
            label: "Primary Phone",
            type: "phone",
            value: contact.phoneNumber,
            showEmpty: true,
          },
          {
            name: "additionalPhone",
            label: "Secondary Phone",
            type: "phone",
            value: contact.additionalPhone,
            showEmpty: true,
          },
          {
            name: "role",
            label: "Role",
            type: "text",
            value: contact.role,
            showEmpty: true,
          },
        ]}
      />

      {/* Additional Information Card */}
      <InfoCardWrapper
        title="Additional Information"
        contactId={contactId}
        gridColumns={2}
        formFields={[
          {
            name: "accountName",
            label: "Account",
            type: "text",
            value: contact.account?.name,
            showEmpty: true,
          },
          {
            name: "assignedUser",
            label: "Assigned User",
            type: "text",
            value: contact.user?.name || contact.user?.email,
            showEmpty: true,
          },
          {
            name: "createdAt",
            label: "Created",
            type: "text",
            value: formatDate(contact.createdAt),
            showEmpty: true,
          },
          {
            name: "updatedAt",
            label: "Last Updated",
            type: "text",
            value: formatDate(contact.updatedAt),
            showEmpty: true,
          },
        ]}
      />
    </div>
  );
}
