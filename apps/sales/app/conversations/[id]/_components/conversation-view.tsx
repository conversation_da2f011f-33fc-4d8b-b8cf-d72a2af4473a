"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Skeleton } from "@flinkk/components/ui/skeleton";
import toast from "react-hot-toast";
import { ConversationHeader } from "./conversation-header";
import { MessageList } from "./message-list";
import { MessageComposer } from "./message-composer";
import { ConversationSidebar } from "./conversation-sidebar";
import { ArrowLeftIcon } from "lucide-react";
import { useSocket } from "@/hooks/use-socket";

interface ConversationViewProps {
  conversationId: string;
}

export function ConversationView({ conversationId }: ConversationViewProps) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [conversation, setConversation] = useState<any | null>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isTyping, setIsTyping] = useState(false);

  // Initialize socket connection
  const {
    isConnected,
    // error: socketError, // Unused
    messages: socketMessages,
    typingStatus,
    sendMessage: sendSocketMessage,
    sendTypingIndicator,
  } = useSocket({
    conversationId,
    role: "agent",
    autoConnect: true,
  });

  // Update messages when new socket messages arrive
  useEffect(() => {
    if (socketMessages.length > 0) {
      const latestMessage = socketMessages[socketMessages.length - 1];

      // Check if this message is already in our messages array
      const messageExists = messages.some(
        (msg) =>
          msg.id === latestMessage.id ||
          (msg.content === latestMessage.content &&
            msg.createdAt === latestMessage.createdAt)
      );

      if (!messageExists) {
        setMessages((prev) => [
          ...prev,
          {
            id: latestMessage.id,
            content: latestMessage.content,
            isIncoming: latestMessage.isIncoming,
            createdAt: latestMessage.createdAt,
            sender: null,
          },
        ]);
      }
    }
  }, [socketMessages, messages]);

  // Update typing indicator
  useEffect(() => {
    if (typingStatus) {
      setIsTyping(typingStatus.isTyping && typingStatus.role === "customer");
    } else {
      setIsTyping(false);
    }
  }, [typingStatus]);

  // Fetch conversation data
  useEffect(() => {
    const fetchConversation = async () => {
      try {
        const response = await fetch(`/api/conversations/${conversationId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch conversation");
        }
        const data = await response.json();
        setConversation(data);
        setMessages(data.messages);
      } catch (error) {
        console.error("Error fetching conversation:", error);
        toast.error("Failed to load conversation. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchConversation();
  }, [conversationId, toast]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    // Try socket first if connected
    if (isConnected && sendSocketMessage) {
      try {
        const success = sendSocketMessage(content);

        if (success) {
          // Message will be added to the UI via the socketMessages effect
          return;
        }
      } catch (socketError) {
        console.error("Error sending message via socket:", socketError);
        // Fall back to HTTP API
      }
    }

    // Fall back to HTTP API if socket is not available
    try {
      const response = await fetch(
        `/api/conversations/${conversationId}/messages`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to send message");
      }

      const newMessage = await response.json();
      setMessages((prevMessages) => [...prevMessages, newMessage]);
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message. Please try again.");
    }
  };

  const handleStatusChange = async (status: string) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update conversation status");
      }

      const updatedConversation = await response.json();
      setConversation(updatedConversation);

      toast.success(`Conversation marked as ${status.toLowerCase()}`);
    } catch (error) {
      console.error("Error updating conversation status:", error);
      toast.error("Failed to update conversation status. Please try again.");
    }
  };

  const handleAssigneeChange = async (userId: string) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assignedToId: userId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update conversation assignee");
      }

      const updatedConversation = await response.json();
      setConversation(updatedConversation);

      toast.success("Conversation assignee updated");
    } catch (error) {
      console.error("Error updating conversation assignee:", error);
      toast.error("Failed to update conversation assignee. Please try again.");
    }
  };

  const handleBackClick = () => {
    router.push("/conversations");
  };

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        <div className="p-4 border-b flex items-center">
          <Button variant="ghost" size="sm" onClick={handleBackClick}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Skeleton className="h-6 w-40 ml-4" />
        </div>
        <div className="flex flex-1 overflow-hidden">
          <div className="flex-1 flex flex-col">
            <div className="p-4 border-b">
              <Skeleton className="h-6 w-full mb-2" />
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
            <div className="flex-1 p-4 space-y-4 overflow-auto">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-20 w-full rounded-md" />
                  </div>
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <Skeleton className="h-24 w-full rounded-md" />
            </div>
          </div>
          <div className="w-80 border-l hidden md:block">
            <Skeleton className="h-full w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8 text-center">
        <h3 className="text-lg font-medium">Conversation not found</h3>
        <p className="text-muted-foreground mt-1">
          The conversation you're looking for doesn't exist or you don't have
          permission to view it.
        </p>
        <Button className="mt-2" onClick={handleBackClick}>
          Back to Inbox
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <ConversationHeader
        conversation={conversation}
        onBackClick={handleBackClick}
        onStatusChange={handleStatusChange}
      />
      <div className="flex flex-1 overflow-hidden">
        <div className="flex-1 flex flex-col">
          <MessageList messages={messages} messagesEndRef={messagesEndRef} />
          <MessageComposer
            onSendMessage={handleSendMessage}
            onTyping={sendTypingIndicator}
            isCustomerTyping={isTyping}
            conversation={conversation}
          />
        </div>
        <ConversationSidebar
          conversation={conversation}
          onAssigneeChange={handleAssigneeChange}
        />
      </div>
    </div>
  );
}
