import React from "react";
import { CustomTabs } from "@flinkk/shared-view-port/custom-tabs";

export const metadata = {
  title: "My Leads",
};

export default function FormPageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const tabs = [
    {
      title: "Assigned to me",
      path: "/my-leads/assigned-to-me",
    },
    {
      title: "Created by me",
      path: "/my-leads/created-by-me",
    },
  ];

  return (
    <>
      <CustomTabs tabs={tabs} />
      {children}
    </>
  );
}
