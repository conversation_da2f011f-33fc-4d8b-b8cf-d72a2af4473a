"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import type { GetLeadsSchema } from "./_lib/validations";
import { leadsTableFields } from "shared-constant-table-fields";

export async function getLeads(input: GetLeadsSchema) {
  // Get current user session
  const { userId } = await getServerSession();

  // Get all permissions for leads model
  const permissions = await getModelPermissions("lead");

  // Check if user has permission to read leads
  if (!permissions.canView) {
    throw new Error("You do not have permission to view leads.");
  }

  // Build where conditions - filter by leads created by current user
  const whereConditions = [
    // Filter by creator (createdById field in Lead model)
    { createdById: userId },
    input.name
      ? {
          OR: [
            { firstName: { contains: input.name, mode: "insensitive" } },
            { lastName: { contains: input.name, mode: "insensitive" } },
            { email: { contains: input.name, mode: "insensitive" } },
            { company: { contains: input.name, mode: "insensitive" } },
          ],
        }
      : undefined,
    input.status ? { status: input.status } : undefined,
  ].filter(Boolean);

  // Use base query utility which handles field selection and related fields automatically
  // Note: baseQuery should be enhanced to use RBAC-enabled Prisma client
  const data = await baseQuery(
    "lead",
    {
      ...input,
      tableFields: leadsTableFields,
    },
    whereConditions,
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}

export async function getValueRange() {
  return { min: 0, max: 100 };
}
