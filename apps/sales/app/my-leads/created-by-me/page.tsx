import * as React from "react";
import { LeadsTable } from "./page-client";
import { getLeads } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";

export default async function ListPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  // Get leads data with permissions
  const leadsData = await getLeads({
    ...search,
    filters: validFilters,
  });

  return (
    <div className="p-4 overflow-y-scroll h-full">
      <LeadsTable
        data={leadsData.data}
        pageCount={leadsData.pageCount}
        permissions={leadsData.permissions}
      />
    </div>
  );
}
