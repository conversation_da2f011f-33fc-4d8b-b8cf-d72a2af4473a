"use client";

import type { Lead } from "@/types/lead";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { DeleteConfirmationDialog } from "@flinkk/patterns/model/delete-pop-up";
import { useDelete } from "@flinkk/hooks";
import { getLeadsTableColumns } from "./columns";
import { useRouter } from "next/navigation";

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface LeadsTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

export function LeadsTable({ data, pageCount, permissions }: LeadsTableProps) {
  const router = useRouter();

  console.log("LeadsTable data:", data);
  console.log("LeadsTable permissions:", permissions);

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Lead> | null>(null);

  const { deleteRecord } = useDelete({
    model: "lead",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setRowAction(null);
      router.refresh();
    },
  });

  const handleDeleteLead = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      await deleteRecord(rowAction.row.id);
    }
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    }
  }, [rowAction]);

  const columns = React.useMemo(
    () =>
      getLeadsTableColumns({
        setRowAction,
        permissions,
      }),
    [setRowAction, permissions],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Construct full name from firstName and lastName
          const firstName = row.firstName || "";
          const lastName = row.lastName || "";
          const fullName = `${firstName}${lastName}`.trim();

          // Create URL with title parameter
          const url = `/leads/${row.id}/view?title=${encodeURIComponent(fullName)}`;
          router.push(url);
        }}
        doctype="lead"
      >
        <DataTableToolbar
          table={table}
          buttonText={permissions.canCreate ? "New Lead" : undefined}
          href={permissions.canCreate ? "/leads/new" : undefined}
          buttonTestId="create-lead-button"
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && permissions.canDelete && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeleteLead}
        />
      )}
    </>
  );
}
