import type { Lead } from "@/types/lead";
import {
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from "nuqs/server";
import * as z from "zod";

import {
  getFiltersStateParser,
  getSortingStateParser,
} from "@flinkk/data-table/lib/parsers";

export const searchParamsCache = createSearchParamsCache({
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  name: parseAsString.withDefault(""),
  status: parseAsString.withDefault(""),
  createdAt: parseAsArrayOf(z.coerce.number()).withDefault([]),
  sort: getSortingStateParser<Lead>().withDefault([
    { id: "createdAt", desc: true },
  ]),
  filters: getFiltersStateParser().withDefault([]),
  tableFields: parseAsArrayOf(z.string()).withDefault([]),
  joinOperator: parseAsStringEnum(["and", "or"] as const).withDefault("and"),
});

export type GetLeadsSchema = Awaited<
  ReturnType<typeof searchParamsCache.parse>
>;
