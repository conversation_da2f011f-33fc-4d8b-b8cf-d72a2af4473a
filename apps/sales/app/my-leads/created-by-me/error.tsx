"use client";

import * as React from "react";
import NoAccessSvg from "@flinkk/illustrations/no-access";
import { But<PERSON> } from "@flinkk/components/ui/button";
import Link from "next/link";
import ErrorLoadingSvg from "@flinkk/illustrations/error-loading";

export default function LeadsError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  React.useEffect(() => {
    // Log the error to an error reporting service
    console.error("Leads page error:", error);
  }, [error]);

  if (error.message.toLowerCase().includes("permission")) {
    return (
      <div className="flex h-full flex-col items-center justify-center bg-background p-4 text-center">
        <NoAccessSvg className="w-full max-w-sm" />
        <h2 className="font-heading mt-6 text-2xl font-bold text-destructive sm:text-3xl">
          Access Denied
        </h2>
        <p className="mt-4 max-w-xl text-muted-foreground">
          {error.message} Please contact your administrator for access.
        </p>
        <div className="mt-8 flex justify-center gap-4">
          <Button asChild>
            <Link href="/sales/dashboard">Go to Dashboard</Link>
          </Button>
        </div>
      </div>
    );
  }

  // Generic error for other cases
  return (
    <div className="flex h-full flex-col items-center justify-center bg-background p-4 text-center">
      <ErrorLoadingSvg className="w-full max-w-sm" />
      <h2 className="font-heading my-4 text-2xl font-bold text-destructive sm:text-3xl">
        Oops! Something Went Wrong
      </h2>
      <div className="mt-2 max-w-xl text-muted-foreground">
        <p>
          We encountered an unexpected issue while loading the leads. Our
          technical team has been notified.
        </p>
        <p className="mt-2">
          Please try again, or if the issue persists, contact support.
        </p>
      </div>
      <div className="mt-8 flex justify-center gap-4">
        <Button onClick={() => reset()} variant="outline">
          Try Again
        </Button>
      </div>
    </div>
  );
}
