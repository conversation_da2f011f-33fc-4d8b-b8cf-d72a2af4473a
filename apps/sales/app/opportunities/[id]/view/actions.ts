"use server";

import { prisma } from "@flinkk/database/prisma";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { validateAction } from "@flinkk/shared-rbac";

import { notFound } from "next/navigation";
import { getFieldPermissionsForEntityType } from "@/lib/field-permissions";
import { revalidatePath } from "next/cache";
import { runWithContext } from "@flinkk/database/server";

// Type definitions for client-side use
export interface StatusOptionClient {
  value: string;
  label: string;
  description?: string | null;
  color?: string | null;
  icon?: string | null;
  order?: number;
  isDefault?: boolean;
}

export async function getOpportunityById(id: string) {
  try {
    await validateAction(
      "opportunity",
      "read",
      "You do not have permission to view opportunity details.",
    );
    const { tenantId, userId } = await getServerSession();

    // Get the opportunity with all fields and related data
    const opportunity = await prisma.opportunity.findUnique({
      where: {
        id,
        tenantId: tenantId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        account: {
          select: {
            id: true,
            name: true,
            industry: true,
            website: true,
            phone: true,
            email: true,
            employees: true,
            annualRevenue: true,
            billingAddress: true,
            shippingAddress: true,
            description: true,
            region: true,
            emailDomain: true,
          },
        },
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phoneNumber: true,
          },
        },
        customFields: {
          include: {
            customField: true,
          },
        },
      },
    });

    if (!opportunity) {
      return notFound();
    }

    // If this opportunity was converted from a lead, fetch the lead data for requirements
    let leadData = null;
    if (opportunity.convertedFromLeadId) {
      leadData = await prisma.lead.findUnique({
        where: {
          id: opportunity.convertedFromLeadId,
          tenantId: tenantId,
        },
        select: {
          id: true,
          requirements: true,
          firstName: true,
          lastName: true,
          company: true,
          description: true,
          notes: true,
        },
      });
    }

    // Get the user's role in the organization
    const userOrg = await prisma.memberShip.findUnique({
      where: {
        userId_tenantId: {
          userId: userId,
          tenantId: tenantId,
        },
      },
    });

    if (!userOrg) {
      throw new Error("Unauthorized");
    }

    // Get field permissions for this entity type
    const fieldPermissions = await getFieldPermissionsForEntityType(
      "Opportunity",
      userOrg.role as any, // Cast to any to avoid type mismatch between Prisma enum and local enum
      tenantId,
    );

    // For opportunity detail view, we want to show all fields without permission filtering
    // Only apply permissions to custom fields, not the core opportunity fields
    const opportunityWithPermissions = opportunity;

    // Get custom fields for opportunities
    const customFields = await prisma.customField.findMany({
      where: {
        entityType: "opportunity",
        tenantId: tenantId,
      },
    });

    // Filter custom fields based on permissions
    const visibleCustomFields = customFields.filter(
      (field: { id: string; isVisibleByDefault: boolean }) =>
        fieldPermissions[field.id]?.canView ?? field.isVisibleByDefault,
    );

    // Prepare the response with custom fields and lead data
    const opportunityWithCustomFields = {
      ...opportunityWithPermissions,
      customFields: visibleCustomFields.map((field: any) => {
        // Find the value for this field if it exists
        const fieldValue = opportunityWithPermissions?.customFields?.find(
          (v: any) => v.customFieldId === field.id,
        );
        return {
          ...field,
          value: fieldValue?.value || null,
        };
      }),
      // Add lead data if this opportunity was converted from a lead
      convertedFromLead: leadData,
      // Add requirements from lead if available, otherwise use opportunity requirements
      requirements:
        leadData?.requirements ||
        opportunityWithPermissions.requirements ||
        null,
    };

    return opportunityWithCustomFields;
  } catch (error) {
    console.error("Error fetching opportunity:", error);
    throw new Error("Failed to fetch opportunity details");
  }
}

/**
 * Create a new opportunity
 * @param data Opportunity data to create
 * @returns The created opportunity
 */
export async function createOpportunity(data: {
  // New fields
  dealId?: string;
  dealName: string;
  description: string;
  contactId?: string;
  accountId?: string;
  relatedToType: string;
  relatedToId: string;
  associatedContacts?: string;
  dealOwner?: string;
  stage?: string;
  status?: string;
  value?: number;
  currency?: string;
  expectedCloseDate?: string;
  actualCloseDate?: string;
  source?: string;
  campaignId?: string;
  tags?: string;
  convertedFromLeadId?: string;
  lastContactedAt?: string;
  stageEnteredAt?: string;
  probability?: number;
  bookingId?: string;
  fulfillmentId?: string;

  // Legacy fields for backward compatibility
  name?: string;
  amount?: number;
  closeDate?: string;
  userId?: string;
}) {
  try {
    await validateAction(
      "opportunity",
      "create",
      "You do not have permission to create new opportunities.",
    );
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      throw new Error("Unauthorized: User ID or Tenant ID is missing");
    }

    // Helper function to validate ObjectId fields
    const validateObjectId = (id: string | undefined): string | undefined => {
      if (!id || id.trim() === "") return undefined;
      // Check if the string is a valid MongoDB ObjectId (24 hex characters)
      const objectIdRegex = /^[0-9a-fA-F]{24}$/;
      if (!objectIdRegex.test(id)) return undefined;
      return id;
    };

    // Create context for activity logging
    const context = {
      userId,
      tenantId,
    };

    // Create the opportunity with context for activity logging
    const opportunity = await runWithContext(context, async () => {
      return await prisma.opportunity.create({
        data: {
          // New fields
          dealId: data.dealId,
          dealName: data.dealName || data.name || "",
          description: data.description,
          contactId: validateObjectId(data.contactId),
          accountId: validateObjectId(data.accountId),
          relatedToType: data.relatedToType as any,
          relatedToId:
            validateObjectId(data.relatedToId) ||
            validateObjectId(data.accountId) ||
            "GENERAL",
          associatedContacts: data.associatedContacts,
          dealOwner:
            validateObjectId(data.dealOwner) ||
            validateObjectId(data.userId) ||
            userId,
          stage: data.stage || "DISCOVERY",
          status: data.status || "OPEN",
          value: data.value || data.amount,
          currency: data.currency || "USD",
          expectedCloseDate: data.expectedCloseDate
            ? new Date(data.expectedCloseDate)
            : data.closeDate
              ? new Date(data.closeDate)
              : null,
          actualCloseDate: data.actualCloseDate
            ? new Date(data.actualCloseDate)
            : null,
          source: data.source,
          campaignId: validateObjectId(data.campaignId),
          tags: data.tags,
          convertedFromLeadId: validateObjectId(data.convertedFromLeadId),
          lastContactedAt: data.lastContactedAt
            ? new Date(data.lastContactedAt)
            : null,
          stageEnteredAt: data.stageEnteredAt
            ? new Date(data.stageEnteredAt)
            : new Date(),
          probability: data.probability || 50,
          bookingId: validateObjectId(data.bookingId),
          fulfillmentId: validateObjectId(data.fulfillmentId),

          // Legacy fields for backward compatibility
          name: data.dealName || data.name || "",
          notes: data.description,
          userId:
            validateObjectId(data.dealOwner) ||
            validateObjectId(data.userId) ||
            userId,
          tenantId: tenantId,
          createdById: userId,
          updatedById: userId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          account: {
            select: {
              id: true,
              name: true,
              industry: true,
              website: true,
              phone: true,
              email: true,
              employees: true,
              annualRevenue: true,
              billingAddress: true,
              shippingAddress: true,
              description: true,
              region: true,
              emailDomain: true,
            },
          },
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phoneNumber: true,
            },
          },
        },
      });
    });

    // Revalidate the opportunities list page
    revalidatePath("/opportunities");

    return opportunity;
  } catch (error) {
    console.error("Error creating opportunity:", error);
    throw error;
  }
}

/**
 * Create a new account
 * @param data Account data to create
 * @returns The created account
 */
export async function createAccount(data: { name: string; type: string }) {
  try {
    await validateAction(
      "account",
      "create",
      "You do not have permission to create new accounts.",
    );
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      throw new Error("Unauthorized: User ID or Tenant ID is missing");
    }

    // Create context for activity logging
    const context = {
      userId,
      tenantId,
    };

    // Create the account with context for activity logging
    const account = await runWithContext(context, async () => {
      return await prisma.businessAccount.create({
        data: {
          name: data.name,
          type: data.type,
          industry: "",
          website: "",
          phone: "",
          email: "",
          userId: userId,
          tenantId: tenantId,
          createdById: userId,
          updatedById: userId,
        },
      });
    });

    return account;
  } catch (error) {
    console.error("Error creating account:", error);
    throw error;
  }
}
