"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Badge } from "@flinkk/components/ui/badge";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Checkbox } from "@flinkk/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@flinkk/components/ui/dropdown-menu";
import { DataTable } from "@flinkk/data-table/component/data-table";
import { DataTableColumnHeader } from "@flinkk/data-table/component/data-table-column-header";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { useDelete } from "@flinkk/hooks/mutation/use-delete";
import { formatDate } from "@/lib/utils";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";
import { FileTextIcon, PlusIcon } from "lucide-react";
import { QuoteGeneratorButton } from "@flinkk/quote-pdf";

const statusOptions = [
  { label: "Draft", value: "DRAFT" },
  { label: "In Review", value: "IN_REVIEW" },
  { label: "Sent", value: "SENT" },
  { label: "Accepted", value: "ACCEPTED" },
  { label: "Rejected", value: "REJECTED" },
  { label: "Expired", value: "EXPIRED" },
  { label: "Converted", value: "CONVERTED" },
  { label: "Cancelled", value: "CANCELLED" },
];

interface OpportunityQuotesTableProps {
  data: any[];
  pageCount: number;
  opportunityId: string;
}

export function OpportunityQuotesTable({
  data,
  pageCount,
  opportunityId,
}: OpportunityQuotesTableProps) {
  const router = useRouter();

  const { deleteRecord: deleteQuote } = useDelete({
    model: "quote",
    successMessage: "Quote deleted successfully",
    errorMessage: "Failed to delete quote",
  });

  // Handle row click navigation
  const handleRowClick = React.useCallback(
    (quote: any) => {
      // Use quote name for the tab title
      const quoteName = quote.name || "Unnamed Quote";

      // Create URL with title parameter for view mode
      const url = `/quotes/${quote.id}?title=${encodeURIComponent(quoteName)}&mode=view`;
      router.push(url);
    },
    [router],
  );

  // Handle Add Quote button click
  const handleAddQuote = React.useCallback(() => {
    router.push(`/quotes/new?opportunityId=${opportunityId}`);
  }, [router, opportunityId]);

  const columns = React.useMemo(
    () => [
      {
        id: "select",
        header: ({ table }: any) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }: any) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "name",
        header: ({ column }: any) => (
          <DataTableColumnHeader column={column} title="Quote Name" />
        ),
        cell: ({ row }: any) => {
          return (
            <div className="flex space-x-2">
              <span className="max-w-[500px] truncate font-medium">
                {row.getValue("name")}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: "status",
        header: ({ column }: any) => (
          <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }: any) => {
          const status = row.getValue("status");
          const statusOption = statusOptions.find(
            (option) => option.value === status,
          );

          if (!statusOption) {
            return null;
          }

          return (
            <Badge variant="outline" className="capitalize">
              {statusOption.label}
            </Badge>
          );
        },
        filterFn: (row: any, id: string, value: string) => {
          return value.includes(row.getValue(id));
        },
      },
      {
        accessorKey: "grandTotal",
        header: ({ column }: any) => (
          <DataTableColumnHeader column={column} title="Total Amount" />
        ),
        cell: ({ row }: any) => {
          const amount = parseFloat(row.getValue("grandTotal"));
          const formatted = new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
          }).format(amount);

          return <div className="font-medium">{formatted}</div>;
        },
      },
      {
        accessorKey: "validUntil",
        header: ({ column }: any) => (
          <DataTableColumnHeader column={column} title="Valid Until" />
        ),
        cell: ({ row }: any) => {
          const date = row.getValue("validUntil");
          return date ? formatDate(date) : "—";
        },
      },
      {
        accessorKey: "createdAt",
        header: ({ column }: any) => (
          <DataTableColumnHeader column={column} title="Created" />
        ),
        cell: ({ row }: any) => formatDate(row.getValue("createdAt")),
      },
      {
        id: "actions",
        cell: ({ row }: any) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
              >
                <DotsHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() => router.push(`/quotes/${row.original.id}`)}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <QuoteGeneratorButton
                  quote={{
                    quote_number:
                      row.original.quoteNumber ||
                      `QUO-${row.original.id.slice(-6)}`,
                    date_issued: new Date(row.original.createdAt).toISOString(),
                    valid_until: row.original.validUntil
                      ? new Date(row.original.validUntil).toISOString()
                      : new Date(
                          Date.now() + 30 * 24 * 60 * 60 * 1000,
                        ).toISOString(),
                    customer_name: row.original.name || "Customer",
                    customer_address: "Customer Address",
                    payment_terms: row.original.paymentTerms || "Net 30",
                    line_items: [],
                  }}
                  variant="download"
                  size="sm"
                  className="w-full justify-start p-0 h-auto font-normal"
                  options={{
                    company_name: "Flinkk CRM",
                    company_address:
                      "Tech Park, Electronic City\nBangalore, Karnataka 560100\nIndia",
                  }}
                >
                  <div className="flex items-center w-full px-2 py-1.5">
                    <FileTextIcon className="mr-2 h-4 w-4" />
                    Generate PDF
                  </div>
                </QuoteGeneratorButton>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => deleteQuote(row.original.id)}
                className="text-destructive"
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
      },
    ],
    [deleteQuote, router],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    clearOnDefault: true,
  });

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Quotes</h3>
        <Button onClick={handleAddQuote} size="sm" className="gap-2">
          <PlusIcon className="h-4 w-4" />
          Add Quote
        </Button>
      </div>

      <DataTable table={table} doctype="quote" onRowClick={handleRowClick}>
        <DataTableToolbar table={table} />
      </DataTable>
    </div>
  );
}
