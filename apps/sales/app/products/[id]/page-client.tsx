"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { Button } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { Loader2Icon } from "lucide-react";

// Import our dynamic form elements
import {
  TextFieldFormElement,
  NumberFieldFormElement,
  TextAreaFieldFormElement,
  SelectFieldFormElement,
} from "@flinkk/dynamic-form/form-elements";

// Import UI components for custom select
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import {
  FormItem,
  FormMessage,
  FormLabel,
  FormControl,
  FormField,
} from "@flinkk/components/ui/form";
import { cn } from "@flinkk/lib/cn";

// Import section wrapper
import { SectionWrapper } from "@flinkk/dynamic-form/components";

// Remove server action imports - using new hook instead
import { GroupedCustomFieldsForm } from "@/features/crm/components/grouped-custom-fields-form";
import { FieldPermissionsData } from "@flinkk/shared-auth/utils/field-permissions-utils";
import { Unit, ProductCategory } from "@/types/product";
import { UnitCreationModal } from "./_components/unit-creation-modal";
import { CategoryCreationModal } from "./_components/category-creation-modal";

// Zod schema for product form validation
const productFormSchema = z.object({
  // Basic information
  name: z
    .string()
    .min(1, "Product name is required")
    .max(100, "Product name must be less than 100 characters"),
  sku: z.string().max(50, "SKU must be less than 50 characters").optional(),
  description: z
    .string()
    .max(2000, "Description must be less than 2000 characters")
    .optional(),

  // Product type and classification
  type: z.enum(["GOODS", "SERVICE"], {
    required_error: "Product type is required",
  }),
  categoryId: z.string().min(1, "Category is required"),
  unitId: z.string().optional(),
  hsnCode: z
    .string()
    .max(20, "HSN code must be less than 20 characters")
    .optional(),
  taxPreference: z.enum(["TAXABLE", "NON_TAXABLE", "EXEMPT"], {
    required_error: "Tax preference is required",
  }),

  // Pricing fields
  sellingPrice: z
    .number()
    .min(0, "Selling price must be a positive number")
    .max(*********, "Selling price is too large"),
  costPrice: z
    .number()
    .min(0, "Cost price must be a positive number")
    .max(*********, "Cost price is too large")
    .optional(),

  // Sales information
  salesAccount: z
    .string()
    .max(100, "Sales account must be less than 100 characters")
    .optional(),
  salesDescription: z
    .string()
    .max(1000, "Sales description must be less than 1000 characters")
    .optional(),

  // Purchase information
  purchaseAccount: z
    .string()
    .max(100, "Purchase account must be less than 100 characters")
    .optional(),
  purchaseDescription: z
    .string()
    .max(1000, "Purchase description must be less than 1000 characters")
    .optional(),
  preferredVendor: z
    .string()
    .max(100, "Preferred vendor must be less than 100 characters")
    .optional(),

  // Tax information
  intraStateTaxRate: z
    .number()
    .min(0, "Tax rate must be a positive number")
    .max(100, "Tax rate cannot exceed 100%")
    .optional(),
  interStateTaxRate: z
    .number()
    .min(0, "Tax rate must be a positive number")
    .max(100, "Tax rate cannot exceed 100%")
    .optional(),
});

type ProductFormValues = z.infer<typeof productFormSchema>;

interface NewProductFormProps {
  id?: string;
  initialData?: any;
  fieldPermissionsData: FieldPermissionsData;
  customFields?: CustomField[];
}

interface CustomField {
  id: string;
  name: string;
  label: string;
  description?: string | null;
  type: string;
  isRequired: boolean;
  defaultValue?: string | null;
  placeholder?: string | null;
  helpText?: string | null;
  options?: any;
  value?: string | null;
}

export function NewProductForm({
  id,
  initialData,
  fieldPermissionsData,
  customFields = [],
}: NewProductFormProps) {
  const router = useRouter();
  const isEditMode = id !== "new" && initialData;

  // Use the new useSaveFormData hook
  const { save: saveProduct, isLoading: isSaving } = useSaveFormData({
    model: "product",
    onSuccess: () => {
      router.push("/products");
    },
  });

  // Initialize React Hook Form with Zod validation
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      // Basic information
      name: initialData?.name || "",
      sku: initialData?.sku || "",
      description: initialData?.description || "",

      // Product type and classification
      type: initialData?.type || "SERVICE",
      categoryId: initialData?.categoryId || "",
      unitId: initialData?.unitId || "",
      hsnCode: initialData?.hsnCode || "",
      taxPreference: initialData?.taxPreference || "TAXABLE",

      // Pricing fields (use new fields, fallback to legacy)
      sellingPrice: initialData?.sellingPrice || initialData?.price || 0,
      costPrice: initialData?.costPrice || initialData?.cost || 0,

      // Sales information
      salesAccount: initialData?.salesAccount || "",
      salesDescription: initialData?.salesDescription || "",

      // Purchase information
      purchaseAccount: initialData?.purchaseAccount || "",
      purchaseDescription: initialData?.purchaseDescription || "",
      preferredVendor: initialData?.preferredVendor || "",

      // Tax information
      intraStateTaxRate: initialData?.intraStateTaxRate || 0,
      interStateTaxRate: initialData?.interStateTaxRate || 0,
    },
  });

  // State for categories, units and custom fields
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [units, setUnits] = useState<Unit[]>([]);
  const [isLoadingUnits, setIsLoadingUnits] = useState(false);
  const [isUnitModalOpen, setIsUnitModalOpen] = useState(false);
  const [customFieldValues, setCustomFieldValues] = useState<
    Record<string, any>
  >({});

  // Load categories and units on component mount
  useEffect(() => {
    const loadCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const response = await fetch("/api/method/categories/list");
        if (response.ok) {
          const categoriesData = await response.json();
          setCategories(categoriesData);
        } else {
          console.error("Failed to load categories");
        }
      } catch (error) {
        console.error("Error loading categories:", error);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    const loadUnits = async () => {
      setIsLoadingUnits(true);
      try {
        const response = await fetch("/api/units");
        if (response.ok) {
          const unitsData = await response.json();
          setUnits(unitsData);
        } else {
          console.error("Failed to load units");
        }
      } catch (error) {
        console.error("Error loading units:", error);
      } finally {
        setIsLoadingUnits(false);
      }
    };

    loadCategories();
    loadUnits();
  }, []);

  // Set initial custom field values if in edit mode
  useEffect(() => {
    if (initialData?.customFields) {
      const initialCustomFieldValues: Record<string, any> = {};
      initialData.customFields.forEach((field: CustomField) => {
        if (field.value !== null) {
          initialCustomFieldValues[field.id] = field.value;
        }
      });
      setCustomFieldValues(initialCustomFieldValues);
    }
  }, [initialData]);

  // Handle category creation
  const handleCategoryCreated = (newCategory: ProductCategory) => {
    // Add the new category to the list
    setCategories((prevCategories) => {
      const updatedCategories = [...prevCategories, newCategory];
      return updatedCategories;
    });

    // Close the modal immediately
    setIsCategoryModalOpen(false);

    // Use setTimeout to ensure the categories state is updated before setting the form value
    setTimeout(() => {
      // Set the form value to the new category
      form.setValue("categoryId", newCategory.id);

      // Reset form with current values + new category selection
      const currentValues = form.getValues();
      form.reset({
        ...currentValues,
        categoryId: newCategory.id,
      });

      // Force trigger validation and re-render
      form.trigger("categoryId");
    }, 100);
  };

  // Handle unit creation
  const handleUnitCreated = (newUnit: Unit) => {
    // Add the new unit to the list
    setUnits((prevUnits) => {
      const updatedUnits = [...prevUnits, newUnit];
      return updatedUnits;
    });

    // Close the modal immediately
    setIsUnitModalOpen(false);

    // Use setTimeout to ensure the units state is updated before setting the form value
    setTimeout(() => {
      // Set the form value to the new unit
      form.setValue("unitId", newUnit.id);

      // Reset form with current values + new unit selection
      const currentValues = form.getValues();
      form.reset({
        ...currentValues,
        unitId: newUnit.id,
      });

      // Force trigger validation and re-render
      form.trigger("unitId");
    }, 100);
  };

  // Watch for tax preference and product type changes for dynamic field visibility
  const taxPreference = form.watch("taxPreference");
  const productType = form.watch("type");

  // Determine if tax-related fields should be visible
  const showTaxFields = taxPreference !== "NON_TAXABLE";

  // Determine the HSN/SAC label based on product type
  const getHsnSacLabel = () => {
    return productType === "SERVICE" ? "SAC Code" : "HSN Code";
  };

  // Handle form submission using the new hook
  const onSubmit = async (data: ProductFormValues) => {
    // Prepare the data for submission
    const submissionData = {
      ...data,
      customFields: customFieldValues,
    };

    // Use the new save function from the hook
    await saveProduct(id, submissionData);
  };

  // Prepare category options for dropdown (sorted with system categories first, then custom categories)
  const sortedCategories = [...categories].sort((a, b) => {
    // System categories first, then alphabetical by name
    if (a.isSystem && !b.isSystem) return -1;
    if (!a.isSystem && b.isSystem) return 1;
    return a.name.localeCompare(b.name);
  });

  const categoryOptions = sortedCategories.map((category) => ({
    value: category.id,
    label: category.name,
    isSystem: category.isSystem,
  }));

  // Prepare unit options for dropdown (sorted with system units first, then custom units)
  const sortedUnits = [...units].sort((a, b) => {
    // System units first, then alphabetical by display name
    if (a.isSystem && !b.isSystem) return -1;
    if (!a.isSystem && b.isSystem) return 1;
    return a.displayName.localeCompare(b.displayName);
  });

  const unitOptions = sortedUnits.map((unit) => ({
    value: unit.id,
    label: unit.displayName,
    isSystem: unit.isSystem,
  }));

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Product Information Section */}
        <SectionWrapper
          title="Product Information"
          description="Basic product details and classification"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <TextFieldFormElement
              control={form.control}
              name="name"
              label="Name"
              required={true}
              placeholder="Enter product/service name"
            />

            <TextFieldFormElement
              control={form.control}
              name="sku"
              label="SKU"
              required={false}
              placeholder="Enter SKU code"
            />

            <SelectFieldFormElement
              control={form.control}
              name="type"
              label="Type"
              required={true}
              placeholder="Select type"
              options={[
                {
                  value: "SERVICE",
                  label: "Service",
                },
                {
                  value: "GOODS",
                  label: "Goods",
                },
              ]}
            />

            <div className="md:col-span-2 lg:col-span-3">
              <TextAreaFieldFormElement
                control={form.control}
                name="description"
                label="Description"
                required={false}
                placeholder="Detailed description of the product or service..."
                rows={4}
              />
            </div>

            {/* Category Field with Add New in Dropdown */}
            <FormField
              control={form.control}
              name="categoryId"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      fieldState.error
                        ? "text-red-500"
                        : "text-muted-foreground",
                    )}
                  >
                    Category
                    <span className="text-destructive">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoadingCategories}
                  >
                    <FormControl>
                      <SelectTrigger
                        className={cn(fieldState.error && "border-red-500")}
                      >
                        <SelectValue
                          placeholder={
                            isLoadingCategories
                              ? "Loading categories..."
                              : "Select category"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categoryOptions?.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                      <div className="border-t mx-1 mt-1 pt-1">
                        <button
                          type="button"
                          onClick={() => setIsCategoryModalOpen(true)}
                          className="w-full text-left px-2 py-1.5 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-sm font-medium"
                        >
                          + Add New Category
                        </button>
                      </div>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Unit Field with Add New in Dropdown */}
            <FormField
              control={form.control}
              name="unitId"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      fieldState.error
                        ? "text-red-500"
                        : "text-muted-foreground",
                    )}
                  >
                    Unit
                    <span className="text-destructive">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoadingUnits}
                  >
                    <FormControl>
                      <SelectTrigger
                        className={cn(fieldState.error && "border-red-500")}
                      >
                        <SelectValue
                          placeholder={
                            isLoadingUnits ? "Loading units..." : "Select unit"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {unitOptions?.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                      <div className="border-t mx-1 mt-1 pt-1">
                        <button
                          type="button"
                          onClick={() => setIsUnitModalOpen(true)}
                          className="w-full text-left px-2 py-1.5 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-sm font-medium"
                        >
                          + Add New Unit
                        </button>
                      </div>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </SectionWrapper>

        {/* Sales Information Section */}
        <SectionWrapper
          title="Sales Information"
          description="Pricing and sales account details"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <NumberFieldFormElement
              control={form.control}
              name="sellingPrice"
              label="Selling Price"
              required={true}
              placeholder="0.00"
              min={0}
              step={0.01}
            />

            <TextFieldFormElement
              control={form.control}
              name="salesAccount"
              label="Sales Account"
              required={false}
              placeholder="Enter sales account"
            />

            <div className="md:col-span-2 lg:col-span-3">
              <TextAreaFieldFormElement
                control={form.control}
                name="salesDescription"
                label="Sales Description"
                required={false}
                placeholder="Description for sales documents..."
                rows={3}
              />
            </div>
          </div>
        </SectionWrapper>

        {/* Purchase Information Section */}
        <SectionWrapper
          title="Purchase Information"
          description="Cost and purchase account details"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <NumberFieldFormElement
              control={form.control}
              name="costPrice"
              label="Cost Price"
              required={false}
              placeholder="0.00"
              min={0}
              step={0.01}
            />

            <TextFieldFormElement
              control={form.control}
              name="purchaseAccount"
              label="Purchase Account"
              required={false}
              placeholder="Enter purchase account"
            />

            <TextFieldFormElement
              control={form.control}
              name="preferredVendor"
              label="Preferred Vendor"
              required={false}
              placeholder="Enter vendor name"
            />

            <div className="md:col-span-2 lg:col-span-3">
              <TextAreaFieldFormElement
                control={form.control}
                name="purchaseDescription"
                label="Purchase Description"
                required={false}
                placeholder="Description for purchase documents..."
                rows={3}
              />
            </div>
          </div>
        </SectionWrapper>

        {/* Tax Information Section */}
        <SectionWrapper
          title="Tax Information"
          description="Tax rates and preferences"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <SelectFieldFormElement
                control={form.control}
                name="taxPreference"
                label="Tax Preference"
                required={true}
                placeholder="Select tax preference"
                options={[
                  { value: "TAXABLE", label: "Taxable" },
                  { value: "NON_TAXABLE", label: "Non-Taxable" },
                  { value: "EXEMPT", label: "Exempt" },
                ]}
              />
            </div>

            {/* Conditionally render HSN/SAC Code and tax rate fields based on tax preference */}
            {showTaxFields && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <TextFieldFormElement
                  control={form.control}
                  name="hsnCode"
                  label={getHsnSacLabel()}
                  required={false}
                  placeholder={`Enter ${getHsnSacLabel().toLowerCase()}`}
                />

                <NumberFieldFormElement
                  control={form.control}
                  name="intraStateTaxRate"
                  label="Intra-State Tax Rate (%)"
                  required={false}
                  placeholder="0.00"
                  min={0}
                  max={100}
                  step={0.01}
                />

                <NumberFieldFormElement
                  control={form.control}
                  name="interStateTaxRate"
                  label="Inter-State Tax Rate (%)"
                  required={false}
                  placeholder="0.00"
                  min={0}
                  max={100}
                  step={0.01}
                />
              </div>
            )}
          </div>
        </SectionWrapper>

        {/* Custom Fields Section */}
        {customFields.length > 0 && (
          <GroupedCustomFieldsForm
            customFields={customFields}
            values={customFieldValues}
            onChange={(fieldId: string, value: string | null) => {
              setCustomFieldValues(prev => ({
                ...prev,
                [fieldId]: value
              }));
            }}
            entityType="Product"
          />
        )}

        {/* Submit Button */}
        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/products")}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
            {isEditMode ? "Update Product" : "Create Product"}
          </Button>
        </div>
      </form>

      {/* Category Creation Modal */}
      <CategoryCreationModal
        isOpen={isCategoryModalOpen}
        onClose={() => setIsCategoryModalOpen(false)}
        onCategoryCreated={handleCategoryCreated}
      />

      {/* Unit Creation Modal */}
      <UnitCreationModal
        isOpen={isUnitModalOpen}
        onClose={() => setIsUnitModalOpen(false)}
        onUnitCreated={handleUnitCreated}
      />
    </Form>
  );
}
