"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { Typography } from "@flinkk/components/ui/typography";
import { Card, CardContent } from "@flinkk/components/ui/card";
import { Badge } from "@flinkk/components/ui/badge";
import {
  BoringAvatar,
  avatarPresets,
} from "@flinkk/components/ui/boring-avatar";
import {
  ArrowRightIcon,
  EditIcon,
  BuildingIcon,
  MailIcon,
  PhoneIcon,
  Loader2Icon,
} from "lucide-react";
import Link from "next/link";
import dynamic from "next/dynamic";

// Dynamic import for convert modal
const ConvertOpportunityModal = dynamic(
  () =>
    import("./convert-opportunity-modal").then(
      (mod) => mod.ConvertOpportunityModal,
    ),
  {
    ssr: false,
  },
);
interface HeaderProps {
  lead: any;
  leadId: string;
  userId: string;
  leadEmail?: string;
}

// Helper function to get priority color using theme variables
const getPriorityColor = (priority: string) => {
  switch (priority?.toLowerCase()) {
    case "high":
      return "bg-destructive/10 text-destructive border-destructive/20";
    case "medium":
      return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20 dark:text-yellow-400";
    case "low":
      return "bg-green-500/10 text-green-600 border-green-500/20 dark:text-green-400";
    default:
      return "bg-muted text-muted-foreground border-border";
  }
};

export const Header = ({ lead, leadId, userId, leadEmail }: HeaderProps) => {
  const [isConvertDialogOpen, setIsConvertDialogOpen] = useState(false);
  const [isConverting, setIsConverting] = useState(false);

  // Get lead full name
  const getLeadFullName = () => {
    if (lead.firstName && lead.lastName) {
      return `${lead.firstName} ${lead.lastName}`;
    } else if (lead.firstName) {
      return lead.firstName;
    } else if (lead.lastName) {
      return lead.lastName;
    } else if (lead.email) {
      return lead.email;
    }
    return "Unnamed Lead";
  };

  const handleConvertClick = () => {
    setIsConverting(true);
    setIsConvertDialogOpen(true);
  };

  return (
    <div className="sticky top-0 z-10">
      <Card className="border-0 shadow-none bg-transparent p-0">
        <CardContent className="p-0">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            {/* Left Side - Lead Information Card */}
            <div className="flex items-center gap-6">
              {/* Lead Avatar */}
              <BoringAvatar
                name={
                  lead.email ||
                  `${lead.firstName} ${lead.lastName}` ||
                  `lead-${lead.id}`
                }
                size="xl"
                {...avatarPresets.lead}
                border="primary"
                shadow="lg"
              />

              {/* Lead Details */}
              <div className="min-w-0 flex-1 space-y-2">
                <div className="flex items-center gap-3 flex-wrap">
                  <Typography
                    type="h1"
                    className="font-bold tracking-tight text-lg lg:text-xl truncate"
                  >
                    {getLeadFullName()}
                  </Typography>
                  {lead.priority && (
                    <Badge
                      variant="outline"
                      size="sm"
                      className={getPriorityColor(lead.priority)}
                    >
                      {lead.priority} Priority
                    </Badge>
                  )}
                  {lead.isB2B && (
                    <Badge variant="secondary" size="sm">
                      B2B Lead
                    </Badge>
                  )}
                </div>

                {/* Quick Contact Info */}
                <div className="flex items-center gap-4 text-muted-foreground">
                  {lead.company && (
                    <div className="flex items-center gap-1.5">
                      <BuildingIcon className="h-3.5 w-3.5 text-muted-foreground" />
                      <span className="text-xs truncate max-w-[180px]">
                        {lead.company}
                      </span>
                    </div>
                  )}
                  {lead.email && (
                    <div className="flex items-center gap-1.5">
                      <MailIcon className="h-3.5 w-3.5 text-muted-foreground" />
                      <span className="text-xs truncate max-w-[180px]">
                        {lead.email}
                      </span>
                    </div>
                  )}
                  {lead.phone && (
                    <div className="flex items-center gap-1.5">
                      <PhoneIcon className="h-3.5 w-3.5 text-muted-foreground" />
                      <span className="text-xs">{lead.phone}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Side - Action Buttons */}
            <div className="flex flex-wrap gap-3 shrink-0">
              {/* Edit Lead Button */}
              <Button
                variant="outline"
                size="default"
                asChild
                className="gap-2"
              >
                <Link href={`/leads/${leadId}?title=${encodeURIComponent(`Edit ${getLeadFullName()}`)}`}>
                  <EditIcon className="h-4 w-4" />
                  Edit Lead
                </Link>
              </Button>

              {/* Convert to Opportunity Button */}
              {!lead.isConverted ? (
                <Button
                  onClick={handleConvertClick}
                  size="default"
                  disabled={isConverting}
                  className="gap-2"
                >
                  {isConverting ? (
                    <Loader2Icon className="h-4 w-4 animate-spin" />
                  ) : (
                    <ArrowRightIcon className="h-4 w-4" />
                  )}
                  Convert to Opportunity
                </Button>
              ) : (
                <Button
                  variant="outline"
                  disabled
                  size="default"
                  className="gap-2"
                >
                  <ArrowRightIcon className="h-4 w-4" />
                  Already Converted
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Convert Opportunity Modal */}
      <ConvertOpportunityModal
        isConvertDialogOpen={isConvertDialogOpen}
        setIsConvertDialogOpen={(open) => {
          setIsConvertDialogOpen(open);
          if (!open) setIsConverting(false);
        }}
        lead={lead}
      />
    </div>
  );
};
