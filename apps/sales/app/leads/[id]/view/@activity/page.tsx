import { getActivityByEntity } from "@flinkk/shared-slots-activity/page-actions";
import { CompactPageClient } from "@flinkk/shared-slots-activity/compact-page-client";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { getLeadById } from "../actions";
import { Badge } from "@flinkk/components/ui/badge";
import { Typography } from "@flinkk/components/ui/typography";
import { validateAction } from "@flinkk/shared-rbac";

import { InfoCardWrapper } from "../_components/info-card-wrapper";
import { transformFieldsForInfoCard } from "../_utils/custom-fields-grouping";

const getLeadScoreColor = (score: number) => {
  if (score >= 80) return "text-green-600 dark:text-green-400";
  if (score >= 60) return "text-yellow-600 dark:text-yellow-400";
  if (score >= 40) return "text-orange-600 dark:text-orange-400";
  return "text-red-600 dark:text-red-400";
};

const getPriorityColor = (priority: string) => {
  switch (priority?.toUpperCase()) {
    case "HIGH":
      return "bg-red-500/10 text-red-600 border-red-500/20";
    case "MEDIUM":
      return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20";
    case "LOW":
      return "bg-green-500/10 text-green-600 border-green-500/20";
    default:
      return "bg-gray-500/10 text-gray-600 border-gray-500/20";
  }
};

const formatLeadType = (isB2B: boolean) => {
  return isB2B ? "B2B (Business)" : "B2C (Individual)";
};

// Helper function to format status
const formatStatus = (status: string): string => {
  return status
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

// Normalize status to match dropdown options
const normalizeStatus = (status: string) => {
  if (!status) return "";
  const map: Record<string, string> = {
    "OPEN": "OPEN",
    "CONVERTED": "CONVERTED",
    "LOST": "LOST",
    // Add more mappings if needed
  };
  const upper = status.toUpperCase();
  return map[upper] || "";
};

export default async function LeadsActivityPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;

  const { tenantId, userId } = await getServerSession();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const leadId = params.id;
  const [activityLogs, lead] = await Promise.all([
    getActivityByEntity("LEAD", leadId, tenantId),
    getLeadById(leadId),
  ]);

  const leadScore = lead.leadScore || 0;

  // Filter custom fields for Enquiry section
  const enquiryFields =
    lead.customFields?.filter((field: any) => field.groupName === "Enquiry") ||
    [];


  return (
    <div className="space-y-4">
      {/* Enquiry Section - Only show if there are enquiry fields */}
      {enquiryFields.length > 0 && (
        <InfoCardWrapper
          title="Enquiry"
          leadId={leadId}
          gridColumns={1}
          formFields={
            transformFieldsForInfoCard(enquiryFields, leadId) as Array<{
              name: string;
              label: string;
              type:
                | "text"
                | "email"
                | "phone"
                | "url"
                | "textarea"
                | "select"
                | "dynamic-select";
              value: string | null | undefined;
              required: boolean;
              placeholder: string;
              showEmpty: boolean;
              options?: Array<{
                value: string;
                label: string;
              }>;
            }>
          }
        />
      )}
      {/* Assignment & Status Card */}
      <InfoCardWrapper
        title="Assignment & Status"
        leadId={leadId}
        formFields={[
          {
            name: "status",
            label: "Status",
            type: "dynamic-select",
            value: lead.status ? normalizeStatus(lead.status) : "",
            displayValue: lead.status ? formatStatus(lead.status) : null,
            required: true,
            dynamicKey: "lead-status",
            placeholder: "Select status",
          },
          {
            name: "userId",
            label: "Assigned To",
            type: "dynamic-select",
            value: lead.userId,
            displayValue: lead?.user?.name || lead?.user?.email || "Unassigned",
            showEmpty: true,
            dynamicKey: "tenant-members",
            placeholder: "Select team member",
          },
        ]}
      />

      {/* Lead Qualification Card */}
      <InfoCardWrapper title="Lead Qualification">
        <Typography
          type="label"
          className="text-muted-foreground font-medium mb-2 text-xs"
        >
          Lead Score
        </Typography>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span
              className={`text-sm font-bold ${getLeadScoreColor(leadScore)}`}
            >
              {leadScore}/100
            </span>
            <Badge
              variant="outline"
              size="xs"
              className={`ml-auto ${
                leadScore >= 80
                  ? "bg-green-500/10 text-green-600 border-green-500/20"
                  : leadScore >= 60
                    ? "bg-yellow-500/10 text-yellow-600 border-yellow-500/20"
                    : leadScore >= 40
                      ? "bg-orange-500/10 text-orange-600 border-orange-500/20"
                      : "bg-red-500/10 text-red-600 border-red-500/20"
              }`}
            >
              {leadScore >= 80
                ? "Excellent"
                : leadScore >= 60
                  ? "Good"
                  : leadScore >= 40
                    ? "Fair"
                    : "Poor"}
            </Badge>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-500 ${
                leadScore >= 80
                  ? "bg-green-500"
                  : leadScore >= 60
                    ? "bg-yellow-500"
                    : leadScore >= 40
                      ? "bg-orange-500"
                      : "bg-red-500"
              }`}
              style={{ width: `${leadScore}%` }}
            />
          </div>
        </div>

        {/* Priority and Lead Type */}
        <div className="space-y-1">
          {lead.priority && (
            <div className="flex items-center justify-between py-1">
              <div className="flex items-center">
                <Typography
                  type="label"
                  className="text-muted-foreground text-xs"
                >
                  Priority
                </Typography>
              </div>
              <Badge
                variant="outline"
                size="xs"
                className={getPriorityColor(lead.priority)}
              >
                {lead.priority} Priority
              </Badge>
            </div>
          )}

          <div className="flex items-center justify-between py-1">
            <div className="flex items-center">
              <Typography
                type="label"
                className="text-muted-foreground text-xs"
              >
                Lead Type
              </Typography>
            </div>
            <Typography type="label" className="font-medium text-xs">
              {formatLeadType(lead.isB2B)}
            </Typography>
          </div>
        </div>

        {/* Tags Section */}
        {lead.tags && lead.tags.length > 0 && (
          <div>
            <Typography
              type="label"
              className="text-muted-foreground font-medium mb-2 text-xs"
            >
              Tags & Labels
            </Typography>
            <div className="flex flex-wrap gap-1.5">
              {lead.tags.map((tag: string, index: number) => (
                <Badge key={index} variant="secondary" size="xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </InfoCardWrapper>

      {/* Activity Log Card */}
      <InfoCardWrapper title="Activity Log">
        <CompactPageClient
          entityType="Lead"
          entityId={leadId}
          activityLogs={activityLogs}
          userId={userId}
        />
      </InfoCardWrapper>
    </div>
  );
}
