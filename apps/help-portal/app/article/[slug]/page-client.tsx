"use client";

import { useState } from "react";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { Button } from "@flinkk/components/ui/button";
import { toast } from "react-hot-toast";
import { getSessionInfo } from "@/lib/session-utils";

interface Article {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  viewCount: number;
  publishedAt: string;
  tags: string[];
  seoTitle?: string;
  seoDescription?: string;
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  author: {
    id: string;
    name: string;
    image?: string;
  };
  votes: Vote[];
  _count: {
    votes: number;
  };
}

interface Vote {
  voteType: "HELPFUL" | "NOT_HELPFUL";
  comment?: string;
  createdAt: string;
  user: {
    name: string;
  };
}

export default function ArticleDetailPageClient({
  article,
}: {
  article: Article;
}) {
  const [userVote, setUserVote] = useState<"HELPFUL" | "NOT_HELPFUL" | null>(
    null
  );

  const handleVote = async (voteType: "HELPFUL" | "NOT_HELPFUL") => {
    if (!article) return;

    try {
      const sessionInfo = getSessionInfo();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_HELP_PORTAL_APP_URL}/api/vote`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            contentType: "ARTICLE",
            contentId: article.id,
            voteType,
            ...(sessionInfo.isAnonymous && {
              sessionId: sessionInfo.sessionId,
            }),
          }),
        }
      );

      if (response.ok) {
        setUserVote(voteType);

        // Show success toast
        toast.success(
          voteType === "HELPFUL"
            ? "Thank you for your feedback! 😊"
            : "Thank you for your feedback. We'll work to improve this article."
        );
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to submit your feedback");
      }
    } catch (error) {
      console.error("Error submitting vote:", error);
      toast.error("Unable to submit feedback. Please try again.");
    }
  };
  if (!article) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Article not found
          </h2>
          <p className="text-gray-600">
            The article you're looking for doesn't exist.
          </p>
          <Link href="">
            <Button variant="outline">Back to Help Portal</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Breadcrumb */}
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="" className="hover:text-blue-600">
            Help
          </Link>
          <ChevronRight className="h-4 w-4" />
          {article.category && (
            <>
              <Link
                href={`/category/${article.category.slug}`}
                className="hover:text-blue-600"
              >
                {article.category.name}
              </Link>
              <ChevronRight className="h-4 w-4" />
            </>
          )}
          <span className="text-gray-900">{article.title}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Article Content */}
          <div className="lg:col-span-3">
            <div className="space-y-6">
              {/* Article Title */}
              <div className="space-y-2">
                <h1 className="text-3xl font-bold text-gray-900">
                  {article.title}
                </h1>
                <p className="text-sm text-gray-500">
                  Last updated{" "}
                  {new Date(article?.publishedAt).toLocaleDateString()}
                </p>
              </div>

              {/* Article Content */}
              <div className="prose prose-lg max-w-none">
                <ReactMarkdown className="markdown break-words">
                  {article?.content}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Section */}
      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-6">
            How helpful was this article?
          </h3>

          {userVote ? (
            <div className="space-y-4">
              <p className="text-green-600 font-medium">
                Thank you for your feedback!
                {userVote === "HELPFUL"
                  ? " 😊"
                  : " We'll work to improve this article."}
              </p>
              <p className="text-sm text-gray-500">
                You can change your rating by clicking a different option below.
              </p>
            </div>
          ) : (
            <p className="text-gray-600 mb-6">
              Your feedback helps us improve our content.
            </p>
          )}

          <div className="flex justify-center space-x-4 mt-6">
            <button
              onClick={() => handleVote("NOT_HELPFUL")}
              className={`text-3xl hover:scale-110 transition-all duration-200 p-2 rounded-lg ${
                userVote === "NOT_HELPFUL"
                  ? "bg-red-100 border-2 border-red-300 scale-110"
                  : "hover:bg-gray-100"
              }`}
              title="Very unhelpful"
            >
              😞
            </button>

            <button
              onClick={() => handleVote("HELPFUL")}
              className={`text-3xl hover:scale-110 transition-all duration-200 p-2 rounded-lg ${
                userVote === "HELPFUL"
                  ? "bg-green-100 border-2 border-green-300 scale-110"
                  : "hover:bg-gray-100"
              }`}
              title="Very helpful"
            >
              🤩
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
