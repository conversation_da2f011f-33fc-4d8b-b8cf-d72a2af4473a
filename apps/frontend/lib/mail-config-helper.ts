import {prisma} from "@flinkk/database/prisma";
import { createFlinkkMailAPI } from "@flinkk/mail-api";

/**
 * Create a mail API instance using the stored mail configuration
 * @param tenantId Tenant ID to get configuration for
 * @returns Object with FlinkkMailAPI instance and mailTenantId, or null if no configuration found
 */
export async function createMailAPIFromConfig(tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error("Tenant ID is required");
    }

    // Get the mail configuration for this tenant
    const mailConfig = await prisma.mailConfiguration.findUnique({
      where: {
        tenantId: tenantId,
        isActive: true,
      },
      select: {
        mailUrl: true,
        mailTenantId: true,
      },
    });

    if (!mailConfig) {
      return null;
    }

    // Create and return the mail API instance with the stored configuration
    try {
      const mailAPI = createFlinkkMailAPI({
        apiUrl: mailConfig.mailUrl,
        // Note: We're not storing API keys, so this will use environment variables if needed
      });

      return {
        mailAPI,
        mailTenantId: mailConfig.mailTenantId,
      };
    } catch (apiError) {
      console.error("Failed to create mail API instance:", apiError);
      return null;
    }
  } catch (error) {
    console.error("Error creating mail API from config:", error);
    return null;
  }
}
