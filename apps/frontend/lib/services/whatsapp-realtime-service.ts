import React from "react";
import { io, Socket } from "socket.io-client";

interface WhatsAppRealtimeConfig {
  conversationId: string;
  tenantId: string;
  userId: string;
  onMessageReceived?: (message: any) => void;
  onMessageStatusUpdate?: (update: any) => void;
  onTypingIndicator?: (typing: any) => void;
  onConnectionChange?: (connected: boolean) => void;
}

interface WhatsAppMessage {
  id: string;
  content: string;
  contentType: string;
  isIncoming: boolean;
  status: string;
  createdAt: string;
  meta?: any;
  sender?: any;
}

interface MessageStatusUpdate {
  messageId: string;
  whatsappMessageId: string;
  status: "SENT" | "DELIVERED" | "READ" | "FAILED";
  timestamp: string;
}

export class WhatsAppRealtimeService {
  private socket: Socket | null = null;
  private config: WhatsAppRealtimeConfig;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isConnected = false;

  constructor(config: WhatsAppRealtimeConfig) {
    this.config = config;
    this.connect();
  }

  private connect() {
    try {
      // Use the same Azure Web PubSub URL as existing socket connections
      const azurePubSubUrl = "https://flinkk-crm-dev.webpubsub.azure.com";

      this.socket = io(azurePubSubUrl, {
        path: "/clients/socketio/hubs/Hub",
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000,
        autoConnect: true,
        transports: ["websocket", "polling"],
        query: {
          conversationId: this.config.conversationId,
          tenantId: this.config.tenantId,
          userId: this.config.userId,
          type: "whatsapp_realtime",
        },
      });

      this.setupEventHandlers();
    } catch (error) {
      console.error("Failed to connect to WhatsApp realtime service:", error);
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    // Connection events
    this.socket.on("connect", () => {
      console.log("WhatsApp realtime service connected");
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.config.onConnectionChange?.(true);

      // Join the conversation room for WhatsApp updates
      this.socket?.emit("join_whatsapp_conversation", {
        conversationId: this.config.conversationId,
        tenantId: this.config.tenantId,
        userId: this.config.userId,
      });
    });

    this.socket.on("disconnect", () => {
      console.log("WhatsApp realtime service disconnected");
      this.isConnected = false;
      this.config.onConnectionChange?.(false);
    });

    this.socket.on("connect_error", (error) => {
      console.error("WhatsApp realtime connection error:", error);
      this.reconnectAttempts++;

      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error("Max reconnection attempts reached");
        this.config.onConnectionChange?.(false);
      }
    });

    // WhatsApp-specific events
    this.socket.on("whatsapp_message_received", (data: WhatsAppMessage) => {
      console.log("WhatsApp message received:", data);
      this.config.onMessageReceived?.(data);
    });

    this.socket.on("whatsapp_message_sent", (data: WhatsAppMessage) => {
      console.log("WhatsApp message sent:", data);
      this.config.onMessageReceived?.(data);
    });

    this.socket.on(
      "whatsapp_message_status_update",
      (data: MessageStatusUpdate) => {
        console.log("WhatsApp message status update:", data);
        this.config.onMessageStatusUpdate?.(data);
      }
    );

    this.socket.on("whatsapp_typing_indicator", (data: any) => {
      console.log("WhatsApp typing indicator:", data);
      this.config.onTypingIndicator?.(data);
    });

    // General message events (for compatibility with existing system)
    this.socket.on("new_message", (data: any) => {
      // Check if this is a WhatsApp message
      if (data.meta?.whatsapp_message_id) {
        console.log("New WhatsApp message via general event:", data);
        this.config.onMessageReceived?.(data);
      }
    });

    this.socket.on("message_status_update", (data: any) => {
      // Check if this is a WhatsApp message status update
      if (data.whatsappMessageId) {
        console.log("WhatsApp message status updated via general event:", data);
        this.config.onMessageStatusUpdate?.(data);
      }
    });
  }

  // Send a WhatsApp message through the socket
  public sendMessage(
    content: string,
    replyToMessageId?: string
  ): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.socket || !this.isConnected) {
        console.error("WhatsApp realtime service not connected");
        resolve(false);
        return;
      }

      this.socket.emit(
        "send_whatsapp_message",
        {
          conversationId: this.config.conversationId,
          content,
          replyToMessageId,
          tenantId: this.config.tenantId,
          userId: this.config.userId,
        },
        (response: any) => {
          if (response.success) {
            console.log("WhatsApp message sent successfully");
            resolve(true);
          } else {
            console.error("Failed to send WhatsApp message:", response.error);
            resolve(false);
          }
        }
      );
    });
  }

  // Send typing indicator
  public sendTypingIndicator(isTyping: boolean) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit("whatsapp_typing", {
      conversationId: this.config.conversationId,
      isTyping,
      tenantId: this.config.tenantId,
      userId: this.config.userId,
    });
  }

  // Request message status update
  public requestStatusUpdate(messageId: string, whatsappMessageId: string) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit("request_whatsapp_status", {
      messageId,
      whatsappMessageId,
      conversationId: this.config.conversationId,
      tenantId: this.config.tenantId,
    });
  }

  // Update configuration
  public updateConfig(newConfig: Partial<WhatsAppRealtimeConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  // Get connection status
  public getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      socketId: this.socket?.id,
    };
  }

  // Disconnect and cleanup
  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.config.onConnectionChange?.(false);
  }

  // Reconnect manually
  public reconnect() {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }
}

// Hook for using WhatsApp realtime service in React components
export function useWhatsAppRealtime(config: WhatsAppRealtimeConfig) {
  const serviceRef = React.useRef<WhatsAppRealtimeService | null>(null);
  const [isConnected, setIsConnected] = React.useState(false);
  const [messages, setMessages] = React.useState<WhatsAppMessage[]>([]);

  React.useEffect(() => {
    // Create service instance
    serviceRef.current = new WhatsAppRealtimeService({
      ...config,
      onConnectionChange: (connected) => {
        setIsConnected(connected);
        config.onConnectionChange?.(connected);
      },
      onMessageReceived: (message) => {
        setMessages((prev) => {
          // Avoid duplicates
          const exists = prev.find((m) => m.id === message.id);
          if (exists) return prev;
          return [...prev, message];
        });
        config.onMessageReceived?.(message);
      },
      onMessageStatusUpdate: (update) => {
        setMessages((prev) =>
          prev.map((msg) => {
            if (msg.meta?.whatsapp_message_id === update.whatsappMessageId) {
              return { ...msg, status: update.status };
            }
            return msg;
          })
        );
        config.onMessageStatusUpdate?.(update);
      },
    });

    // Cleanup on unmount
    return () => {
      serviceRef.current?.disconnect();
    };
  }, [config.conversationId, config.tenantId, config.userId]);

  return {
    isConnected,
    messages,
    sendMessage: (content: string, replyToMessageId?: string) =>
      serviceRef.current?.sendMessage(content, replyToMessageId) ||
      Promise.resolve(false),
    sendTypingIndicator: (isTyping: boolean) =>
      serviceRef.current?.sendTypingIndicator(isTyping),
    requestStatusUpdate: (messageId: string, whatsappMessageId: string) =>
      serviceRef.current?.requestStatusUpdate(messageId, whatsappMessageId),
    reconnect: () => serviceRef.current?.reconnect(),
    getStatus: () => serviceRef.current?.getConnectionStatus(),
  };
}
