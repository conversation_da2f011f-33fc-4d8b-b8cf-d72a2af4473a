import { prisma } from "@flinkk/database/prisma";

export interface WhatsAppSettings {
  whatsappProvider: "whatsapp_cloud" | "360dialog";
  phoneNumber: string;
  apiKey: string;
  phoneNumberId?: string;
  businessAccountId?: string;
  webhookVerifyToken?: string;
}

export interface WhatsAppInboxConfig {
  inboxId: string;
  settings: WhatsAppSettings;
  tenantId: string;
}

export interface WhatsAppMessage {
  to: string;
  text: string;
  conversationId: string;
  replyToMessageId?: string;
}

export class WhatsAppInboxService {
  /**
   * Configure WhatsApp inbox settings
   */
  static async configureWhatsAppInbox(
    inboxId: string,
    settings: WhatsAppSettings,
    tenantId: string
  ): Promise<{ success: boolean; error?: string; webhookUrl?: string }> {
    try {
      // Validate required fields based on provider
      const validationResult = this.validateWhatsAppSettings(settings);
      if (!validationResult.isValid) {
        return { success: false, error: validationResult.error };
      }

      // Generate webhook verify token for WhatsApp Cloud API
      let webhookVerifyToken = settings.webhookVerifyToken;
      if (
        settings.whatsappProvider === "whatsapp_cloud" &&
        !webhookVerifyToken
      ) {
        webhookVerifyToken = this.generateWebhookVerifyToken();
      }

      // Update inbox with WhatsApp configuration
      const updatedInbox = await prisma.inbox.update({
        where: { id: inboxId },
        data: {
          settings: {
            ...settings,
            webhookVerifyToken,
          },
        },
      });

      // Generate webhook URL
      const webhookUrl = this.generateWebhookUrl(settings.phoneNumber);

      return {
        success: true,
        webhookUrl,
      };
    } catch (error) {
      console.error("WhatsApp inbox configuration error:", error);
      return {
        success: false,
        error: "Failed to configure WhatsApp inbox",
      };
    }
  }

  /**
   * Test WhatsApp connection
   */
  static async testWhatsAppConnection(
    settings: WhatsAppSettings
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Validate settings first
      const validationResult = this.validateWhatsAppSettings(settings);
      if (!validationResult.isValid) {
        return { success: false, error: validationResult.error };
      }

      // Test connection based on provider
      if (settings.whatsappProvider === "whatsapp_cloud") {
        return await this.testWhatsAppCloudConnection(settings);
      } else {
        return await this.test360DialogConnection(settings);
      }
    } catch (error) {
      console.error("WhatsApp connection test error:", error);
      return {
        success: false,
        error: "Failed to test WhatsApp connection",
      };
    }
  }

  /**
   * Test WhatsApp Cloud API connection
   */
  private static async testWhatsAppCloudConnection(
    settings: WhatsAppSettings
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { apiKey, phoneNumberId, businessAccountId } = settings;

      // Test API access by fetching business account info first
      const businessResponse = await fetch(
        `https://graph.facebook.com/v21.0/${businessAccountId}?fields=id,name`,
        {
          headers: {
            Authorization: `Bearer ${apiKey}`,
          },
        }
      );

      if (!businessResponse.ok) {
        const errorData = await businessResponse.json();
        return {
          success: false,
          error: `WhatsApp Cloud API error: ${errorData.error?.message || "Invalid business account ID or access token"}`,
        };
      }

      // Test phone number access
      const phoneResponse = await fetch(
        `https://graph.facebook.com/v21.0/${phoneNumberId}?fields=id,display_phone_number,verified_name`,
        {
          headers: {
            Authorization: `Bearer ${apiKey}`,
          },
        }
      );

      if (!phoneResponse.ok) {
        const phoneErrorData = await phoneResponse.json();
        console.log("Phone Response Error:", phoneErrorData);
        return {
          success: false,
          error: `WhatsApp Cloud API error: ${phoneErrorData.error?.message || "Invalid phone number ID or insufficient permissions"}`,
        };
      }

      const phoneData = await phoneResponse.json();

      console.log("Phone Data:", phoneData);

      // Verify phone number matches (optional check, as display format may vary)
      const normalizedInputPhone = settings.phoneNumber.replace(/[^\d]/g, "");
      const normalizedApiPhone = phoneData.display_phone_number?.replace(
        /[^\d]/g,
        ""
      );

      if (normalizedApiPhone && normalizedInputPhone !== normalizedApiPhone) {
        return {
          success: false,
          error: `Phone number mismatch: Expected ${settings.phoneNumber}, but API returned ${phoneData.display_phone_number}`,
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error:
          "Failed to connect to WhatsApp Cloud API. Please check your network connection.",
      };
    }
  }

  /**
   * Test 360Dialog connection
   */
  private static async test360DialogConnection(
    settings: WhatsAppSettings
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { apiKey } = settings;
      const baseUrl =
        process.env.DIALOG_360_BASE_URL || "https://waba.360dialog.io/v1";

      // Test API access by fetching account info
      const response = await fetch(`${baseUrl}/configs/about`, {
        headers: {
          "D360-API-KEY": apiKey,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: `360Dialog API error: ${errorData.error?.message || "Invalid API key"}`,
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: "Failed to connect to 360Dialog API",
      };
    }
  }

  /**
   * Validate WhatsApp settings
   */
  private static validateWhatsAppSettings(settings: WhatsAppSettings): {
    isValid: boolean;
    error?: string;
  } {
    const { whatsappProvider, phoneNumber, apiKey } = settings;

    if (!phoneNumber) {
      return { isValid: false, error: "Phone number is required" };
    }

    if (!apiKey) {
      return { isValid: false, error: "API key is required" };
    }

    // Validate phone number format (E.164)
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    if (!phoneRegex.test(phoneNumber)) {
      return {
        isValid: false,
        error: "Phone number must be in E.164 format (e.g., +**********)",
      };
    }

    // Provider-specific validation
    if (whatsappProvider === "whatsapp_cloud") {
      if (!settings.phoneNumberId) {
        return {
          isValid: false,
          error: "Phone Number ID is required for WhatsApp Cloud API",
        };
      }
      if (!settings.businessAccountId) {
        return {
          isValid: false,
          error: "Business Account ID is required for WhatsApp Cloud API",
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Generate webhook verify token
   */
  private static generateWebhookVerifyToken(): string {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }

  /**
   * Generate webhook URL for WhatsApp
   */
  private static generateWebhookUrl(phoneNumber: string): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const cleanPhoneNumber = phoneNumber.replace("+", "");
    return `${baseUrl}/api/webhooks/whatsapp/${cleanPhoneNumber}`;
  }

  /**
   * Send WhatsApp message
   */
  static async sendMessage(
    inboxId: string,
    message: WhatsAppMessage
  ): Promise<{ success: boolean; error?: string; messageId?: string }> {
    try {
      // Get inbox configuration
      const inbox = await prisma.inbox.findUnique({
        where: { id: inboxId },
        select: { settings: true, type: true },
      });

      if (!inbox || inbox.type !== "WHATSAPP") {
        return { success: false, error: "Invalid WhatsApp inbox" };
      }

      const settings = inbox.settings as WhatsAppSettings;

      // Send message based on provider
      if (settings.whatsappProvider === "whatsapp_cloud") {
        return await this.sendWhatsAppCloudMessage(settings, message);
      } else {
        return await this.send360DialogMessage(settings, message);
      }
    } catch (error) {
      console.error("Error sending WhatsApp message:", error);
      return { success: false, error: "Failed to send message" };
    }
  }

  /**
   * Send message via WhatsApp Cloud API
   */
  private static async sendWhatsAppCloudMessage(
    settings: WhatsAppSettings,
    message: WhatsAppMessage
  ): Promise<{ success: boolean; error?: string; messageId?: string }> {
    try {
      const payload: any = {
        messaging_product: "whatsapp",
        to: message.to,
        type: "text",
        text: {
          body: message.text,
        },
      };

      // Add context for replies
      if (message.replyToMessageId) {
        payload.context = {
          message_id: message.replyToMessageId,
        };
      }

      const response = await fetch(
        `https://graph.facebook.com/v18.0/${settings.phoneNumberId}/messages`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${settings.apiKey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        let errorMessage = errorData.error?.message || "Failed to send message";

        // Handle specific WhatsApp API error codes
        if (errorMessage.includes("#131030")) {
          errorMessage =
            "Phone number not in allowed list. Please add the recipient's phone number to your WhatsApp Business Account's allowed list in development mode.";
        } else if (errorMessage.includes("#131031")) {
          errorMessage =
            "Phone number not registered with WhatsApp Business API.";
        } else if (errorMessage.includes("#131032")) {
          errorMessage = "Phone number is not a valid WhatsApp number.";
        } else if (errorMessage.includes("#131033")) {
          errorMessage = "Message template not approved or doesn't exist.";
        } else if (errorMessage.includes("rate limit")) {
          errorMessage =
            "WhatsApp API rate limit exceeded. Please try again later.";
        }

        return {
          success: false,
          error: `WhatsApp Cloud API error: ${errorMessage}`,
        };
      }

      const responseData = await response.json();
      return {
        success: true,
        messageId: responseData.messages?.[0]?.id,
      };
    } catch (error) {
      return {
        success: false,
        error: "Failed to send message via WhatsApp Cloud API",
      };
    }
  }

  /**
   * Send message via 360Dialog
   */
  private static async send360DialogMessage(
    settings: WhatsAppSettings,
    message: WhatsAppMessage
  ): Promise<{ success: boolean; error?: string; messageId?: string }> {
    try {
      const baseUrl =
        process.env.DIALOG_360_BASE_URL || "https://waba.360dialog.io/v1";

      const payload: any = {
        to: message.to,
        type: "text",
        text: {
          body: message.text,
        },
      };

      // Add context for replies
      if (message.replyToMessageId) {
        payload.context = {
          message_id: message.replyToMessageId,
        };
      }

      const response = await fetch(`${baseUrl}/messages`, {
        method: "POST",
        headers: {
          "D360-API-KEY": settings.apiKey,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: `360Dialog API error: ${errorData.error?.message || "Failed to send message"}`,
        };
      }

      const responseData = await response.json();
      return {
        success: true,
        messageId: responseData.id,
      };
    } catch (error) {
      return {
        success: false,
        error: "Failed to send message via 360Dialog",
      };
    }
  }

  /**
   * Get WhatsApp inbox configuration
   */
  static async getWhatsAppConfig(
    inboxId: string
  ): Promise<WhatsAppSettings | null> {
    try {
      const inbox = await prisma.inbox.findUnique({
        where: { id: inboxId },
        select: { settings: true, type: true },
      });

      if (!inbox || inbox.type !== "WHATSAPP") {
        return null;
      }

      return inbox.settings as WhatsAppSettings;
    } catch (error) {
      console.error("Error fetching WhatsApp config:", error);
      return null;
    }
  }
}
