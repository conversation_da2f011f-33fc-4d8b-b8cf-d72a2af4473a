import { prisma } from "@flinkk/database/prisma";
import { WhatsAppInboxService } from "./whatsapp-inbox-service";
import { WhatsAppAIChatbotService } from "./whatsapp-ai-chatbot-service";
import { conversationEventsService } from "./conversation-events-service";

export interface WhatsAppWebhookPayload {
  object: string;
  entry: Array<{
    id: string;
    changes: Array<{
      value: {
        messaging_product: string;
        metadata: {
          display_phone_number: string;
          phone_number_id: string;
        };
        contacts?: Array<{
          profile: {
            name: string;
          };
          wa_id: string;
        }>;
        messages?: Array<{
          from: string;
          id: string;
          timestamp: string;
          text?: {
            body: string;
          };
          type: string;
          context?: {
            from: string;
            id: string;
          };
        }>;
        statuses?: Array<{
          id: string;
          status: string;
          timestamp: string;
          recipient_id: string;
        }>;
      };
      field: string;
    }>;
  }>;
}

export class WhatsAppWebhookService {
  private static socketConnection: any = null;

  /**
   * Emit real-time event via HTTP request to socket server
   */
  private static async emitSocketEvent(room: string, event: string, data: any) {
    try {
      // Use the socket server URL for broadcasting
      const socketServerUrl =
        process.env.SOCKET_SERVER_URL || "http://localhost:4000";

      console.log(
        `Attempting to emit ${event} to room ${room} via ${socketServerUrl}`
      );

      const response = await fetch(`${socketServerUrl}/broadcast`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          room,
          event,
          data,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`Successfully emitted ${event} to room ${room}:`, result);
      } else {
        const errorText = await response.text();
        console.error(
          `Failed to emit ${event} to room ${room}:`,
          response.status,
          response.statusText,
          errorText
        );
      }
    } catch (error) {
      console.error("Failed to emit socket event:", error);
    }
  }

  /**
   * Emit real-time event for new WhatsApp message
   */
  private static async emitMessageEvent(
    conversation: any,
    message: any,
    isIncoming: boolean
  ) {
    try {
      const eventData = {
        id: message.id,
        content: message.content,
        contentType: message.contentType,
        isIncoming,
        status: message.status,
        createdAt: message.createdAt,
        conversationId: conversation.id,
        tenantId: conversation.tenantId,
        meta: message.meta,
        sender: message.sender,
      };

      // Emit to conversation room via HTTP
      await this.emitSocketEvent(
        `conversation:${conversation.id}`,
        "new_message",
        eventData
      );

      console.log(
        "Emitted real-time message event for conversation:",
        conversation.id
      );
    } catch (error) {
      console.error("Failed to emit message event:", error);
    }
  }

  /**
   * Emit real-time event for message status update
   */
  private static async emitStatusUpdateEvent(
    message: any,
    oldStatus: string,
    newStatus: string
  ) {
    try {
      const eventData = {
        messageId: message.id,
        whatsappMessageId: message.meta?.whatsapp_message_id,
        oldStatus,
        newStatus,
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
      };

      // Emit to conversation room via HTTP
      await this.emitSocketEvent(
        `conversation:${message.conversationId}`,
        "message_status_update",
        eventData
      );

      console.log("Emitted status update event for message:", message.id);
    } catch (error) {
      console.error("Failed to emit status update event:", error);
    }
  }

  /**
   * Process incoming WhatsApp webhook
   */
  static async processWebhook(
    inbox: any,
    payload: WhatsAppWebhookPayload
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log("Processing WhatsApp webhook for inbox:", inbox.id);

      // Validate payload structure
      if (!payload.entry || !Array.isArray(payload.entry)) {
        return { success: false, error: "Invalid webhook payload structure" };
      }

      for (const entry of payload.entry) {
        if (!entry.changes || !Array.isArray(entry.changes)) {
          continue;
        }

        for (const change of entry.changes) {
          if (change.field === "messages") {
            await this.processMessageChange(inbox, change.value);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error("WhatsApp webhook processing error:", error);
      return { success: false, error: "Failed to process webhook" };
    }
  }

  /**
   * Process message changes from webhook
   */
  private static async processMessageChange(inbox: any, value: any) {
    try {
      // Process incoming messages
      if (value.messages && Array.isArray(value.messages)) {
        for (const message of value.messages) {
          await this.processIncomingMessage(inbox, message, value.contacts);
        }
      }

      // Process message status updates
      if (value.statuses && Array.isArray(value.statuses)) {
        for (const status of value.statuses) {
          await this.processMessageStatus(inbox, status);
        }
      }
    } catch (error) {
      console.error("Error processing message change:", error);
    }
  }

  /**
   * Process incoming WhatsApp message
   */
  private static async processIncomingMessage(
    inbox: any,
    message: any,
    contacts: any[]
  ) {
    try {
      console.log("Processing incoming WhatsApp message:", message.id);

      // Find or create contact
      const contact = await this.findOrCreateContact(
        inbox.tenantId,
        message.from,
        contacts
      );

      // Find or create conversation
      const conversation = await this.findOrCreateConversation(
        inbox,
        contact,
        message.from
      );

      // Create message record
      const createdMessage = await this.createMessage(
        conversation,
        message,
        true
      );

      // Emit real-time event for new message
      await this.emitMessageEvent(conversation, createdMessage, true);

      // Add to events service for SSE
      conversationEventsService.addNewMessageEvent(conversation.id, {
        id: createdMessage.id,
        content: createdMessage.content,
        contentType: createdMessage.contentType,
        isIncoming: true,
        status: createdMessage.status,
        createdAt: createdMessage.createdAt,
        conversationId: conversation.id,
        tenantId: conversation.tenantId,
        meta: createdMessage.meta,
        sender: createdMessage.sender,
        attachments: createdMessage.attachments,
      });

      // Process AI chatbot response
      await this.processAIChatbotResponse(conversation, createdMessage, {
        conversation,
        contact,
        inbox,
        tenant: inbox.tenant,
        recentMessages: await this.getRecentMessages(conversation.id),
      });

      console.log(
        "Successfully processed incoming WhatsApp message:",
        message.id
      );
    } catch (error) {
      console.error("Error processing incoming message:", error);
    }
  }

  /**
   * Process message status updates
   */
  private static async processMessageStatus(inbox: any, status: any) {
    try {
      console.log(
        "Processing WhatsApp message status:",
        status.id,
        status.status
      );

      // Find the message by WhatsApp message ID
      const messages = await prisma.message.findMany({
        where: {
          tenantId: inbox.tenantId,
        },
      });

      const message = messages.find(
        (msg: any) => msg?.meta?.whatsapp_message_id === status?.id
      );

      if (!message) {
        console.log("Message not found for status update:", status.id);
        return;
      }

      // Update message status
      const newStatus = this.mapWhatsAppStatus(status.status);
      const oldStatus = message.status;

      await prisma.message.update({
        where: { id: message.id },
        data: {
          status: newStatus,
          updatedAt: new Date(),
        },
      });

      // Emit real-time event for status update
      await this.emitStatusUpdateEvent(message, oldStatus, newStatus);

      // Add to events service for SSE
      conversationEventsService.addMessageStatusUpdateEvent(
        message.conversationId,
        {
          messageId: message.id,
          whatsappMessageId: message.meta?.whatsapp_message_id,
          oldStatus,
          newStatus,
          timestamp: new Date().toISOString(),
          conversationId: message.conversationId,
        }
      );

      console.log(
        "Successfully updated message status:",
        status.id,
        "to",
        newStatus
      );

      // Log status update activity
      // await prisma.activityLog.create({
      //   data: {
      //     action: "WHATSAPP_MESSAGE_STATUS_UPDATED",
      //     entityType: "MESSAGE",
      //     entityId: message.id,
      //     tenantId: inbox.tenantId,
      //     meta: {
      //       whatsappMessageId: status.id,
      //       oldStatus: message.status,
      //       newStatus,
      //       statusTimestamp: status.timestamp,
      //     },
      //     createdAt: new Date(),
      //     updatedAt: new Date(),
      //   },
      // });
    } catch (error) {
      console.error("Error processing message status:", error);
    }
  }

  /**
   * Find or create contact from WhatsApp data
   */
  private static async findOrCreateContact(
    tenantId: string,
    phoneNumber: string,
    contacts: any[]
  ) {
    try {
      // Try to find existing contact by phone number
      let contact = await prisma.contact.findFirst({
        where: {
          tenantId,
          phoneNumber,
        },
      });

      if (!contact) {
        // Get contact info from webhook data
        const contactInfo = contacts?.find((c) => c.wa_id === phoneNumber);
        const name =
          contactInfo?.profile?.name || `WhatsApp User ${phoneNumber}`;

        // Create new contact
        contact = await prisma.contact.create({
          data: {
            firstName: name?.split(" ")[0] || "",
            lastName: name?.split(" ").slice(1).join(" ") || "",
            phoneNumber,
            tenantId,
            email: `${phoneNumber}@flinkk.io`,
            source: "WHATSAPP",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        console.log("Created new WhatsApp contact:", contact.id);
      }

      return contact;
    } catch (error) {
      console.error("Error finding/creating contact:", error);
      throw error;
    }
  }

  /**
   * Find or create conversation
   */
  private static async findOrCreateConversation(
    inbox: any,
    contact: any,
    phoneNumber: string
  ) {
    try {
      // Try to find existing open conversation first
      let conversation = await prisma.conversation.findFirst({
        where: {
          tenantId: inbox.tenantId,
          inboxId: inbox.id,
          contactId: contact.id,
          status: "OPEN",
        },
        orderBy: {
          lastMessageAt: "desc",
        },
      });

      // If no open conversation, check for recent closed conversations (within 24 hours)
      if (!conversation) {
        const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

        conversation = await prisma.conversation.findFirst({
          where: {
            tenantId: inbox.tenantId,
            inboxId: inbox.id,
            contactId: contact.id,
            lastMessageAt: {
              gte: twentyFourHoursAgo,
            },
          },
          orderBy: {
            lastMessageAt: "desc",
          },
        });

        // Reopen the conversation if found
        if (conversation) {
          conversation = await prisma.conversation.update({
            where: { id: conversation.id },
            data: {
              status: "OPEN",
              updatedAt: new Date(),
            },
          });
          console.log("Reopened WhatsApp conversation:", conversation.id);
        }
      }

      if (!conversation) {
        // Create new conversation with enhanced metadata
        conversation = await prisma.conversation.create({
          data: {
            subject: `WhatsApp conversation with ${contact.firstName}`,
            status: "OPEN",
            priority: 0,
            tenantId: inbox.tenantId,
            inboxId: inbox.id,
            contactId: contact.id,
            lastMessageAt: new Date(),
            meta: {
              channel: "WHATSAPP",
              whatsapp_phone: phoneNumber,
              inbox_name: inbox.name,
              contact_source: "WHATSAPP",
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        console.log("Created new WhatsApp conversation:", conversation.id);
      }

      return conversation;
    } catch (error) {
      console.error("Error finding/creating conversation:", error);
      throw error;
    }
  }

  /**
   * Create message record
   */
  private static async createMessage(
    conversation: any,
    messageData: any,
    isIncoming: boolean
  ) {
    try {
      // Check if message already exists to prevent duplicates
      const existingMessages = await prisma.message.findMany({
        where: {
          tenantId: conversation.tenantId,
        },
      });
      const existingMessage = existingMessages.find(
        (msg: any) => msg?.meta?.whatsapp_message_id === messageData?.id
      );

      if (existingMessage) {
        console.log("Message already exists, skipping:", messageData.id);
        return existingMessage;
      }

      // Extract content based on message type
      let content = "";
      let contentType = "text";
      let attachments = [];

      switch (messageData.type) {
        case "text":
          content = messageData.text?.body || "";
          contentType = "text";
          break;
        case "image":
          content = messageData.image?.caption || "[Image]";
          contentType = "image";
          attachments.push({
            type: "image",
            mediaId: messageData.image?.id, // WhatsApp media ID
            mimeType: messageData.image?.mime_type,
            sha256: messageData.image?.sha256,
            id: messageData.image?.id, // Keep for backward compatibility
          });
          break;
        case "document":
          content =
            messageData.document?.caption ||
            messageData.document?.filename ||
            "[Document]";
          contentType = "document";
          attachments.push({
            type: "document",
            mediaId: messageData.document?.id, // WhatsApp media ID
            filename: messageData.document?.filename,
            mimeType: messageData.document?.mime_type,
            sha256: messageData.document?.sha256,
            id: messageData.document?.id, // Keep for backward compatibility
          });
          break;
        case "audio":
          content = "[Audio message]";
          contentType = "audio";
          attachments.push({
            type: "audio",
            mediaId: messageData.audio?.id, // WhatsApp media ID
            mimeType: messageData.audio?.mime_type,
            sha256: messageData.audio?.sha256,
            id: messageData.audio?.id, // Keep for backward compatibility
          });
          break;
        case "video":
          content = messageData.video?.caption || "[Video]";
          contentType = "video";
          attachments.push({
            type: "video",
            mediaId: messageData.video?.id, // WhatsApp media ID
            mimeType: messageData.video?.mime_type,
            sha256: messageData.video?.sha256,
            id: messageData.video?.id, // Keep for backward compatibility
          });
          break;
        default:
          content = `[Unsupported message type: ${messageData.type}]`;
          contentType = "unsupported";
      }

      const messageTimestamp = new Date(parseInt(messageData.timestamp) * 1000);

      const message = await prisma.message.create({
        data: {
          content,
          contentType,
          status: isIncoming ? "RECEIVED" : "SENT",
          isIncoming,
          conversationId: conversation.id,
          tenantId: conversation.tenantId,
          meta: {
            whatsapp_message_id: messageData.id,
            whatsapp_timestamp: messageData.timestamp,
            whatsapp_type: messageData.type,
            whatsapp_from: messageData.from,
            whatsapp_context: messageData.context,
            attachments: attachments.length > 0 ? attachments : undefined,
          },
          createdAt: messageTimestamp,
          updatedAt: new Date(),
        },
      });

      // Update conversation with last message info
      await prisma.conversation.update({
        where: { id: conversation.id },
        data: {
          lastMessageAt: messageTimestamp,
          messageCount: {
            increment: 1,
          },
          updatedAt: new Date(),
        },
      });

      console.log(
        "Created WhatsApp message:",
        message.id,
        "Type:",
        messageData.type
      );
      return message;
    } catch (error) {
      console.error("Error creating message:", error);
      throw error;
    }
  }

  /**
   * Process AI chatbot response for incoming message
   */
  private static async processAIChatbotResponse(
    conversation: any,
    message: any,
    context: any
  ) {
    try {
      console.log("Processing AI chatbot response for message:", message.id);

      // Check if AI should respond to this message
      const aiResult = await WhatsAppAIChatbotService.processIncomingMessage(
        conversation,
        message,
        context
      );

      if (aiResult.shouldReply && aiResult.response) {
        console.log("AI chatbot will respond:", aiResult.response);

        // Get chatbot config for delay
        const config = await this.getChatbotConfig(context.inbox.id);

        // Send AI response
        const sendResult = await WhatsAppAIChatbotService.sendAIResponse(
          context.inbox.id,
          conversation.id,
          context.contact.phone,
          aiResult.response,
          config.autoReplyDelay || 2
        );

        if (sendResult.success) {
          console.log("AI response sent successfully");
        } else {
          console.error("Failed to send AI response:", sendResult.error);
        }
      } else {
        console.log("AI chatbot will not respond:", aiResult.reason);
      }
    } catch (error) {
      console.error("Error processing AI chatbot response:", error);
    }
  }

  /**
   * Get recent messages for conversation context
   */
  private static async getRecentMessages(conversationId: string) {
    try {
      return await prisma.message.findMany({
        where: { conversationId },
        orderBy: { createdAt: "desc" },
        take: 10,
        select: {
          id: true,
          content: true,
          isIncoming: true,
          createdAt: true,
        },
      });
    } catch (error) {
      console.error("Error getting recent messages:", error);
      return [];
    }
  }

  /**
   * Get chatbot configuration for inbox
   */
  private static async getChatbotConfig(inboxId: string) {
    try {
      const inbox = await prisma.inbox.findUnique({
        where: { id: inboxId },
        select: { settings: true },
      });

      const settings = inbox?.settings as any;
      const chatbotSettings = settings?.chatbot || {};

      return {
        enabled: chatbotSettings.enabled || false,
        autoReplyDelay: chatbotSettings.autoReplyDelay || 2,
      };
    } catch (error) {
      console.error("Error getting chatbot config:", error);
      return { enabled: false, autoReplyDelay: 2 };
    }
  }

  /**
   * Map WhatsApp status to our message status
   */
  private static mapWhatsAppStatus(whatsappStatus: string): string {
    switch (whatsappStatus) {
      case "sent":
        return "SENT";
      case "delivered":
        return "DELIVERED";
      case "read":
        return "READ";
      case "failed":
        return "FAILED";
      default:
        return "SENT";
    }
  }
}
