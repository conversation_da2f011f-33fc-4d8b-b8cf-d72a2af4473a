import { prisma } from "@flinkk/database/prisma";
import OpenAI from "openai";

export interface ChatbotConfig {
  enabled: boolean;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  autoReplyDelay: number; // in seconds
  businessHours?: {
    enabled: boolean;
    timezone: string;
    schedule: {
      [key: string]: { start: string; end: string; enabled: boolean };
    };
  };
  fallbackToHuman?: boolean;
  keywords?: {
    humanHandoff: string[];
    noReply: string[];
  };
}

export interface ChatbotContext {
  conversation: any;
  contact: any;
  inbox: any;
  tenant: any;
  recentMessages: any[];
}

export class WhatsAppAIChatbotService {
  private static openai: OpenAI;

  static {
    const API_KEY =
      process.env.NEXT_PUBLIC_AZURE_OPENAI_API_KEY ||
      process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const BASE_URL = process.env.NEXT_PUBLIC_AZURE_OPENAI_BASE_URL;
    const API_VERSION = process.env.NEXT_PUBLIC_AZURE_OPENAI_API_VERSION;

    const openaiConfig: any = {
      apiKey: API_KEY,
    };

    // If using Azure OpenAI, add the necessary Azure-specific configuration
    if (BASE_URL && API_VERSION) {
      openaiConfig.baseURL = BASE_URL;
      openaiConfig.defaultQuery = { "api-version": API_VERSION };
      openaiConfig.defaultHeaders = {
        "api-key": API_KEY,
        Authorization: `Bearer ${API_KEY}`,
      };
    }

    this.openai = new OpenAI(openaiConfig);
  }

  /**
   * Process incoming message and generate AI response if applicable
   */
  static async processIncomingMessage(
    conversation: any,
    message: any,
    context: ChatbotContext
  ): Promise<{ shouldReply: boolean; response?: string; reason?: string }> {
    try {
      // Get chatbot configuration for this inbox
      const config = await this.getChatbotConfig(context.inbox.id);

      if (!config.enabled) {
        return { shouldReply: false, reason: "Chatbot disabled" };
      }

      // Check business hours
      if (!this.isWithinBusinessHours(config)) {
        return { shouldReply: false, reason: "Outside business hours" };
      }

      // Check for human handoff keywords
      if (this.shouldHandoffToHuman(message.content, config)) {
        await this.markConversationForHumanHandoff(conversation.id);
        return { shouldReply: false, reason: "Human handoff requested" };
      }

      // Check for no-reply keywords
      if (this.shouldNotReply(message.content, config)) {
        return { shouldReply: false, reason: "No-reply keyword detected" };
      }

      // Check if conversation is already assigned to human
      if (conversation.assigneeId) {
        return { shouldReply: false, reason: "Conversation assigned to human" };
      }

      // Generate AI response
      const response = await this.generateAIResponse(message, context, config);

      if (response) {
        return { shouldReply: true, response };
      }

      return { shouldReply: false, reason: "Failed to generate response" };
    } catch (error) {
      console.error("Error processing message for AI chatbot:", error);
      return { shouldReply: false, reason: "Processing error" };
    }
  }

  /**
   * Generate AI response using OpenAI
   */
  private static async generateAIResponse(
    message: any,
    context: ChatbotContext,
    config: ChatbotConfig
  ): Promise<string | null> {
    try {
      const systemPrompt = this.buildSystemPrompt(context, config);
      const conversationHistory = this.buildConversationHistory(
        context.recentMessages
      );

      const completion = await this.openai.chat.completions.create({
        model: config.model || "gpt-4o-mini",
        messages: [
          { role: "system", content: systemPrompt },
          ...conversationHistory,
          { role: "user", content: message.content },
        ],
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 500,
      });

      const response = completion.choices[0]?.message?.content;

      if (response) {
        // Log AI interaction
        await this.logAIInteraction(
          context.conversation.id,
          message.content,
          response,
          config.model
        );
      }

      return response;
    } catch (error) {
      console.error("Error generating AI response:", error);
      return null;
    }
  }

  /**
   * Build system prompt with context
   */
  private static buildSystemPrompt(
    context: ChatbotContext,
    config: ChatbotConfig
  ): string {
    const basePrompt =
      config.systemPrompt ||
      `
You are a helpful AI assistant for ${context.tenant.name || "our company"}. 
You are responding to WhatsApp messages from customers.

Guidelines:
- Be helpful, friendly, and professional
- Keep responses concise and clear
- If you cannot help with something, politely suggest they speak with a human agent
- Use the customer's name if available: ${context.contact.name}
- Current time: ${new Date().toLocaleString()}

Business Information:
- Company: ${context.tenant.name || "Our Company"}
- Inbox: ${context.inbox.name}
- Contact: ${context.contact.name} (${context.contact.phone})
`;

    return basePrompt;
  }

  /**
   * Build conversation history for context
   */
  private static buildConversationHistory(
    messages: any[]
  ): Array<{ role: string; content: string }> {
    return messages
      .slice(-10) // Last 10 messages for context
      .map((msg) => ({
        role: msg.isIncoming ? "user" : "assistant",
        content: msg.content,
      }));
  }

  /**
   * Get chatbot configuration for inbox
   */
  private static async getChatbotConfig(
    inboxId: string
  ): Promise<ChatbotConfig> {
    try {
      const inbox = await prisma.inbox.findUnique({
        where: { id: inboxId },
        select: { settings: true },
      });

      const settings = inbox?.settings as any;
      const chatbotSettings = settings?.chatbot || {};

      return {
        enabled: chatbotSettings.enabled || false,
        model: chatbotSettings.model || "gpt-4o-mini",
        temperature: chatbotSettings.temperature || 0.7,
        maxTokens: chatbotSettings.maxTokens || 500,
        systemPrompt: chatbotSettings.systemPrompt || "",
        autoReplyDelay: chatbotSettings.autoReplyDelay || 2,
        businessHours: chatbotSettings.businessHours,
        fallbackToHuman: chatbotSettings.fallbackToHuman || true,
        keywords: chatbotSettings.keywords || {
          humanHandoff: [
            "human",
            "agent",
            "representative",
            "speak to someone",
          ],
          noReply: ["stop", "unsubscribe", "opt out"],
        },
      };
    } catch (error) {
      console.error("Error getting chatbot config:", error);
      return {
        enabled: false,
        model: "gpt-4o-mini",
        temperature: 0.7,
        maxTokens: 500,
        systemPrompt: "",
        autoReplyDelay: 2,
      };
    }
  }

  /**
   * Check if current time is within business hours
   */
  private static isWithinBusinessHours(config: ChatbotConfig): boolean {
    if (!config.businessHours?.enabled) {
      return true; // Always available if business hours not configured
    }

    const now = new Date();
    const dayOfWeek = now.toLocaleLowerCase().substring(0, 3); // mon, tue, etc.
    const schedule = config.businessHours.schedule[dayOfWeek];

    if (!schedule?.enabled) {
      return false;
    }

    const currentTime = now.toTimeString().substring(0, 5); // HH:MM format
    return currentTime >= schedule.start && currentTime <= schedule.end;
  }

  /**
   * Check if message contains human handoff keywords
   */
  private static shouldHandoffToHuman(
    content: string,
    config: ChatbotConfig
  ): boolean {
    if (!config.keywords?.humanHandoff) return false;

    const lowerContent = content.toLowerCase();
    return config.keywords.humanHandoff.some((keyword) =>
      lowerContent.includes(keyword.toLowerCase())
    );
  }

  /**
   * Check if message contains no-reply keywords
   */
  private static shouldNotReply(
    content: string,
    config: ChatbotConfig
  ): boolean {
    if (!config.keywords?.noReply) return false;

    const lowerContent = content.toLowerCase();
    return config.keywords.noReply.some((keyword) =>
      lowerContent.includes(keyword.toLowerCase())
    );
  }

  /**
   * Mark conversation for human handoff
   */
  private static async markConversationForHumanHandoff(
    conversationId: string
  ): Promise<void> {
    try {
      await prisma.conversation.update({
        where: { id: conversationId },
        data: {
          meta: {
            humanHandoffRequested: true,
            handoffRequestedAt: new Date().toISOString(),
          },
        },
      });
    } catch (error) {
      console.error("Error marking conversation for human handoff:", error);
    }
  }

  /**
   * Log AI interaction for analytics
   */
  private static async logAIInteraction(
    conversationId: string,
    userMessage: string,
    aiResponse: string,
    model: string
  ): Promise<void> {
    try {
      await prisma.activityLog.create({
        data: {
          action: "AI_RESPONSE_GENERATED",
          entityType: "CONVERSATION",
          entityId: conversationId,
          meta: {
            userMessage: userMessage.substring(0, 500), // Limit length
            aiResponse: aiResponse.substring(0, 500),
            model,
            timestamp: new Date().toISOString(),
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error("Error logging AI interaction:", error);
    }
  }

  /**
   * Send AI response via WhatsApp
   */
  static async sendAIResponse(
    inboxId: string,
    conversationId: string,
    phoneNumber: string,
    response: string,
    delay: number = 2
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Add delay to make response feel more natural
      if (delay > 0) {
        await new Promise((resolve) => setTimeout(resolve, delay * 1000));
      }

      // Send message via WhatsApp API
      const sendResponse = await fetch("/api/whatsapp/send-message", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          inboxId,
          to: phoneNumber,
          text: response,
          conversationId,
        }),
      });

      if (sendResponse.ok) {
        return { success: true };
      } else {
        const errorData = await sendResponse.json();
        return { success: false, error: errorData.error };
      }
    } catch (error) {
      console.error("Error sending AI response:", error);
      return { success: false, error: "Failed to send AI response" };
    }
  }
}
