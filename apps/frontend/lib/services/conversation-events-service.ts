// Simple in-memory event store for conversation updates
// In production, this should use Redis or another persistent store

interface ConversationEvent {
  id: string;
  conversationId: string;
  type: "new_message" | "message_status_update";
  data: any;
  timestamp: Date;
}

class ConversationEventsService {
  private events: Map<string, ConversationEvent[]> = new Map();
  private maxEventsPerConversation = 100;

  // Add a new event for a conversation
  addEvent(conversationId: string, type: ConversationEvent["type"], data: any) {
    const event: ConversationEvent = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      conversationId,
      type,
      data,
      timestamp: new Date(),
    };

    if (!this.events.has(conversationId)) {
      this.events.set(conversationId, []);
    }

    const conversationEvents = this.events.get(conversationId)!;
    conversationEvents.push(event);

    // Keep only the most recent events
    if (conversationEvents.length > this.maxEventsPerConversation) {
      conversationEvents.splice(0, conversationEvents.length - this.maxEventsPerConversation);
    }

    console.log(`Added ${type} event for conversation ${conversationId}:`, event);
  }

  // Get events for a conversation since a specific timestamp
  getEventsSince(conversationId: string, since: Date): ConversationEvent[] {
    const conversationEvents = this.events.get(conversationId) || [];
    return conversationEvents.filter(event => event.timestamp > since);
  }

  // Get all events for a conversation
  getAllEvents(conversationId: string): ConversationEvent[] {
    return this.events.get(conversationId) || [];
  }

  // Clear old events (cleanup)
  clearOldEvents(olderThan: Date) {
    for (const [conversationId, events] of this.events.entries()) {
      const filteredEvents = events.filter(event => event.timestamp > olderThan);
      if (filteredEvents.length === 0) {
        this.events.delete(conversationId);
      } else {
        this.events.set(conversationId, filteredEvents);
      }
    }
  }

  // Add a new message event
  addNewMessageEvent(conversationId: string, message: any) {
    this.addEvent(conversationId, "new_message", message);
  }

  // Add a message status update event
  addMessageStatusUpdateEvent(conversationId: string, update: any) {
    this.addEvent(conversationId, "message_status_update", update);
  }
}

// Export a singleton instance
export const conversationEventsService = new ConversationEventsService();

// Cleanup old events every 5 minutes
setInterval(() => {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  conversationEventsService.clearOldEvents(oneHourAgo);
}, 5 * 60 * 1000);
