import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

// Define public routes that don't require authentication
const publicRoutes = [
  "/sign-in",
  "/sign-up",
  "/forgot-password",
  "/reset-password",
  "/set-password",
  "/verify-email",
  "/no-organization", // Added no-organization page for users without org access
  "/api/auth",
  "/api/login",
  "/api/register",
  "/api/widget",
  "/api/socket",
  "/api/copilot", // Added copilot API to public routes
  "/api/copilot/perfect-piste", // Explicitly add perfect-piste endpoint
  "/api/roadmap", // Added roadmap API for public access
  "/api/lead/create", // Added public lead creation API
  "/embed.js",
  "/sdk.js", // Added SDK.js for the live chat
  "/support-chat", // Added support-chat page for public access
  "/public-roadmap", // Added public roadmap page for customer access
  "/quotation-sample", // Added quotation sample page for public access
];

// Check if the route is a public route
function isPublicRoute(path: string): boolean {
  // Special case for support-chat with dynamic routes
  if (path.startsWith("/support-chat")) {
    return true;
  }
  // Special case for all copilot API routes
  if (path.startsWith("/api/copilot")) {
    return true;
  }
  return publicRoutes.some((route) => path.startsWith(route));
}

// Add CORS headers to the response
function addCorsHeaders(response: NextResponse, _origin?: string) {
  // Allow all origins for copilot API
  response.headers.set("Access-Control-Allow-Origin", "*");
  // Note: Cannot use credentials: true with origin: * - removing credentials header
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, PATCH, OPTIONS",
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, Accept, x-copilotkit-runtime-client-gql-version, X-Requested-With",
  );
  response.headers.set("Access-Control-Max-Age", "86400"); // 24 hours
  return response;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const origin = request.headers.get("origin") || undefined;

  // Handle CORS preflight requests first, before any authentication checks
  if (request.method === "OPTIONS") {
    const response = NextResponse.json({}, { status: 200 });
    return addCorsHeaders(response, origin);
  }

  // Special handling for copilot API routes - always allow with CORS headers, no auth required
  if (pathname.startsWith("/api/copilot")) {
    const response = NextResponse.next();
    return addCorsHeaders(response, origin);
  }

  // Check if it's a public route early to avoid authentication checks
  const isPublic = isPublicRoute(pathname);

  // Allow all public routes without authentication
  if (isPublic) {
    const response = NextResponse.next();
    // Add CORS headers for API routes
    if (pathname.startsWith("/api/")) {
      return addCorsHeaders(response, origin);
    }
    return response;
  }

  // Add CORS headers for other API routes
  if (pathname.startsWith("/api/")) {
    const response = NextResponse.next();
    return addCorsHeaders(response, origin);
  }

  const token = await getToken({ req: request });
  const isAuthenticated = !!token;

  // If not authenticated and not a public route, redirect to sign-in
  if (!isAuthenticated && !isPublic) {
    const signInUrl = new URL("/sign-in", request.url);
    return NextResponse.redirect(signInUrl);
  }

  return NextResponse.next();
}

// Configure which routes use this middleware
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * 1. /api/auth/* (authentication routes)
     * 2. /_next/* (Next.js internals)
     * 3. /fonts/* (static assets)
     * 4. /images/* (static assets)
     * 5. /favicon.ico, /site.webmanifest (static assets)
     * 6. /support-chat/* (public support chat)
     */
    "/((?!_next/|fonts/|images/|favicon.ico|site.webmanifest|support-chat/).*)",
  ],
};
