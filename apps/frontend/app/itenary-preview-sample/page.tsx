import React from 'react';
import {  Hotel, Car, FileText, MapPin, Clock, Phone, Mail, Calendar, Download } from 'lucide-react';

export default function ItineraryPage() {
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header Section */}
      <div className="bg-gradient-to-br from-teal-500 via-cyan-500 to-blue-600 text-white">
        <div className="relative h-64 overflow-hidden">
          <div 
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-teal-600/80 via-cyan-600/70 to-blue-700/80"></div>
          </div>
          
          <div className="relative z-10 p-6 h-full flex flex-col justify-end">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-full overflow-hidden border-3 border-white/30 shadow-lg">
                  <img
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">Yokesh KS</h1>
                  <div className="flex items-center gap-1 text-white/90 text-sm mt-1">
                    <Mail className="w-3 h-3" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-1 text-white/90 text-sm mt-0.5">
                    <Phone className="w-3 h-3" />
                    <span>+91 80984 44187</span>
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 border border-white/30">
                  <div className="flex items-center gap-1 text-white/90 text-sm mb-1">
                    <Calendar className="w-3 h-3" />
                    <span>May 8, 2025 | May 15, 2025</span>
                  </div>
                  <h2 className="text-xl font-bold text-white">Cruise Itinerary</h2>
                  <p className="text-white/90 text-sm font-medium">$5000 per person</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6 space-y-6 -mt-8">
        {/* Information & Documents Card */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-600" />
              INFORMATION & DOCUMENTS
            </h3>
          </div>
          
          <div className="p-6">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                <MapPin className="w-4 h-4 text-orange-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-800 mb-2">Los Angeles, California - The City</h4>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  Los Angeles is a sprawling metropolis between the mountains and the ocean. It consists of several mini-cities connected by a network of freeways. LA is divided into five main districts: Downtown, Hollywood, the Valley, West Side, and the Beach - each a world in itself.
                </p>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  The size of the place may be a source of confusion to a first-time visitor, but don't give up! LA is one huge epic space, teeming with a concoction of hundreds of various communities and neighborhoods. From Beverly Hills, it certainly is worth venturing Downtown for a bit more grit and less glitz, as well as for some great eating and drinking. Downtown LA boasts the magnificent Walt Disney Concert Hall, an experience in itself.
                </p>
                <p className="text-gray-600 text-sm leading-relaxed mb-6">
                  LA is Always will always be associated with movies and movie stars. The huge Hollywood sign on the hill to Universal Studios, the glamour of Beverly Hills and the unparalleled celebrity spotting in best-end restaurants... You cannot help but be lured into the surreal and magical world. However, if you fail to look beyond this celluloid culture, you will be missing some of the most fabulous art collections and galleries in the world, the Getty Centre, the Los Angeles County Museum of Art and the Norton Simon Museum all house spectacularly permanent collections.
                </p>
                <p className="text-gray-600 text-sm leading-relaxed">
                  With most days of the year being sunny and warm, outdoor life is a large part of the LA experience: beaches, Santa Monica, Venice and Melbo beaches offer a great urban beach culture with roller skating on the promenades and shopping on the boulevards. Griffith Park is one of the largest city parks in the country and offers an immense of outdoor pursuits (including the LA Zoo).
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-6">
              <img src="https://images.unsplash.com/photo-1544413966-1ad5ac882d5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Los Angeles" className="rounded-lg object-cover h-24 w-full"/>
              <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Hollywood" className="rounded-lg object-cover h-24 w-full"/>
              <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Beach" className="rounded-lg object-cover h-24 w-full"/>
              <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="City" className="rounded-lg object-cover h-24 w-full"/>
            </div>
          </div>
        </div>

        {/* Welcome to Los Angeles */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 border-b border-blue-200">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <Calendar className="w-5 h-5 text-blue-600" />
              WELCOME TO LOS ANGELES May 8
            </h3>
          </div>
          
          <div className="p-6">
            {/* Departure */}
            <div className="flex items-start gap-4 mb-6">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-bold text-sm">1</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="font-semibold text-gray-800">6:02 - Departure Central Daylight Time</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">Depart from Dallas Fort Worth International Airport</h4>
                <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Airline</span>
                    <span className="font-medium">Delta Air Lines</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Flight Number</span>
                    <span className="font-medium">DL 419</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Terminal</span>
                    <span className="font-medium">E</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Gate</span>
                    <span className="font-medium">E13</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Seat / Ticket Details</span>
                    <span className="font-medium">Business</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Arrival */}
            <div className="flex items-start gap-4 mb-6">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-green-600 font-bold text-sm">2</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="font-semibold text-gray-800">7:12 - Arrival Pacific Daylight Time</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">Arrive at Los Angeles International Airport</h4>
                <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Airline</span>
                    <span className="font-medium">Delta Air Lines</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Flight Number</span>
                    <span className="font-medium">DL 439</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Terminal</span>
                    <span className="font-medium">3</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Gate</span>
                    <span className="font-medium">25</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Seat / Ticket Details</span>
                    <span className="font-medium">Business</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Transport */}
            <div className="flex items-start gap-4 mb-6">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Car className="w-4 h-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-800 mb-2">Private Transport to Hotel</h4>
                <p className="text-gray-600 text-sm">
                  Forget the hassle of taxis or figuring out transport from the airport to the hotel. Our private transfer will be arranged to take you to your hotel as well as to and from the various departure points.
                </p>
              </div>
            </div>

            {/* Hotel Check-in */}
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Hotel className="w-4 h-4 text-teal-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-800 mb-2">Check-in at DoubleTree San Pedro - Port of Los Angeles</h4>
                <p className="text-gray-600 text-sm mb-3">This hotel warmly welcomes guests in San Pedro.</p>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="font-medium text-gray-800 mb-2">Facilities</h5>
                  <p className="text-sm text-gray-600 mb-3">
                    This hotel has an elevator and features 225 rooms. The friendly staff at the reception desk are happy to answer any questions. Amenities include a baggage storage service, safe, ATM and drink machine. Wireless internet access is provided in public areas. The hotel has a range of facilities for guests with disabilities. Wheelchair-accessible facilities are available. Souvenirs can be purchased at the gift shop. A garden provides extra space for rest and relaxation in the open air. Additional facilities include a playroom. Guests arriving by car can park their vehicles in the parking lot. Further services and facilities include a 24-hour security service, a car rental service, a transfer service, room service, a laundry service and a conference room. Active guests can make use of the bicycle rental service to explore the surrounding area. In addition, a shuttle service to the airport is offered.
                  </p>
                  <p className="text-sm text-gray-600">
                    For guests' business requirements, email provides a fax machine.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Download Button */}
        <div className="text-center pt-4">
          <button className="bg-gradient-to-r from-blue-600 to-teal-600 text-white px-8 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 mx-auto">
            <Download className="w-5 h-5" />
            Download Itinerary
          </button>
        </div>
      </div>
    </div>
  );
}