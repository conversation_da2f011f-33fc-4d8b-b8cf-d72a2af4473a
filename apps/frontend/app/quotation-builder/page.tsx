"use client";

import React, { useState } from "react";
import { QuotationBuilderProvider } from "./components/quotation-builder-context";
import { LeftPane } from "./components/left-pane";
import { RightPane } from "./components/right-pane";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@flinkk/components/ui/resizable";
import { SendQuoteModal } from "./components/send-quote-modal";

export default function QuotationBuilderPage() {
  const [showSendModal, setShowSendModal] = useState(false);

  return (
    <QuotationBuilderProvider>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">
                Quotation Builder
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Create and customize your quotation with live preview
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Save Draft
              </button>
              <button
                onClick={() => setShowSendModal(true)}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
              >
                Send Quote
              </button>
            </div>
          </div>
        </div>

        {/* Main Content - Resizable Two Pane Layout */}
        <div className="h-[calc(100vh-80px)]">
          <ResizablePanelGroup direction="horizontal" className="h-full">
            {/* Left Pane - Form Builder */}
            <ResizablePanel defaultSize={40} minSize={30} maxSize={60}>
              <div className="h-full bg-white border-r border-gray-200 overflow-y-auto">
                <LeftPane />
              </div>
            </ResizablePanel>

            <ResizableHandle withHandle />

            {/* Right Pane - Live Preview */}
            <ResizablePanel defaultSize={60} minSize={40} maxSize={70}>
              <div className="h-full bg-gray-50 overflow-y-auto">
                <RightPane />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>

        {/* Send Quote Modal */}
        {showSendModal && (
          <SendQuoteModal onClose={() => setShowSendModal(false)} />
        )}
      </div>
    </QuotationBuilderProvider>
  );
}
