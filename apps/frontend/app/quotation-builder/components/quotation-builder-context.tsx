"use client";

import React, { createContext, useContext, useState, useReducer } from "react";

// Define types for our context
export interface CustomSection {
  id: string;
  order: number;
  type: "text" | "image" | "text_image";
  title?: string;
  content?: string;
  imageUrl?: string;
  imageAlt?: string;
}

export interface BuilderProduct {
  id: string;
  type: "destination" | "hotel" | "room" | "addon";
  sourceId: string;
  name: string;
  description: string;
  imageUrl?: string;
  price?: number;
  isSelected: boolean;
  customizations?: Record<string, any>;
}

export interface TextStyle {
  headingFont: string;
  headingSize: string;
  bodyFont: string;
}

export interface QuotationBuilderState {
  id?: string;
  coverImage?: string;
  textStyle: TextStyle;
  customSections: CustomSection[];
  products: BuilderProduct[];
  addons: BuilderProduct[];
  totalPrice: number;
  isManualPrice: boolean;
  manualPriceValue?: number;
  status: "draft" | "sent" | "accepted" | "rejected" | "changes_requested";
}

// Initial state
const initialState: QuotationBuilderState = {
  textStyle: {
    headingFont: "sans",
    headingSize: "2xl",
    bodyFont: "sans",
  },
  customSections: [],
  products: [],
  addons: [],
  totalPrice: 0,
  isManualPrice: false,
  status: "draft",
};

// Action types
type Action =
  | { type: "SET_COVER_IMAGE"; payload: string }
  | { type: "SET_TEXT_STYLE"; payload: TextStyle }
  | { type: "ADD_SECTION"; payload: CustomSection }
  | { type: "UPDATE_SECTION"; payload: CustomSection }
  | { type: "REMOVE_SECTION"; payload: string }
  | { type: "REORDER_SECTIONS"; payload: CustomSection[] }
  | { type: "ADD_PRODUCT"; payload: BuilderProduct }
  | { type: "UPDATE_PRODUCT"; payload: BuilderProduct }
  | { type: "REMOVE_PRODUCT"; payload: string }
  | { type: "TOGGLE_PRODUCT_SELECTION"; payload: string }
  | { type: "SET_MANUAL_PRICE"; payload: { isManual: boolean; value?: number } }
  | { type: "CALCULATE_TOTAL" }
  | { type: "SET_STATUS"; payload: QuotationBuilderState["status"] }
  | { type: "RESET" }
  | { type: "LOAD_STATE"; payload: QuotationBuilderState };

// Reducer function
function quotationBuilderReducer(
  state: QuotationBuilderState,
  action: Action
): QuotationBuilderState {
  switch (action.type) {
    case "SET_COVER_IMAGE":
      return { ...state, coverImage: action.payload };
    case "SET_TEXT_STYLE":
      return { ...state, textStyle: action.payload };
    case "ADD_SECTION":
      return {
        ...state,
        customSections: [...state.customSections, action.payload],
      };
    case "UPDATE_SECTION":
      return {
        ...state,
        customSections: state.customSections.map((section) =>
          section.id === action.payload.id ? action.payload : section
        ),
      };
    case "REMOVE_SECTION":
      return {
        ...state,
        customSections: state.customSections.filter(
          (section) => section.id !== action.payload
        ),
      };
    case "REORDER_SECTIONS":
      return {
        ...state,
        customSections: action.payload,
      };
    case "ADD_PRODUCT":
      if (action.payload.type === "addon") {
        return {
          ...state,
          addons: [...state.addons, action.payload],
        };
      }
      return {
        ...state,
        products: [...state.products, action.payload],
      };
    case "UPDATE_PRODUCT":
      if (action.payload.type === "addon") {
        return {
          ...state,
          addons: state.addons.map((product) =>
            product.id === action.payload.id ? action.payload : product
          ),
        };
      }
      return {
        ...state,
        products: state.products.map((product) =>
          product.id === action.payload.id ? action.payload : product
        ),
      };
    case "REMOVE_PRODUCT":
      return {
        ...state,
        products: state.products.filter(
          (product) => product.id !== action.payload
        ),
        addons: state.addons.filter((addon) => addon.id !== action.payload),
      };
    case "TOGGLE_PRODUCT_SELECTION":
      const updatedProducts = state.products.map((product) =>
        product.id === action.payload
          ? { ...product, isSelected: !product.isSelected }
          : product
      );
      const updatedAddons = state.addons.map((addon) =>
        addon.id === action.payload
          ? { ...addon, isSelected: !addon.isSelected }
          : addon
      );
      return {
        ...state,
        products: updatedProducts,
        addons: updatedAddons,
      };
    case "SET_MANUAL_PRICE":
      return {
        ...state,
        isManualPrice: action.payload.isManual,
        manualPriceValue: action.payload.value,
        totalPrice: action.payload.isManual && action.payload.value !== undefined
          ? action.payload.value
          : state.totalPrice,
      };
    case "CALCULATE_TOTAL":
      if (state.isManualPrice && state.manualPriceValue !== undefined) {
        return {
          ...state,
          totalPrice: state.manualPriceValue,
        };
      }
      
      const selectedProducts = state.products.filter(p => p.isSelected);
      const selectedAddons = state.addons.filter(a => a.isSelected);
      
      const productTotal = selectedProducts.reduce(
        (sum, product) => sum + (product.price || 0),
        0
      );
      
      const addonTotal = selectedAddons.reduce(
        (sum, addon) => sum + (addon.price || 0),
        0
      );
      
      return {
        ...state,
        totalPrice: productTotal + addonTotal,
      };
    case "SET_STATUS":
      return {
        ...state,
        status: action.payload,
      };
    case "RESET":
      return initialState;
    case "LOAD_STATE":
      return action.payload;
    default:
      return state;
  }
}

// Create context
interface QuotationBuilderContextType {
  state: QuotationBuilderState;
  dispatch: React.Dispatch<Action>;
}

const QuotationBuilderContext = createContext<QuotationBuilderContextType | undefined>(
  undefined
);

// Provider component
export function QuotationBuilderProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [state, dispatch] = useReducer(quotationBuilderReducer, initialState);

  return (
    <QuotationBuilderContext.Provider value={{ state, dispatch }}>
      {children}
    </QuotationBuilderContext.Provider>
  );
}

// Custom hook to use the context
export function useQuotationBuilder() {
  const context = useContext(QuotationBuilderContext);
  if (context === undefined) {
    throw new Error(
      "useQuotationBuilder must be used within a QuotationBuilderProvider"
    );
  }
  return context;
}
