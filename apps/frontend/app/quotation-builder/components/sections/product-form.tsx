"use client";

import React, { useState, useRef } from "react";
import { BuilderProduct } from "../quotation-builder-context";
import { X, Upload, Image as ImageIcon, Search } from "lucide-react";
import Image from "next/image";

interface ProductFormProps {
  product: BuilderProduct;
  onSave: (productData: Omit<BuilderProduct, "id">) => void;
  onCancel: () => void;
}

export function ProductForm({ product, onSave, onCancel }: ProductFormProps) {
  const [formData, setFormData] = useState({
    type: product.type,
    sourceId: product.sourceId || "",
    name: product.name || "",
    description: product.description || "",
    imageUrl: product.imageUrl || "",
    price: product.price || 0,
    isSelected: product.isSelected || false,
  });
  const [isUploading, setIsUploading] = useState(false);
  const [showInventoryBrowser, setShowInventoryBrowser] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setIsUploading(true);
    try {
      const imageUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, imageUrl }));
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    if (formData.imageUrl) {
      URL.revokeObjectURL(formData.imageUrl);
    }
    setFormData(prev => ({ ...prev, imageUrl: "" }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('Please enter a product name');
      return;
    }

    if (!formData.description.trim()) {
      alert('Please enter a product description');
      return;
    }

    onSave(formData);
  };

  const getTypeLabel = (type: BuilderProduct["type"]) => {
    switch (type) {
      case "destination":
        return "Destination";
      case "hotel":
        return "Hotel";
      case "room":
        return "Room Configuration";
      case "addon":
        return "Add-on Service";
      default:
        return "Product";
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {product.id ? "Edit" : "Add"} {getTypeLabel(formData.type)}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Browse Inventory Button */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-blue-900">Browse Inventory</h4>
                <p className="text-xs text-blue-700 mt-1">
                  Search and select from available {formData.type}s
                </p>
              </div>
              <button
                type="button"
                onClick={() => setShowInventoryBrowser(true)}
                className="flex items-center px-3 py-2 text-xs font-medium text-blue-600 bg-white border border-blue-300 rounded-md hover:bg-blue-50"
              >
                <Search className="h-3 w-3 mr-1" />
                Browse
              </button>
            </div>
          </div>

          {/* Manual Entry */}
          <div className="space-y-4">
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Or enter details manually:
              </h4>
            </div>

            {/* Product Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder={`Enter ${formData.type} name`}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder={`Describe this ${formData.type}`}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            {/* Price */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2 text-gray-500">$</span>
                <input
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleInputChange("price", parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image
              </label>
              
              {formData.imageUrl ? (
                <div className="relative">
                  <div className="relative h-48 w-full rounded-lg overflow-hidden border border-gray-200">
                    <Image
                      src={formData.imageUrl}
                      alt="Product image preview"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={handleRemoveImage}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 cursor-pointer"
                >
                  <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-sm font-medium text-gray-900 mb-1">
                    {isUploading ? "Uploading..." : "Click to upload image"}
                  </p>
                  <p className="text-xs text-gray-500">
                    PNG, JPG, GIF up to 10MB
                  </p>
                </div>
              )}

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                disabled={isUploading}
              />
            </div>

            {/* Include in Quote */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isSelected"
                checked={formData.isSelected}
                onChange={(e) => handleInputChange("isSelected", e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isSelected" className="ml-2 block text-sm text-gray-700">
                Include in quotation immediately
              </label>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isUploading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isUploading ? "Uploading..." : product.id ? "Update" : "Add"} {getTypeLabel(formData.type)}
            </button>
          </div>
        </form>
      </div>

      {/* Inventory Browser Modal - Placeholder */}
      {showInventoryBrowser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Browse {getTypeLabel(formData.type)}s
              </h3>
              <button
                onClick={() => setShowInventoryBrowser(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="p-6">
              <div className="text-center py-12 text-gray-500">
                <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium mb-2">Inventory Browser</p>
                <p className="text-sm">
                  This feature will connect to the inventory APIs to browse available {formData.type}s.
                </p>
                <p className="text-xs mt-2 text-gray-400">
                  Coming soon - for now, please enter details manually.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
