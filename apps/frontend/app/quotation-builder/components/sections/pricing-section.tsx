"use client";

import React, { useEffect } from "react";
import { useQuotationBuilder } from "../quotation-builder-context";
import { Calculator, DollarSign } from "lucide-react";

export function PricingSection() {
  const { state, dispatch } = useQuotationBuilder();

  // Auto-calculate total when products change
  useEffect(() => {
    if (!state.isManualPrice) {
      dispatch({ type: "CALCULATE_TOTAL" });
    }
  }, [state.products, state.addons, state.isManualPrice, dispatch]);

  const handleManualPriceToggle = (isManual: boolean) => {
    dispatch({
      type: "SET_MANUAL_PRICE",
      payload: {
        isManual,
        value: isManual ? state.totalPrice : undefined,
      },
    });
  };

  const handleManualPriceChange = (value: number) => {
    dispatch({
      type: "SET_MANUAL_PRICE",
      payload: {
        isManual: true,
        value,
      },
    });
    // Trigger total calculation
    setTimeout(() => {
      dispatch({ type: "CALCULATE_TOTAL" });
    }, 0);
  };

  const selectedProducts = state.products.filter(p => p.isSelected);
  const selectedAddons = state.addons.filter(a => a.isSelected);
  const allSelectedItems = [...selectedProducts, ...selectedAddons];

  const calculatedTotal = allSelectedItems.reduce(
    (sum, item) => sum + (item.price || 0),
    0
  );

  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium text-gray-700">Pricing</h4>
        <p className="text-xs text-gray-500 mt-1">
          Configure the total price for your quotation
        </p>
      </div>

      {/* Pricing Mode Toggle */}
      <div className="space-y-3">
        <div className="flex items-center">
          <input
            type="radio"
            id="auto-pricing"
            name="pricing-mode"
            checked={!state.isManualPrice}
            onChange={() => handleManualPriceToggle(false)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <label htmlFor="auto-pricing" className="ml-2 block text-sm text-gray-700">
            Automatic calculation (sum of selected items)
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="radio"
            id="manual-pricing"
            name="pricing-mode"
            checked={state.isManualPrice}
            onChange={() => handleManualPriceToggle(true)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <label htmlFor="manual-pricing" className="ml-2 block text-sm text-gray-700">
            Manual price override
          </label>
        </div>
      </div>

      {/* Price Breakdown */}
      {!state.isManualPrice && allSelectedItems.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="text-xs font-medium text-gray-700 mb-3 flex items-center">
            <Calculator className="h-3 w-3 mr-1" />
            Price Breakdown
          </h5>
          <div className="space-y-2">
            {allSelectedItems.map((item) => (
              <div key={item.id} className="flex justify-between items-center text-xs">
                <span className="text-gray-600 truncate mr-2">{item.name}</span>
                <span className="font-medium text-gray-900">
                  ${(item.price || 0).toFixed(2)}
                </span>
              </div>
            ))}
            <div className="border-t border-gray-200 pt-2 mt-2">
              <div className="flex justify-between items-center text-sm font-medium">
                <span className="text-gray-900">Subtotal</span>
                <span className="text-gray-900">${calculatedTotal.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Manual Price Input */}
      {state.isManualPrice && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Total Price
          </label>
          <div className="relative">
            <span className="absolute left-3 top-2 text-gray-500">$</span>
            <input
              type="number"
              value={state.manualPriceValue || 0}
              onChange={(e) => handleManualPriceChange(parseFloat(e.target.value) || 0)}
              placeholder="0.00"
              min="0"
              step="0.01"
              className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            This will override the automatic calculation
          </p>
        </div>
      )}

      {/* Total Display */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <DollarSign className="h-5 w-5 text-blue-600 mr-2" />
            <span className="text-sm font-medium text-blue-900">
              Total Quote Value
            </span>
          </div>
          <span className="text-lg font-bold text-blue-900">
            ${state.totalPrice.toFixed(2)}
          </span>
        </div>
        {state.isManualPrice && (
          <p className="text-xs text-blue-700 mt-1">
            Manual override active
          </p>
        )}
      </div>

      {/* Continue Payment Section */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <h5 className="text-sm font-medium text-green-900">
            To Continue, Pay Small Amount
          </h5>
          <span className="text-lg font-bold text-green-900">
            ${(state.totalPrice * 0.1).toFixed(2)}
          </span>
        </div>
        <p className="text-xs text-green-700 mb-3">
          Secure your booking with a 10% deposit. Remaining balance due upon confirmation.
        </p>
        <div className="flex items-center justify-between text-xs text-green-600">
          <span>Deposit (10%): ${(state.totalPrice * 0.1).toFixed(2)}</span>
          <span>Remaining: ${(state.totalPrice * 0.9).toFixed(2)}</span>
        </div>
      </div>

      {/* Pricing Notes */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Prices are displayed in USD</p>
        <p>• Tax and additional fees may apply</p>
        <p>• Final pricing subject to terms and conditions</p>
      </div>
    </div>
  );
}
