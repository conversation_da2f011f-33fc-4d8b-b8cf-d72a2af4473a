"use client";

import React from "react";
import { useQuotationBuilder } from "../quotation-builder-context";

export function TextStyleSection() {
  const { state, dispatch } = useQuotationBuilder();

  const handleStyleChange = (key: keyof typeof state.textStyle, value: string) => {
    dispatch({
      type: "SET_TEXT_STYLE",
      payload: {
        ...state.textStyle,
        [key]: value,
      },
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Text Styling
        </label>
        <p className="text-xs text-gray-500 mb-3">
          Customize the typography for your quotation
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {/* Heading Font */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Heading Font
          </label>
          <select
            value={state.textStyle.headingFont}
            onChange={(e) => handleStyleChange("headingFont", e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="sans">Sans Serif</option>
            <option value="serif">Serif</option>
            <option value="mono">Monospace</option>
          </select>
        </div>

        {/* Heading Size */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Heading Size
          </label>
          <select
            value={state.textStyle.headingSize}
            onChange={(e) => handleStyleChange("headingSize", e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="xl">Extra Large</option>
            <option value="2xl">2X Large</option>
            <option value="3xl">3X Large</option>
            <option value="4xl">4X Large</option>
            <option value="5xl">5X Large</option>
            <option value="6xl">6X Large</option>
          </select>
        </div>

        {/* Body Font */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Body Font
          </label>
          <select
            value={state.textStyle.bodyFont}
            onChange={(e) => handleStyleChange("bodyFont", e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="sans">Sans Serif</option>
            <option value="serif">Serif</option>
            <option value="mono">Monospace</option>
          </select>
        </div>
      </div>

      {/* Preview */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <div className="text-xs text-gray-500 mb-2">Preview:</div>
        <h3 
          className={`text-lg font-bold mb-2 ${
            state.textStyle.headingFont === 'serif' ? 'font-serif' : 
            state.textStyle.headingFont === 'mono' ? 'font-mono' : 'font-sans'
          }`}
        >
          Sample Heading
        </h3>
        <p 
          className={`text-sm text-gray-600 ${
            state.textStyle.bodyFont === 'serif' ? 'font-serif' : 
            state.textStyle.bodyFont === 'mono' ? 'font-mono' : 'font-sans'
          }`}
        >
          This is how your body text will appear in the quotation.
        </p>
      </div>
    </div>
  );
}
