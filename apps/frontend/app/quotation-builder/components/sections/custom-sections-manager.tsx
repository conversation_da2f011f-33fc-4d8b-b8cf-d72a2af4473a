"use client";

import React, { useState } from "react";
import { useQuotationBuilder, CustomSection } from "../quotation-builder-context";
import { Plus, Edit, Trash2, GripVertical, Type, Image as ImageIcon, FileText } from "lucide-react";
import { CustomSectionForm } from "./custom-section-form";

export function CustomSectionsManager() {
  const { state, dispatch } = useQuotationBuilder();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingSection, setEditingSection] = useState<CustomSection | null>(null);

  const handleAddSection = () => {
    setEditingSection(null);
    setIsFormOpen(true);
  };

  const handleEditSection = (section: CustomSection) => {
    setEditingSection(section);
    setIsFormOpen(true);
  };

  const handleDeleteSection = (sectionId: string) => {
    if (confirm("Are you sure you want to delete this section?")) {
      dispatch({ type: "REMOVE_SECTION", payload: sectionId });
    }
  };

  const handleSaveSection = (sectionData: Omit<CustomSection, "id" | "order">) => {
    if (editingSection) {
      // Update existing section
      dispatch({
        type: "UPDATE_SECTION",
        payload: {
          ...editingSection,
          ...sectionData,
        },
      });
    } else {
      // Add new section
      const newSection: CustomSection = {
        id: `section_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        order: state.customSections.length,
        ...sectionData,
      };
      dispatch({ type: "ADD_SECTION", payload: newSection });
    }
    setIsFormOpen(false);
    setEditingSection(null);
  };

  const handleCancelForm = () => {
    setIsFormOpen(false);
    setEditingSection(null);
  };

  const getSectionIcon = (type: CustomSection["type"]) => {
    switch (type) {
      case "text":
        return <Type className="h-4 w-4" />;
      case "image":
        return <ImageIcon className="h-4 w-4" />;
      case "text_image":
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getSectionTypeLabel = (type: CustomSection["type"]) => {
    switch (type) {
      case "text":
        return "Text Only";
      case "image":
        return "Image Only";
      case "text_image":
        return "Text + Image";
      default:
        return "Unknown";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-sm font-medium text-gray-700">Custom Sections</h4>
          <p className="text-xs text-gray-500 mt-1">
            Add custom content sections to your quotation
          </p>
        </div>
        <button
          onClick={handleAddSection}
          className="flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100"
        >
          <Plus className="h-3 w-3 mr-1" />
          Add Section
        </button>
      </div>

      {/* Sections List */}
      {state.customSections.length > 0 ? (
        <div className="space-y-2">
          {state.customSections
            .sort((a, b) => a.order - b.order)
            .map((section) => (
              <div
                key={section.id}
                className="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-lg"
              >
                <div className="flex items-center mr-3 text-gray-400">
                  <GripVertical className="h-4 w-4" />
                </div>
                
                <div className="flex items-center mr-3 text-gray-500">
                  {getSectionIcon(section.type)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h5 className="text-sm font-medium text-gray-900 truncate">
                      {section.title || "Untitled Section"}
                    </h5>
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                      {getSectionTypeLabel(section.type)}
                    </span>
                  </div>
                  {section.content && (
                    <p className="text-xs text-gray-500 mt-1 truncate">
                      {section.content.replace(/<[^>]*>/g, '').substring(0, 60)}...
                    </p>
                  )}
                </div>

                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => handleEditSection(section)}
                    className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded"
                  >
                    <Edit className="h-3 w-3" />
                  </button>
                  <button
                    onClick={() => handleDeleteSection(section.id)}
                    className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded"
                  >
                    <Trash2 className="h-3 w-3" />
                  </button>
                </div>
              </div>
            ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <FileText className="h-8 w-8 mx-auto mb-2 text-gray-300" />
          <p className="text-sm">No custom sections added yet</p>
          <p className="text-xs mt-1">Click "Add Section" to get started</p>
        </div>
      )}

      {/* Section Form Modal/Drawer */}
      {isFormOpen && (
        <CustomSectionForm
          section={editingSection}
          onSave={handleSaveSection}
          onCancel={handleCancelForm}
        />
      )}
    </div>
  );
}
