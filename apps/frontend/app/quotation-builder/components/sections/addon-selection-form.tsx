"use client";

import React, { useState, useEffect } from "react";
import { BuilderProduct } from "../quotation-builder-context";
import { X, Search, Star, Filter } from "lucide-react";
import Image from "next/image";

interface Addon {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  price: number;
  currency: string;
  category: string;
  duration: string;
  maxParticipants: number;
  difficulty: string;
  includes: string[];
}

interface AddonSelectionFormProps {
  product: BuilderProduct;
  onSave: (productData: Omit<BuilderProduct, "id">) => void;
  onCancel: () => void;
}

export function AddonSelectionForm({ product, onSave, onCancel }: AddonSelectionFormProps) {
  const [selectedAddon, setSelectedAddon] = useState<Addon | null>(null);
  const [addons, setAddons] = useState<Addon[]>([]);
  const [search, setSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Fetch add-ons on mount
  useEffect(() => {
    fetchAddons();
  }, []);

  const fetchAddons = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/inventory/addons');
      const data = await response.json();
      setAddons(data.addons || []);
    } catch (error) {
      console.error('Failed to fetch add-ons:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedAddon) {
      alert('Please select an add-on');
      return;
    }

    const productData: Omit<BuilderProduct, "id"> = {
      type: "addon",
      sourceId: selectedAddon.id,
      name: selectedAddon.name,
      description: selectedAddon.description,
      imageUrl: selectedAddon.imageUrl,
      price: selectedAddon.price,
      isSelected: false,
      customizations: {
        addon: selectedAddon,
      },
    };

    onSave(productData);
  };

  const categories = [...new Set(addons.map(addon => addon.category))];

  const filteredAddons = addons.filter(addon => {
    const matchesSearch = addon.name.toLowerCase().includes(search.toLowerCase()) ||
                         addon.description.toLowerCase().includes(search.toLowerCase()) ||
                         addon.category.toLowerCase().includes(search.toLowerCase());
    
    const matchesCategory = !selectedCategory || addon.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Select Add-on Service
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* Search and Filter */}
          <div className="flex space-x-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search add-ons..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="relative">
              <Filter className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Add-ons Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {filteredAddons.map((addon) => (
              <div
                key={addon.id}
                onClick={() => setSelectedAddon(addon)}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedAddon?.id === addon.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="space-y-3">
                  <div className="relative h-32 w-full rounded-lg overflow-hidden">
                    <Image
                      src={addon.imageUrl}
                      alt={addon.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <h5 className="text-sm font-medium text-gray-900 truncate">
                        {addon.name}
                      </h5>
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        {addon.category}
                      </span>
                    </div>
                    
                    <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                      {addon.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{addon.duration}</span>
                      <span>👥 {addon.maxParticipants}</span>
                    </div>
                    
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-sm font-semibold text-gray-900">
                        {addon.currency} {addon.price}
                      </span>
                      <span className="text-xs text-gray-500">
                        {addon.difficulty}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredAddons.length === 0 && !isLoading && (
            <div className="text-center py-12 text-gray-500">
              <Star className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium mb-2">No add-ons found</p>
              <p className="text-sm">Try adjusting your search or filter criteria</p>
            </div>
          )}

          {/* Selected Summary */}
          {selectedAddon && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h5 className="text-sm font-medium text-blue-900 mb-2">Selected Add-on:</h5>
              <div className="text-sm text-blue-800">
                <p><strong>Service:</strong> {selectedAddon.name}</p>
                <p><strong>Category:</strong> {selectedAddon.category}</p>
                <p><strong>Duration:</strong> {selectedAddon.duration}</p>
                <p><strong>Max Participants:</strong> {selectedAddon.maxParticipants}</p>
                <p><strong>Price:</strong> {selectedAddon.currency} {selectedAddon.price}</p>
              </div>
              
              {selectedAddon.includes.length > 0 && (
                <div className="mt-2">
                  <p className="text-xs font-medium text-blue-900 mb-1">Includes:</p>
                  <ul className="text-xs text-blue-800 list-disc list-inside">
                    {selectedAddon.includes.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!selectedAddon || isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? "Loading..." : "Add Service"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
