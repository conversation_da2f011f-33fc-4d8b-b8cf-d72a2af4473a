"use client";

import React from "react";
import { BuilderProduct } from "../quotation-builder-context";
import { Edit, Trash2, Check, X, MapPin, Building, Bed, Star, ShoppingBag } from "lucide-react";
import Image from "next/image";

interface ProductCardProps {
  product: BuilderProduct;
  onEdit: () => void;
  onDelete: () => void;
  onToggleSelection: () => void;
}

export function ProductCard({ product, onEdit, onDelete, onToggleSelection }: ProductCardProps) {
  const getProductIcon = (type: BuilderProduct["type"]) => {
    switch (type) {
      case "destination":
        return <MapPin className="h-4 w-4" />;
      case "hotel":
        return <Building className="h-4 w-4" />;
      case "room":
        return <Bed className="h-4 w-4" />;
      case "addon":
        return <Star className="h-4 w-4" />;
      default:
        return <ShoppingBag className="h-4 w-4" />;
    }
  };

  const getProductTypeLabel = (type: BuilderProduct["type"]) => {
    switch (type) {
      case "destination":
        return "Destination";
      case "hotel":
        return "Hotel";
      case "room":
        return "Room";
      case "addon":
        return "Add-on";
      default:
        return "Product";
    }
  };

  const getProductTypeColor = (type: BuilderProduct["type"]) => {
    switch (type) {
      case "destination":
        return "bg-purple-100 text-purple-800";
      case "hotel":
        return "bg-blue-100 text-blue-800";
      case "room":
        return "bg-green-100 text-green-800";
      case "addon":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className={`relative p-4 border-2 rounded-lg transition-all ${
      product.isSelected 
        ? "border-blue-300 bg-blue-50" 
        : "border-gray-200 bg-white hover:border-gray-300"
    }`}>
      {/* Selection Indicator */}
      <div className="absolute top-2 right-2">
        <button
          onClick={onToggleSelection}
          className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ${
            product.isSelected
              ? "bg-blue-600 border-blue-600 text-white"
              : "border-gray-300 hover:border-blue-400"
          }`}
        >
          {product.isSelected && <Check className="h-3 w-3" />}
        </button>
      </div>

      <div className="flex space-x-3">
        {/* Image */}
        <div className="flex-shrink-0">
          {product.imageUrl ? (
            <div className="w-16 h-16 relative rounded-lg overflow-hidden">
              <Image
                src={product.imageUrl}
                alt={product.name}
                fill
                className="object-cover"
              />
            </div>
          ) : (
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
              {getProductIcon(product.type)}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center space-x-2">
              <h5 className="text-sm font-medium text-gray-900 truncate">
                {product.name || "Untitled Product"}
              </h5>
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getProductTypeColor(product.type)}`}>
                {getProductTypeLabel(product.type)}
              </span>
            </div>
          </div>

          {product.description && (
            <p className="text-xs text-gray-600 mb-2 line-clamp-2">
              {product.description}
            </p>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {product.price !== undefined && (
                <span className="text-sm font-semibold text-gray-900">
                  ${product.price.toFixed(2)}
                </span>
              )}
            </div>

            <div className="flex items-center space-x-1">
              <button
                onClick={onEdit}
                className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                title="Edit product"
              >
                <Edit className="h-3 w-3" />
              </button>
              <button
                onClick={onDelete}
                className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                title="Delete product"
              >
                <Trash2 className="h-3 w-3" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Selection Status */}
      {product.isSelected && (
        <div className="mt-3 pt-3 border-t border-blue-200">
          <div className="flex items-center text-xs text-blue-700">
            <Check className="h-3 w-3 mr-1" />
            <span>Included in quotation</span>
          </div>
        </div>
      )}
    </div>
  );
}
