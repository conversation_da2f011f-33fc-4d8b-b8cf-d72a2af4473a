"use client";

import React, { useState, useEffect } from "react";
import { BuilderProduct } from "../quotation-builder-context";
import { X, Search, MapPin, Building, Bed } from "lucide-react";
import Image from "next/image";
import { RoomDetailsForm } from "./room-details-form";

interface Destination {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  basePrice: number;
  currency: string;
  country: string;
}

interface Hotel {
  id: string;
  destinationId: string;
  name: string;
  description: string;
  imageUrl: string;
  rating: number;
  basePrice: number;
  currency: string;
  amenities: string[];
}

interface RoomConfig {
  id: string;
  hotelId: string;
  name: string;
  description: string;
  imageUrl: string;
  capacity: number;
  price: number;
  currency: string;
  features: string[];
}

interface RoomSelectionFormProps {
  product: BuilderProduct;
  onSave: (productData: Omit<BuilderProduct, "id">) => void;
  onCancel: () => void;
}

export function RoomSelectionForm({ product, onSave, onCancel }: RoomSelectionFormProps) {
  const [selectedDestination, setSelectedDestination] = useState<Destination | null>(null);
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<RoomConfig | null>(null);
  const [showRoomForm, setShowRoomForm] = useState(false);

  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [rooms, setRooms] = useState<RoomConfig[]>([]);

  const [destinationSearch, setDestinationSearch] = useState("");
  const [hotelSearch, setHotelSearch] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Fetch destinations on mount
  useEffect(() => {
    fetchDestinations();
  }, []);

  // Fetch hotels when destination is selected
  useEffect(() => {
    if (selectedDestination) {
      fetchHotels(selectedDestination.id);
    } else {
      setHotels([]);
      setSelectedHotel(null);
    }
  }, [selectedDestination]);

  // Fetch rooms when hotel is selected
  useEffect(() => {
    if (selectedHotel) {
      fetchRooms(selectedHotel.id);
    } else {
      setRooms([]);
      setSelectedRoom(null);
    }
  }, [selectedHotel]);

  const fetchDestinations = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/inventory/destinations');
      const data = await response.json();
      setDestinations(data.destinations || []);
    } catch (error) {
      console.error('Failed to fetch destinations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchHotels = async (destinationId: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/inventory/hotels?destinationId=${destinationId}`);
      const data = await response.json();
      setHotels(data.hotels || []);
    } catch (error) {
      console.error('Failed to fetch hotels:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRooms = async (hotelId: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/inventory/room-configs?hotelId=${hotelId}`);
      const data = await response.json();
      setRooms(data.roomConfigs || []);
    } catch (error) {
      console.error('Failed to fetch rooms:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoomFormSave = (productData: Omit<BuilderProduct, "id">) => {
    onSave(productData);
  };

  const handleBackToSelection = () => {
    setShowRoomForm(false);
  };

  const filteredDestinations = destinations.filter(dest =>
    dest.name.toLowerCase().includes(destinationSearch.toLowerCase()) ||
    dest.country.toLowerCase().includes(destinationSearch.toLowerCase())
  );

  const filteredHotels = hotels.filter(hotel =>
    hotel.name.toLowerCase().includes(hotelSearch.toLowerCase())
  );

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onCancel();
    }
  };

  // Show room details form if room is selected
  if (showRoomForm && selectedRoom && selectedDestination && selectedHotel) {
    return (
      <RoomDetailsForm
        destination={selectedDestination}
        hotel={selectedHotel}
        room={selectedRoom}
        onSave={handleRoomFormSave}
        onCancel={onCancel}
        onBack={handleBackToSelection}
      />
    );
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Select Room Configuration
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Step 1: Select Destination */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <MapPin className="h-5 w-5 text-blue-600" />
                <h4 className="text-sm font-medium text-gray-900">1. Select Destination</h4>
              </div>
              
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search destinations..."
                  value={destinationSearch}
                  onChange={(e) => setDestinationSearch(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2 max-h-64 overflow-y-auto">
                {filteredDestinations.map((destination) => (
                  <div
                    key={destination.id}
                    onClick={() => setSelectedDestination(destination)}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedDestination?.id === destination.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 relative rounded-lg overflow-hidden">
                        <Image
                          src={destination.imageUrl}
                          alt={destination.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {destination.name}
                        </p>
                        <p className="text-xs text-gray-500">{destination.country}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Step 2: Select Hotel */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Building className="h-5 w-5 text-blue-600" />
                <h4 className="text-sm font-medium text-gray-900">2. Select Hotel</h4>
              </div>
              
              {selectedDestination ? (
                <>
                  <div className="relative">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search hotels..."
                      value={hotelSearch}
                      onChange={(e) => setHotelSearch(e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {filteredHotels.map((hotel) => (
                      <div
                        key={hotel.id}
                        onClick={() => setSelectedHotel(hotel)}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedHotel?.id === hotel.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 relative rounded-lg overflow-hidden">
                            <Image
                              src={hotel.imageUrl}
                              alt={hotel.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {hotel.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              ⭐ {hotel.rating} • {hotel.currency} {hotel.basePrice}/night
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Building className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">Select a destination first</p>
                </div>
              )}
            </div>

            {/* Step 3: Select Room */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Bed className="h-5 w-5 text-blue-600" />
                <h4 className="text-sm font-medium text-gray-900">3. Select Room</h4>
              </div>
              
              {selectedHotel ? (
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {rooms.map((room) => (
                    <div
                      key={room.id}
                      onClick={() => {
                        setSelectedRoom(room);
                        setShowRoomForm(true);
                      }}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedRoom?.id === room.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 relative rounded-lg overflow-hidden">
                          <Image
                            src={room.imageUrl}
                            alt={room.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {room.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            👥 {room.capacity} guests • {room.currency} {room.price}/night
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Bed className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">Select a hotel first</p>
                </div>
              )}
            </div>
          </div>

          {/* Selected Summary */}
          {selectedRoom && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h5 className="text-sm font-medium text-blue-900 mb-2">Selected Configuration:</h5>
              <div className="text-sm text-blue-800">
                <p><strong>Destination:</strong> {selectedDestination?.name}</p>
                <p><strong>Hotel:</strong> {selectedHotel?.name}</p>
                <p><strong>Room:</strong> {selectedRoom.name}</p>
                <p><strong>Price:</strong> {selectedRoom.currency} {selectedRoom.price}/night</p>
              </div>
            </div>
          )}

        </div>
      </div>
    </div>
  );
}
