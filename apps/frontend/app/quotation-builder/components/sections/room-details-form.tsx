"use client";

import React, { useState, useRef } from "react";
import { BuilderProduct } from "../quotation-builder-context";
import { X, Upload, Image as ImageIcon, ArrowLeft } from "lucide-react";
import Image from "next/image";

interface Destination {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  basePrice: number;
  currency: string;
  country: string;
}

interface Hotel {
  id: string;
  destinationId: string;
  name: string;
  description: string;
  imageUrl: string;
  rating: number;
  basePrice: number;
  currency: string;
  amenities: string[];
}

interface RoomConfig {
  id: string;
  hotelId: string;
  name: string;
  description: string;
  imageUrl: string;
  capacity: number;
  price: number;
  currency: string;
  features: string[];
}

interface RoomDetailsFormProps {
  destination: Destination;
  hotel: Hotel;
  room: RoomConfig;
  onSave: (productData: Omit<BuilderProduct, "id">) => void;
  onCancel: () => void;
  onBack: () => void;
}

export function RoomDetailsForm({ destination, hotel, room, onSave, onCancel, onBack }: RoomDetailsFormProps) {
  const [formData, setFormData] = useState({
    name: `${destination.name} - ${hotel.name} - ${room.name}`,
    description: room.description,
    imageUrl: room.imageUrl,
    price: room.price,
    isSelected: false,
  });
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setIsUploading(true);
    try {
      const imageUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, imageUrl }));
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    if (formData.imageUrl && formData.imageUrl.startsWith('blob:')) {
      URL.revokeObjectURL(formData.imageUrl);
    }
    setFormData(prev => ({ ...prev, imageUrl: "" }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('Please enter a room name');
      return;
    }

    if (!formData.description.trim()) {
      alert('Please enter a room description');
      return;
    }

    const productData: Omit<BuilderProduct, "id"> = {
      type: "room",
      sourceId: room.id,
      name: formData.name,
      description: formData.description,
      imageUrl: formData.imageUrl,
      price: formData.price,
      isSelected: formData.isSelected,
      customizations: {
        destination,
        hotel,
        room: {
          ...room,
          name: formData.name,
          description: formData.description,
          imageUrl: formData.imageUrl,
          price: formData.price,
        },
      },
    };

    onSave(productData);
  };

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onCancel();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <button
              onClick={onBack}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <h3 className="text-lg font-semibold text-gray-900">
              Room Details
            </h3>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Selection Summary */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="text-sm font-medium text-blue-900 mb-2">Selected Configuration:</h5>
            <div className="text-sm text-blue-800 space-y-1">
              <p><strong>Destination:</strong> {destination.name}</p>
              <p><strong>Hotel:</strong> {hotel.name} (⭐ {hotel.rating})</p>
              <p><strong>Room Type:</strong> {room.name}</p>
              <p><strong>Capacity:</strong> {room.capacity} guests</p>
              <p><strong>Original Price:</strong> {room.currency} {room.price}/night</p>
            </div>
          </div>

          {/* Room Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Display Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Enter display name for this room"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              This is how the room will appear in the quotation
            </p>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Describe this room configuration"
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {/* Price */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Price per Night
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">$</span>
              <input
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange("price", parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                min="0"
                step="0.01"
                className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Adjust the price if needed
            </p>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Room Image
            </label>
            
            {formData.imageUrl ? (
              <div className="relative">
                <div className="relative h-48 w-full rounded-lg overflow-hidden border border-gray-200">
                  <Image
                    src={formData.imageUrl}
                    alt="Room image preview"
                    fill
                    className="object-cover"
                  />
                </div>
                <button
                  type="button"
                  onClick={handleRemoveImage}
                  className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <div
                onClick={() => fileInputRef.current?.click()}
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 cursor-pointer transition-colors"
              >
                <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-sm font-medium text-gray-900 mb-1">
                  {isUploading ? "Uploading..." : "Click to upload room image"}
                </p>
                <p className="text-xs text-gray-500">
                  PNG, JPG, GIF up to 10MB
                </p>
              </div>
            )}

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              disabled={isUploading}
            />
            
            <p className="text-xs text-gray-500 mt-2">
              Upload a custom image or keep the original room image
            </p>
          </div>

          {/* Room Features */}
          {room.features && room.features.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Room Features
              </label>
              <div className="flex flex-wrap gap-2">
                {room.features.map((feature, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Include in Quote */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isSelected"
              checked={formData.isSelected}
              onChange={(e) => handleInputChange("isSelected", e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isSelected" className="ml-2 block text-sm text-gray-700">
              Include in quotation immediately
            </label>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onBack}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Back to Selection
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isUploading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {isUploading ? "Uploading..." : "Add Room"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
