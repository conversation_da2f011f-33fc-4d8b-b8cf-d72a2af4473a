"use client";

import React, { useState } from "react";
import { useQuotationBuilder, BuilderProduct } from "../quotation-builder-context";
import { Plus, ShoppingBag, Bed, Star, Edit, Trash2, Check } from "lucide-react";
import { RoomSelectionForm } from "./room-selection-form";
import { AddonSelectionForm } from "./addon-selection-form";
import Image from "next/image";

export function ProductsManager() {
  const { state, dispatch } = useQuotationBuilder();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<BuilderProduct | null>(null);
  const [activeTab, setActiveTab] = useState<"products" | "addons">("products");

  const handleAddProduct = (type: BuilderProduct["type"]) => {
    setEditingProduct({
      id: "",
      type,
      sourceId: "",
      name: "",
      description: "",
      price: 0,
      isSelected: false,
    });
    setIsFormOpen(true);
  };

  const handleEditProduct = (product: BuilderProduct) => {
    setEditingProduct(product);
    setIsFormOpen(true);
  };

  const handleDeleteProduct = (productId: string) => {
    if (confirm("Are you sure you want to delete this item?")) {
      dispatch({ type: "REMOVE_PRODUCT", payload: productId });
    }
  };

  const handleToggleSelection = (productId: string) => {
    dispatch({ type: "TOGGLE_PRODUCT_SELECTION", payload: productId });
    // Recalculate total after selection change
    setTimeout(() => {
      dispatch({ type: "CALCULATE_TOTAL" });
    }, 0);
  };

  const handleSaveProduct = (productData: Omit<BuilderProduct, "id">) => {
    if (editingProduct?.id) {
      // Update existing product
      dispatch({
        type: "UPDATE_PRODUCT",
        payload: {
          ...productData,
          id: editingProduct.id,
        },
      });
    } else {
      // Add new product
      const newProduct: BuilderProduct = {
        ...productData,
        id: `product_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      };
      dispatch({ type: "ADD_PRODUCT", payload: newProduct });
    }
    setIsFormOpen(false);
    setEditingProduct(null);
    
    // Recalculate total
    setTimeout(() => {
      dispatch({ type: "CALCULATE_TOTAL" });
    }, 0);
  };

  const handleCancelForm = () => {
    setIsFormOpen(false);
    setEditingProduct(null);
  };



  const products = state.products;
  const addons = state.addons;
  const currentItems = activeTab === "products" ? products : addons;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-sm font-medium text-gray-700">Products & Services</h4>
          <p className="text-xs text-gray-500 mt-1">
            Add products and services to your quotation
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab("products")}
          className={`flex-1 px-3 py-2 text-xs font-medium rounded-md transition-colors ${
            activeTab === "products"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
        >
          Products ({products.length})
        </button>
        <button
          onClick={() => setActiveTab("addons")}
          className={`flex-1 px-3 py-2 text-xs font-medium rounded-md transition-colors ${
            activeTab === "addons"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
        >
          Add-ons ({addons.length})
        </button>
      </div>

      {/* Add Buttons */}
      <div className="grid grid-cols-1 gap-2">
        {activeTab === "products" ? (
          <button
            onClick={() => handleAddProduct("room")}
            className="flex items-center justify-center px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100"
          >
            <Bed className="h-3 w-3 mr-1" />
            Add Room
          </button>
        ) : (
          <button
            onClick={() => handleAddProduct("addon")}
            className="flex items-center justify-center px-3 py-2 text-xs font-medium text-green-600 bg-green-50 border border-green-200 rounded-md hover:bg-green-100"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Add-on
          </button>
        )}
      </div>

      {/* Items List */}
      {currentItems.length > 0 ? (
        <div className="space-y-3">
          {currentItems.map((item) => (
            <div
              key={item.id}
              className={`relative p-4 border-2 rounded-lg transition-all ${
                item.isSelected
                  ? "border-blue-300 bg-blue-50"
                  : "border-gray-200 bg-white hover:border-gray-300"
              }`}
            >
              {/* Selection Indicator */}
              <div className="absolute top-2 right-2">
                <button
                  onClick={() => handleToggleSelection(item.id)}
                  className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ${
                    item.isSelected
                      ? "bg-blue-600 border-blue-600 text-white"
                      : "border-gray-300 hover:border-blue-400"
                  }`}
                >
                  {item.isSelected && <Check className="h-3 w-3" />}
                </button>
              </div>

              <div className="flex space-x-3">
                {/* Image */}
                <div className="flex-shrink-0">
                  {item.imageUrl ? (
                    <div className="w-16 h-16 relative rounded-lg overflow-hidden">
                      <Image
                        src={item.imageUrl}
                        alt={item.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                      {item.type === "room" ? <Bed className="h-4 w-4" /> : <Star className="h-4 w-4" />}
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <h5 className="text-sm font-medium text-gray-900 truncate pr-8">
                      {item.name || "Untitled Product"}
                    </h5>
                  </div>

                  {item.description && (
                    <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                      {item.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {item.price !== undefined && (
                        <span className="text-sm font-semibold text-gray-900">
                          ${item.price.toFixed(2)}
                        </span>
                      )}
                    </div>

                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleEditProduct(item)}
                        className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                        title="Edit product"
                      >
                        <Edit className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => handleDeleteProduct(item.id)}
                        className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                        title="Delete product"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Selection Status */}
              {item.isSelected && (
                <div className="mt-3 pt-3 border-t border-blue-200">
                  <div className="flex items-center text-xs text-blue-700">
                    <Check className="h-3 w-3 mr-1" />
                    <span>Included in quotation</span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <ShoppingBag className="h-8 w-8 mx-auto mb-2 text-gray-300" />
          <p className="text-sm">No {activeTab} added yet</p>
          <p className="text-xs mt-1">Click the buttons above to get started</p>
        </div>
      )}

      {/* Selection Summary */}
      {(products.some(p => p.isSelected) || addons.some(a => a.isSelected)) && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h5 className="text-sm font-medium text-blue-900 mb-2">Selected for Quote:</h5>
          <div className="space-y-1">
            {[...products, ...addons]
              .filter(item => item.isSelected)
              .map(item => (
                <div key={item.id} className="flex items-center justify-between text-xs">
                  <span className="text-blue-800">{item.name}</span>
                  <span className="font-medium text-blue-900">
                    ${(item.price || 0).toFixed(2)}
                  </span>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Product Form Modal */}
      {isFormOpen && editingProduct && (
        <>
          {editingProduct.type === "room" ? (
            <RoomSelectionForm
              product={editingProduct}
              onSave={handleSaveProduct}
              onCancel={handleCancelForm}
            />
          ) : editingProduct.type === "addon" ? (
            <AddonSelectionForm
              product={editingProduct}
              onSave={handleSaveProduct}
              onCancel={handleCancelForm}
            />
          ) : null}
        </>
      )}
    </div>
  );
}
