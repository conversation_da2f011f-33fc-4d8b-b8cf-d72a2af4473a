"use client";

import React, { useState, useRef } from "react";
import { CustomSection } from "../quotation-builder-context";
import { X, Upload, Image as ImageIcon } from "lucide-react";
import Image from "next/image";

interface CustomSectionFormProps {
  section?: CustomSection | null;
  onSave: (sectionData: Omit<CustomSection, "id" | "order">) => void;
  onCancel: () => void;
}

export function CustomSectionForm({ section, onSave, onCancel }: CustomSectionFormProps) {
  const [formData, setFormData] = useState({
    type: section?.type || "text" as CustomSection["type"],
    title: section?.title || "",
    content: section?.content || "",
    imageUrl: section?.imageUrl || "",
    imageAlt: section?.imageAlt || "",
  });
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setIsUploading(true);
    try {
      // For now, create a local URL for preview
      const imageUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, imageUrl }));
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    if (formData.imageUrl) {
      URL.revokeObjectURL(formData.imageUrl);
    }
    setFormData(prev => ({ ...prev, imageUrl: "", imageAlt: "" }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.title.trim()) {
      alert('Please enter a section title');
      return;
    }

    if (formData.type === "text" && !formData.content.trim()) {
      alert('Please enter section content');
      return;
    }

    if (formData.type === "image" && !formData.imageUrl) {
      alert('Please upload an image');
      return;
    }

    if (formData.type === "text_image" && (!formData.content.trim() || !formData.imageUrl)) {
      alert('Please enter content and upload an image');
      return;
    }

    onSave(formData);
  };

  const showImageUpload = formData.type === "image" || formData.type === "text_image";
  const showContentEditor = formData.type === "text" || formData.type === "text_image";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {section ? "Edit Section" : "Add New Section"}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Section Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Section Type
            </label>
            <select
              value={formData.type}
              onChange={(e) => handleInputChange("type", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="text">Text Only</option>
              <option value="image">Image Only</option>
              <option value="text_image">Text + Image</option>
            </select>
          </div>

          {/* Section Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Section Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              placeholder="Enter section title"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {/* Content Editor */}
          {showContentEditor && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content *
              </label>
              <textarea
                value={formData.content}
                onChange={(e) => handleInputChange("content", e.target.value)}
                placeholder="Enter section content"
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required={showContentEditor}
              />
              <p className="text-xs text-gray-500 mt-1">
                Basic HTML tags are supported for formatting
              </p>
            </div>
          )}

          {/* Image Upload */}
          {showImageUpload && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image {formData.type === "text_image" ? "*" : ""}
              </label>
              
              {formData.imageUrl ? (
                <div className="relative">
                  <div className="relative h-48 w-full rounded-lg overflow-hidden border border-gray-200">
                    <Image
                      src={formData.imageUrl}
                      alt="Section image preview"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={handleRemoveImage}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 cursor-pointer"
                >
                  <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-sm font-medium text-gray-900 mb-1">
                    {isUploading ? "Uploading..." : "Click to upload image"}
                  </p>
                  <p className="text-xs text-gray-500">
                    PNG, JPG, GIF up to 10MB
                  </p>
                </div>
              )}

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                disabled={isUploading}
              />

              {/* Image Alt Text */}
              {formData.imageUrl && (
                <div className="mt-3">
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Image Alt Text
                  </label>
                  <input
                    type="text"
                    value={formData.imageAlt}
                    onChange={(e) => handleInputChange("imageAlt", e.target.value)}
                    placeholder="Describe the image for accessibility"
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isUploading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isUploading ? "Uploading..." : section ? "Update Section" : "Add Section"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
