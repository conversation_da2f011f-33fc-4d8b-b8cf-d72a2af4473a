"use client";

import React, { useRef, useState } from "react";
import { useQuotationBuilder } from "../quotation-builder-context";
import { Upload, X, Image as ImageIcon } from "lucide-react";
import Image from "next/image";

export function CoverImageSection() {
  const { state, dispatch } = useQuotationBuilder();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setIsUploading(true);

    try {
      // For now, create a local URL for preview
      // In production, this would upload to your storage service
      const imageUrl = URL.createObjectURL(file);
      dispatch({ type: "SET_COVER_IMAGE", payload: imageUrl });
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    if (state.coverImage) {
      URL.revokeObjectURL(state.coverImage);
    }
    dispatch({ type: "SET_COVER_IMAGE", payload: "" });
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Cover Image
        </label>
        <p className="text-xs text-gray-500 mb-3">
          Upload a hero image for your quotation (max 10MB)
        </p>
      </div>

      {state.coverImage ? (
        <div className="relative">
          <div className="relative h-48 w-full rounded-lg overflow-hidden border border-gray-200">
            <Image
              src={state.coverImage}
              alt="Cover image preview"
              fill
              className="object-cover"
            />
          </div>
          <button
            onClick={handleRemoveImage}
            className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ) : (
        <div
          onClick={handleUploadClick}
          className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 cursor-pointer transition-colors"
        >
          <div className="flex flex-col items-center">
            <ImageIcon className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-sm font-medium text-gray-900 mb-1">
              {isUploading ? "Uploading..." : "Click to upload cover image"}
            </p>
            <p className="text-xs text-gray-500">
              PNG, JPG, GIF up to 10MB
            </p>
          </div>
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
        disabled={isUploading}
      />

      {!state.coverImage && (
        <div className="flex space-x-2">
          <button
            onClick={handleUploadClick}
            disabled={isUploading}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            <Upload className="h-4 w-4 mr-2" />
            {isUploading ? "Uploading..." : "Upload Image"}
          </button>
        </div>
      )}
    </div>
  );
}
