"use client";

import React, { useState } from "react";
import { useQuotationBuilder } from "./quotation-builder-context";
import { Button } from "@flinkk/components/ui/button";
import { Download, Check, MessageSquare, Phone, Mail, MapPin, Calendar, Users, ArrowRight } from "lucide-react";
import Image from "next/image";

interface DynamicQuotationPreviewProps {
  viewMode?: "desktop" | "mobile";
}

export function DynamicQuotationPreview({ viewMode = "desktop" }: DynamicQuotationPreviewProps) {
  const { state, dispatch } = useQuotationBuilder();
  const [isApproving, setIsApproving] = useState(false);

  // Get selected products and addons
  const selectedProducts = state.products.filter(p => p.isSelected);
  const selectedAddons = state.addons.filter(a => a.isSelected);
  const allSelectedItems = [...selectedProducts, ...selectedAddons];

  const handleApprove = async () => {
    setIsApproving(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));
    dispatch({ type: "SET_STATUS", payload: "accepted" });
    setIsApproving(false);
  };

  const handleReject = () => {
    dispatch({ type: "SET_STATUS", payload: "rejected" });
  };

  const handleRequestChanges = () => {
    dispatch({ type: "SET_STATUS", payload: "changes_requested" });
  };

  const handleDownloadPDF = () => {
    console.log("Generating PDF...");
  };

  const isMobile = viewMode === "mobile";

  return (
    <div className="min-h-screen bg-white">
      {/* Header Hero Section */}
      <div className={`relative ${isMobile ? 'h-[50vh]' : 'h-[70vh]'} bg-slate-900 overflow-hidden`}>
        {state.coverImage ? (
          <Image
            src={state.coverImage}
            alt="Cover Image"
            fill
            className="object-cover opacity-45"
            priority
          />
        ) : (
          <Image
            src="/images/alpine-hero-background.jpg"
            alt="Default Cover Image"
            fill
            className="object-cover opacity-45"
            priority
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900/30 via-slate-900/50 to-slate-900/70" />
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center text-white px-6">
          <div className="max-w-4xl">
            <div className="mb-8">
              <div className="inline-flex items-center space-x-3 mb-4">
                <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/20">
                  <span className="text-white font-bold text-xl">PB</span>
                </div>
                <div className="text-left">
                  <h1 className="text-2xl font-light tracking-wide">POWDER BYRNE</h1>
                  <p className="text-sm opacity-80 tracking-widest">LUXURY MOUNTAIN EXPERIENCES</p>
                </div>
              </div>
            </div>
            <h2 className={`${
              isMobile
                ? 'text-2xl sm:text-3xl'
                : 'text-3xl sm:text-4xl md:text-5xl lg:text-6xl'
            } font-light mb-6 tracking-wide ${
              state.textStyle.headingFont === 'serif' ? 'font-serif' : 'font-sans'
            }`}>
              YOUR BESPOKE
              <br />
              <span className="font-normal">MOUNTAIN EXPERIENCE</span>
            </h2>
            <p className={`${
              isMobile
                ? 'text-base sm:text-lg'
                : 'text-lg sm:text-xl md:text-2xl'
            } font-light opacity-90 max-w-2xl mx-auto leading-relaxed px-4`}>
              A curated quotation crafted exclusively for you
            </p>
            <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm opacity-80">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4" />
                <span>Quote Date: {new Date().toLocaleDateString()}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>Status: {state.status.replace('_', ' ').toUpperCase()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Introduction Section */}
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        <div className="py-12 md:py-16 text-center">
          <h3 className="text-2xl sm:text-3xl font-light text-slate-800 mb-6 px-4">
            A DEDICATED SERVICE FROM THE
            <br />
            <span className="font-normal">MOMENT YOU BOOK</span>
          </h3>
          <p className="text-base md:text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed mb-8 px-4">
            Our team of mountain experts have carefully curated this bespoke experience, ensuring every detail reflects
            your preferences and exceeds your expectations. From arrival to departure, we orchestrate seamless luxury in
            the heart of the Swiss Alps.
          </p>
        </div>
      </div>

      {/* Custom Sections */}
      {state.customSections.length > 0 && (
        <div className="space-y-16">
          {state.customSections.map((section, index) => (
            <div key={section.id} className="mb-8 lg:mb-16">
              <div className={`hidden lg:flex h-[90vh] w-full ${index % 2 === 1 ? "flex-row-reverse" : ""}`}>
                {/* Image Section - 65% width */}
                {section.imageUrl && (
                  <div className="w-[65%] relative">
                    <Image
                      src={section.imageUrl}
                      alt={section.imageAlt || section.title || "Section image"}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                
                {/* Content Section - 35% width */}
                <div className={`${section.imageUrl ? 'w-[35%]' : 'w-full'} bg-white flex items-center justify-center p-8 lg:p-16`}>
                  <div className="max-w-lg">
                    {section.title && (
                      <h3 className="text-2xl lg:text-3xl font-bold text-slate-900 mb-6 tracking-wide leading-tight">
                        {section.title.toUpperCase()}
                      </h3>
                    )}
                    {section.content && (
                      <div 
                        className="text-slate-600 leading-relaxed text-base lg:text-lg"
                        dangerouslySetInnerHTML={{ __html: section.content }}
                      />
                    )}
                  </div>
                </div>
              </div>

              {/* Mobile Layout */}
              <div className={`${isMobile ? 'block' : 'lg:hidden'} px-4`}>
                {section.imageUrl && (
                  <div className="relative h-[40vh] w-full mb-4 rounded-lg overflow-hidden">
                    <Image
                      src={section.imageUrl}
                      alt={section.imageAlt || section.title || "Section image"}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}

                <div className="bg-white rounded-lg p-4 shadow-lg">
                  {section.title && (
                    <h3 className="text-lg font-bold text-slate-900 mb-2 tracking-wide leading-tight">
                      {section.title.toUpperCase()}
                    </h3>
                  )}
                  {section.content && (
                    <div 
                      className="text-slate-600 leading-relaxed text-sm"
                      dangerouslySetInnerHTML={{ __html: section.content }}
                    />
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Selected Products/Services Sections */}
      {allSelectedItems.length > 0 && (
        <div className="space-y-8 lg:space-y-16">
          {allSelectedItems.map((item, index) => (
            <div key={item.id} className="mb-8 lg:mb-16">
              {/* Desktop Layout */}
              <div className={`${isMobile ? 'hidden' : 'hidden lg:flex'} min-h-[70vh] w-full ${index % 2 === 1 ? "flex-row-reverse" : ""}`}>
                {/* Image Section - 65% width */}
                {item.imageUrl && (
                  <div className="w-[65%] relative">
                    <Image
                      src={item.imageUrl}
                      alt={item.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}

                {/* Content Section - 35% width */}
                <div className={`${item.imageUrl ? 'w-[35%]' : 'w-full'} bg-white flex items-center justify-center p-8 lg:p-16`}>
                  <div className="max-w-lg">
                    <h3 className="text-2xl lg:text-3xl font-bold text-slate-900 mb-6 tracking-wide leading-tight">
                      {item.name.toUpperCase()}
                    </h3>
                    <p className="text-slate-600 leading-relaxed text-base lg:text-lg mb-4">
                      {item.description}
                    </p>
                    {item.price && (
                      <div className="text-slate-800 font-semibold text-lg">
                        ${item.price.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Mobile Layout */}
              <div className="lg:hidden px-4">
                {item.imageUrl && (
                  <div className="relative h-[30vh] w-full mb-4 rounded-lg overflow-hidden">
                    <Image
                      src={item.imageUrl}
                      alt={item.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}

                <div className="bg-white rounded-lg p-4 shadow-lg mb-4">
                  <h3 className="text-lg font-bold text-slate-900 mb-2 tracking-wide leading-tight">
                    {item.name.toUpperCase()}
                  </h3>
                  <p className="text-slate-600 leading-relaxed text-sm mb-3">
                    {item.description}
                  </p>
                  {item.price && (
                    <div className="text-slate-800 font-semibold text-base">
                      ${item.price.toFixed(2)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pricing Summary */}
      <div className={`bg-slate-50 ${isMobile ? 'py-8' : 'py-16'}`}>
        <div className={`${isMobile ? 'max-w-sm' : 'max-w-4xl'} mx-auto px-4 md:px-6`}>
          <div className={`bg-white rounded-lg shadow-lg ${isMobile ? 'p-4' : 'p-8'}`}>
            <h3 className={`${
              isMobile ? 'text-lg' : 'text-2xl'
            } font-bold text-slate-900 mb-4 sm:mb-6 text-center`}>
              INVESTMENT SUMMARY
            </h3>

            <div className="space-y-4 mb-6">
              {allSelectedItems.map((item) => (
                <div key={item.id} className="flex justify-between items-center py-2 border-b border-slate-200">
                  <span className="text-slate-700">{item.name}</span>
                  <span className="font-semibold text-slate-900">
                    ${(item.price || 0).toFixed(2)}
                  </span>
                </div>
              ))}
            </div>

            <div className="border-t-2 border-slate-900 pt-4 mb-6">
              <div className="flex justify-between items-center">
                <span className="text-xl font-bold text-slate-900">TOTAL INVESTMENT</span>
                <span className="text-2xl font-bold text-slate-900">
                  ${state.totalPrice.toFixed(2)}
                </span>
              </div>
            </div>

            {/* Deposit Section */}
            <div className={`bg-green-50 border border-green-200 rounded-lg ${isMobile ? 'p-4' : 'p-6'}`}>
              <div className="text-center">
                <h4 className={`${
                  isMobile ? 'text-base' : 'text-lg'
                } font-bold text-green-900 mb-2`}>
                  TO CONTINUE, PAY THIS SMALL AMOUNT
                </h4>
                <div className={`${
                  isMobile ? 'text-2xl' : 'text-3xl'
                } font-bold text-green-900 mb-2`}>
                  ${(state.totalPrice * 0.1).toFixed(2)}
                </div>
                <p className={`${
                  isMobile ? 'text-xs' : 'text-sm'
                } text-green-700 mb-3 sm:mb-4`}>
                  Secure your booking with just 10% deposit
                </p>
                <div className={`grid grid-cols-2 gap-2 sm:gap-4 ${
                  isMobile ? 'text-xs' : 'text-sm'
                }`}>
                  <div className="text-center">
                    <div className="font-semibold text-green-900">Deposit (10%)</div>
                    <div className="text-green-700">${(state.totalPrice * 0.1).toFixed(2)}</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-green-900">Remaining Balance</div>
                    <div className="text-green-700">${(state.totalPrice * 0.9).toFixed(2)}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className={`${isMobile ? 'py-8 px-4' : 'py-16'} text-center`}>
        <h3 className={`${
          isMobile ? 'text-xl sm:text-2xl' : 'text-3xl'
        } font-light text-slate-800 mb-6 tracking-wide`}>
          SECURE YOUR EXPERIENCE
        </h3>
        <div className={`${isMobile ? 'max-w-sm' : 'max-w-2xl'} mx-auto`}>
          <div className="flex flex-col gap-3 mb-6">
            <Button
              onClick={handleApprove}
              disabled={isApproving}
              className={`w-full bg-slate-800 hover:bg-slate-700 text-white ${
                isMobile ? 'py-3 text-base' : 'py-4 text-lg'
              } font-light tracking-wide`}
            >
              <Check className="w-4 h-4 mr-2" />
              {isApproving ? "PROCESSING..." : "APPROVE QUOTE"}
            </Button>
            <Button
              onClick={handleRequestChanges}
              variant="outline"
              className={`w-full ${
                isMobile ? 'py-3 text-base' : 'py-4 text-lg'
              } font-light tracking-wide border-slate-300 bg-transparent`}
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              REQUEST CHANGES
            </Button>
          </div>
          <div className="flex flex-col gap-3">
            <Button
              onClick={handleReject}
              variant="outline"
              className={`w-full ${
                isMobile ? 'py-2 px-4 text-sm' : 'py-3 px-6 text-base'
              } font-light tracking-wide border-red-300 text-red-600 hover:bg-red-50`}
            >
              DECLINE QUOTE
            </Button>
            <Button
              onClick={handleDownloadPDF}
              variant="outline"
              className={`w-full ${
                isMobile ? 'py-2 px-4 text-sm' : 'py-3 px-6 text-base'
              } font-light tracking-wide border-slate-300 bg-transparent`}
            >
              <Download className="w-4 h-4 mr-2" />
              DOWNLOAD PDF
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
