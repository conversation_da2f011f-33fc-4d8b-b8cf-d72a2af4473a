"use client";

import React, { useState } from "react";
import { useQuotationBuilder } from "./quotation-builder-context";
import { CoverImageSection } from "./sections/cover-image-section";
// import { TextStyleSection } from "./sections/text-style-section";
// import { CustomSectionsManager } from "./sections/custom-sections-manager";
import { ProductsManager } from "./sections/products-manager";
import { PricingSection } from "./sections/pricing-section";
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react";

interface CollapsibleSectionProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}

function CollapsibleSection({ 
  title, 
  children, 
  defaultOpen = true 
}: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="border-b border-gray-200">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50"
      >
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        {isOpen ? (
          <ChevronDownIcon className="h-5 w-5 text-gray-500" />
        ) : (
          <ChevronRightIcon className="h-5 w-5 text-gray-500" />
        )}
      </button>
      {isOpen && (
        <div className="px-6 pb-6">
          {children}
        </div>
      )}
    </div>
  );
}

export function LeftPane() {
  const { state } = useQuotationBuilder();

  return (
    <div className="h-full">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">
          Quote Configuration
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Configure your quotation settings and content
        </p>
      </div>

      {/* Sections */}
      <div className="space-y-0">
        <CollapsibleSection title="Cover & Branding">
          <div className="space-y-6">
            <CoverImageSection />
            {/* <TextStyleSection /> */}
          </div>
        </CollapsibleSection>

        {/* <CollapsibleSection title="Custom Sections">
          <CustomSectionsManager />
        </CollapsibleSection> */}

        <CollapsibleSection title="Products & Services">
          <ProductsManager />
        </CollapsibleSection>

        <CollapsibleSection title="Pricing">
          <PricingSection />
        </CollapsibleSection>
      </div>

      {/* Footer with status */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Status: <span className="font-medium capitalize">{state.status}</span>
          </span>
          <span className="text-gray-600">
            Total: <span className="font-medium">${state.totalPrice.toFixed(2)}</span>
          </span>
        </div>
      </div>
    </div>
  );
}
