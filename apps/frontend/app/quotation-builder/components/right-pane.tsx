"use client";

import React, { useState } from "react";
import { useQuotationBuilder } from "./quotation-builder-context";
import { DynamicQuotationPreview } from "./dynamic-quotation-preview";

export function RightPane() {
  const { state } = useQuotationBuilder();
  const [viewMode, setViewMode] = useState<"desktop" | "mobile">("desktop");

  return (
    <div className="h-full">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Live Preview
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              See how your quotation will look to clients
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode("desktop")}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                viewMode === "desktop"
                  ? "text-gray-700 bg-gray-100"
                  : "text-gray-500 bg-white border border-gray-300 hover:bg-gray-50"
              }`}
            >
              Desktop
            </button>
            <button
              onClick={() => setViewMode("mobile")}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                viewMode === "mobile"
                  ? "text-gray-700 bg-gray-100"
                  : "text-gray-500 bg-white border border-gray-300 hover:bg-gray-50"
              }`}
            >
              Mobile
            </button>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="h-[calc(100%-80px)] overflow-y-auto bg-white">
        <div className={`${
          viewMode === "mobile"
            ? "max-w-sm mx-auto border-l border-r border-gray-200 min-h-full"
            : "w-full"
        }`}>
          <DynamicQuotationPreview viewMode={viewMode} />
        </div>
      </div>
    </div>
  );
}
