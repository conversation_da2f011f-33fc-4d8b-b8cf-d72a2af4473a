"use client";

import React, { useState } from "react";
import { X, Co<PERSON>, ExternalLink, Check } from "lucide-react";
import { useQuotationBuilder } from "./quotation-builder-context";

interface SendQuoteModalProps {
  onClose: () => void;
}

export function SendQuoteModal({ onClose }: SendQuoteModalProps) {
  const { state, dispatch } = useQuotationBuilder();
  const [copied, setCopied] = useState(false);
  
  // Generate a mock quote ID and URL
  const quoteId = `quote_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  const quoteUrl = `${window.location.origin}/quote-preview/${quoteId}`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(quoteUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleOpenPreview = () => {
    window.open(quoteUrl, '_blank');
  };

  const handleSendQuote = () => {
    // Update quote status to sent
    dispatch({ type: "SET_STATUS", payload: "sent" });
    
    // In a real implementation, this would:
    // 1. Save the quote to the database
    // 2. Generate the preview URL
    // 3. Send email notification if needed
    
    console.log('Quote sent with ID:', quoteId);
    console.log('Quote URL:', quoteUrl);
  };

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Send Quotation
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Quote Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Quote Summary</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Total Items:</span>
                <span>{[...state.products, ...state.addons].filter(item => item.isSelected).length}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Value:</span>
                <span className="font-semibold">${state.totalPrice.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Status:</span>
                <span className="capitalize">{state.status}</span>
              </div>
            </div>
          </div>

          {/* Quote Link */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shareable Quote Link
            </label>
            <div className="flex items-center space-x-2">
              <div className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-600 truncate">
                {quoteUrl}
              </div>
              <button
                onClick={handleCopyLink}
                className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                title="Copy link"
              >
                {copied ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
              </button>
              <button
                onClick={handleOpenPreview}
                className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                title="Open preview"
              >
                <ExternalLink className="h-4 w-4" />
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Share this link with your client to view and respond to the quotation
            </p>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="text-sm font-medium text-blue-900 mb-2">Next Steps:</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Copy the link above and share it with your client</li>
              <li>• Your client can view the full quotation and respond</li>
              <li>• You'll be notified when they accept, reject, or request changes</li>
              <li>• The quotation status will update automatically</li>
            </ul>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              handleSendQuote();
              onClose();
            }}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors"
          >
            Send Quotation
          </button>
        </div>
      </div>
    </div>
  );
}
