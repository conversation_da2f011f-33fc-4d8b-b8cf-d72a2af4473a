"use client"

import { useState } from "react"
import { But<PERSON> } from "@flinkk/components/ui/button"

import { Download, Check, MessageSquare, Phone, Mail, MapPin, Calendar, Users, ArrowRight } from "lucide-react"
import Image from "next/image"

interface ServiceItem {
  id: string
  name: string
  image: string
  description: string
  quantity: number
  unitPrice: number
  currency: string
}

interface QuotationData {
  quoteNumber: string
  dateIssued: string
  clientName: string
  services: ServiceItem[]
  taxRate: number
  currency: string
  status: "draft" | "sent" | "approved" | "rejected"
}

const sampleQuotation: QuotationData = {
  quoteNumber: "PB-2024-001",
  dateIssued: "January 18, 2024",
  clientName: "<PERSON>lashri",
  currency: "CHF",
  taxRate: 0.1,
  status: "sent",
  services: [
    {
      id: "1",
      name: "Private Ski Butler Service",
      image: "/images/ski-butler-couple.jpg",
      description:
        "Our dedicated ski butler service provides personalized attention from the moment you arrive. Expert equipment fitting, daily slope preparation, and mountain guidance ensure your skiing experience is seamless and exceptional.",
      quantity: 7,
      unitPrice: 450,
      currency: "CHF",
    },
    {
      id: "2",
      name: "Luxury Chalet Accommodation",
      image: "/images/luxury-chalet.jpg",
      description:
        "Exclusive 5-bedroom chalet with panoramic mountain views, private chef service, and dedicated concierge support. Features include spa facilities, wine cellar, and heated outdoor terrace with breathtaking alpine vistas.",
      quantity: 7,
      unitPrice: 1200,
      currency: "CHF",
    },
    {
      id: "3",
      name: "Private Shuttle Service",
      image: "/images/luxury-vehicle.avif",
      description:
        "Luxury vehicle transfers with professional chauffeur service. Our fleet of premium vehicles ensures comfortable and stylish transportation throughout your mountain experience.",
      quantity: 2,
      unitPrice: 800,
      currency: "CHF",
    },
    {
      id: "4",
      name: "Mountain Host Experience",
      image: "/images/mountain-hosts.jpg",
      description:
        "Personal mountain hosts to guide you through the finest dining, après-ski venues, and exclusive mountain experiences. Local expertise combined with personalized service.",
      quantity: 7,
      unitPrice: 300,
      currency: "CHF",
    },
  ],
}

export default function QuotationView() {
  const [quotation] = useState<QuotationData>(sampleQuotation)
  const [isApproving, setIsApproving] = useState(false)

  const subtotal = quotation.services.reduce((sum, service) => sum + service.quantity * service.unitPrice, 0)
  const taxAmount = subtotal * quotation.taxRate
  const total = subtotal + taxAmount

  const handleApprove = async () => {
    setIsApproving(true)
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsApproving(false)
  }

  const handleDownloadPDF = () => {
    console.log("Generating PDF...")
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header Hero Section */}
      <div className="relative h-[70vh] bg-slate-900 overflow-hidden">
        <Image
          src="/images/alpine-hero-background.jpg"
          alt="Pristine Alpine Mountain Peaks - Luxury Ski Resort Experience"
          fill
          className="object-cover opacity-45"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900/30 via-slate-900/50 to-slate-900/70" />
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center text-white px-6">
          <div className="max-w-4xl">
            <div className="mb-8">
              <div className="inline-flex items-center space-x-3 mb-4">
                <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/20">
                  <span className="text-white font-bold text-xl">PB</span>
                </div>
                <div className="text-left">
                  <h1 className="text-2xl font-light tracking-wide">POWDER BYRNE</h1>
                  <p className="text-sm opacity-80 tracking-widest">LUXURY MOUNTAIN EXPERIENCES</p>
                </div>
              </div>
            </div>
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-light mb-6 tracking-wide">
              YOUR BESPOKE
              <br />
              <span className="font-normal">MOUNTAIN EXPERIENCE</span>
            </h2>
            <p className="text-lg sm:text-xl md:text-2xl font-light opacity-90 max-w-2xl mx-auto leading-relaxed px-4">
              A curated quotation crafted exclusively for {quotation.clientName}
            </p>
            <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm opacity-80">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4" />
                <span>March 15-22, 2024</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>6 Guests</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                <span>Quote #{quotation.quoteNumber}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Introduction Section */}
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        <div className="py-12 md:py-16 text-center">
          <h3 className="text-2xl sm:text-3xl font-light text-slate-800 mb-6 px-4">
            A DEDICATED SERVICE FROM THE
            <br />
            <span className="font-normal">MOMENT YOU BOOK</span>
          </h3>
          <p className="text-base md:text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed mb-8 px-4">
            Our team of mountain experts have carefully curated this bespoke experience, ensuring every detail reflects
            your preferences and exceeds your expectations. From arrival to departure, we orchestrate seamless luxury in
            the heart of the Swiss Alps.
          </p>
        </div>
      </div>

      {/* Service Sections - Full Width */}
      <div className="space-y-16">
        {quotation.services.map((service, index) => (
          <div key={service.id} className="mb-8 lg:mb-16">
            {/* Desktop Layout */}
            <div className={`hidden lg:flex h-[90vh] w-full ${index % 2 === 1 ? "flex-row-reverse" : ""}`}>
              {/* Image Section - 65% width */}
              <div className="w-[65%] relative">
                <Image
                  src={service.image || "/placeholder.svg"}
                  alt={service.name}
                  fill
                  className="object-cover"
                  priority={index === 0}
                />

                {/* Content Card - Positioned Above Image */}
                <div className={`absolute top-1/2 -translate-y-1/2 ${index % 2 !== 1 ? 'left-[130%] -translate-x-full' : 'left-[-30%]'} z-10`}>
                  <div className="w-[520px]">
                    <div className="bg-white rounded-lg p-8 shadow-xl">
                      <div className="mb-6">
                        <h3 className="text-2xl font-bold text-slate-900 mb-3 tracking-wide">
                          {service.name.toUpperCase()}
                        </h3>
                      </div>

                      <p className="text-slate-600 leading-relaxed mb-8 text-base">
                        {service.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Empty Content Section - 35% width for layout balance */}
              <div className="w-[35%]"></div>
            </div>

            {/* Mobile Layout */}
            <div className="lg:hidden px-4">
              {/* Image Section */}
              <div className="relative h-[40vh] w-full mb-4 rounded-lg overflow-hidden">
                <Image
                  src={service.image || "/placeholder.svg"}
                  alt={service.name}
                  fill
                  className="object-cover"
                  priority={index === 0}
                />
              </div>

              {/* Content Card */}
              <div className="bg-white rounded-lg p-4 shadow-lg">
                <div className="mb-4">
                  <h3 className="text-lg font-bold text-slate-900 mb-2 tracking-wide leading-tight">
                    {service.name.toUpperCase()}
                  </h3>
                </div>

                <p className="text-slate-600 leading-relaxed text-sm">
                  {service.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Booking Section */}
      <div className="relative py-16 mt-16  overflow-hidden" style={{ backgroundColor: '#275DA6' }}>
        {/* Mountain silhouette overlay */}
        <div className="absolute bottom-0 right-0 w-1/2 h-full">
          <div className="absolute bottom-0 right-0 w-full h-3/4 transform skew-x-12 origin-bottom-right" style={{ background: 'linear-gradient(to top, rgba(39, 93, 166, 0.4), transparent)' }}></div>
          <div className="absolute bottom-0 right-20 w-3/4 h-2/3 transform skew-x-6 origin-bottom-right" style={{ background: 'linear-gradient(to top, rgba(39, 93, 166, 0.3), transparent)' }}></div>
        </div>

        <div className="relative z-10 flex flex-col items-center justify-center h-full text-white px-6">
          <div className="max-w-4xl w-full">
            {/* Personalized Message */}
            <div className="bg-white/15 backdrop-blur-md rounded-2xl p-6 md:p-10 mb-6 md:mb-8 border border-white/30 shadow-2xl">
              <h3 className="text-2xl md:text-3xl lg:text-4xl font-light mb-4 md:mb-6 text-white">
                Dear {quotation.clientName},
              </h3>
              <p className="text-lg md:text-xl font-light leading-relaxed text-white/90">
                Thank you for choosing Perfect Piste for your next getaway. Below are the details of the proposed itinerary.
              </p>
            </div>

            {/* Total Fee Display */}
            <div className="bg-white/15 backdrop-blur-md rounded-2xl p-6 md:p-8 mb-6 md:mb-8 border border-white/30 shadow-2xl">
              <div className="flex flex-col sm:flex-row justify-between items-center gap-2 sm:gap-0">
                <span className="text-xl md:text-2xl font-light text-white">Total Fee</span>
                <span className="text-2xl md:text-3xl font-bold text-white">CHF {total.toLocaleString()}</span>
              </div>
            </div>

            {/* Booking Fee Section */}
            <div className="bg-white/15 backdrop-blur-md rounded-2xl p-6 md:p-10 border border-white/30 shadow-2xl text-center">
              <h3 className="text-3xl md:text-4xl lg:text-5xl font-light mb-6 md:mb-8 tracking-wide text-white">
                Booking Fee
              </h3>
              <p className="text-lg md:text-xl font-light leading-relaxed text-white/90 mb-8 md:mb-10 max-w-2xl mx-auto px-4">
                A booking fee of <span className="font-semibold text-white">CHF 500</span> is required to confirm your itinerary. Once paid, we will begin planning your trip.
              </p>
              <Button
                onClick={handleApprove}
                disabled={isApproving}
                className="bg-white text-slate-800 hover:bg-gray-50 py-4 md:py-5 px-8 md:px-16 text-lg md:text-xl font-semibold tracking-wide rounded-xl shadow-xl transition-all duration-300 hover:shadow-2xl hover:scale-105 w-full sm:w-auto">
                {isApproving ? "PROCESSING..." : "PAY CHF 500"}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="py-16 text-center">
        <h3 className="text-3xl font-light text-slate-800 mb-8 tracking-wide">SECURE YOUR EXPERIENCE</h3>
        <div className="max-w-2xl mx-auto">
          <div className="flex flex-col sm:flex-row gap-4 mb-8">
            <Button
              onClick={handleApprove}
              disabled={isApproving}
              className="flex-1 bg-slate-800 hover:bg-slate-700 text-white py-4 text-lg font-light tracking-wide"
            >
              <Check className="w-5 h-5 mr-2" />
              {isApproving ? "PROCESSING..." : "APPROVE QUOTE"}
            </Button>
            <Button
              variant="outline"
              className="flex-1 py-4 text-lg font-light tracking-wide border-slate-300 bg-transparent"
            >
              <MessageSquare className="w-5 h-5 mr-2" />
              REQUEST CHANGES
            </Button>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button variant="ghost" onClick={handleDownloadPDF} className="text-slate-600 hover:text-slate-800">
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
            <Button variant="ghost" onClick={handleDownloadPDF} className="text-[#275DA6]">
              View Simplified Quotation
              <ArrowRight />
            </Button>
          </div>

          <p className="text-sm text-slate-500 mt-6 max-w-lg mx-auto">
            A 30% deposit secures your booking. Our concierge team will contact you within 24 hours to finalize
            arrangements and discuss any special requirements.
          </p>
        </div>
      </div>




      {/* Footer */ }
  <div className="bg-slate-800 text-white">
    <div className="max-w-6xl mx-auto px-6 py-16">
      <div className="text-center mb-12">
        <div className="inline-flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/20">
            <span className="text-white font-bold text-lg">PB</span>
          </div>
          <div className="text-left">
            <h4 className="text-xl font-light tracking-wide">POWDER BYRNE</h4>
            <p className="text-sm opacity-80 tracking-widest">LUXURY MOUNTAIN EXPERIENCES</p>
          </div>
        </div>
        <p className="text-lg text-white/90 mb-4 font-light max-w-2xl mx-auto">
          We look forward to creating an unforgettable mountain experience for you and your family.
        </p>
        <p className="text-white/70">
          Your dedicated concierge team is available 24/7 to assist with any requirements.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 text-center mb-12">
        <div className="flex flex-col items-center space-y-3">
          <div className="w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center">
            <Phone className="w-6 h-6 text-white" />
          </div>
          <span className="text-white font-medium">+41 27 123 4567</span>
          <span className="text-sm text-white/70">24/7 Concierge</span>
        </div>
        <div className="flex flex-col items-center space-y-3">
          <div className="w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center">
            <Mail className="w-6 h-6 text-white" />
          </div>
          <span className="text-white font-medium"><EMAIL></span>
          <span className="text-sm text-white/70">Direct Contact</span>
        </div>
        <div className="flex flex-col items-center space-y-3">
          <div className="w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center">
            <MapPin className="w-6 h-6 text-white" />
          </div>
          <span className="text-white font-medium">Verbier, Switzerland</span>
          <span className="text-sm text-white/70">Alpine Headquarters</span>
        </div>
      </div>

      <div className="border-t border-white/20 pt-8">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="text-sm text-white/60">
            © 2024 Powder Byrne. All rights reserved.
          </div>
          <div className="flex space-x-6 text-sm text-white/60">
            <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
            <a href="#" className="hover:text-white transition-colors">Terms of Service</a>
            <a href="#" className="hover:text-white transition-colors">Contact</a>
          </div>
        </div>
      </div>
    </div>
  </div>
    </div >
  )
}
