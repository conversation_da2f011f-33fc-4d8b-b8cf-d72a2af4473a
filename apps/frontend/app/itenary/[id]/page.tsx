'use client';

import React, { useEffect, useState } from 'react';
import { Hotel, Car, FileText, MapPin, Clock, Phone, Mail, Calendar, Download } from 'lucide-react';
import { useParams } from 'next/navigation';

// Types for the itinerary data - flexible to handle various API response formats
interface ItineraryData {
  id?: string;
  title?: string;
  name?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  start_date?: string;
  end_date?: string;
  pricePerPerson?: number;
  price_per_person?: number;
  price?: number;
  currency?: string;
  backgroundImage?: string;
  background_image?: string;
  customer?: {
    name?: string;
    email?: string;
    phone?: string;
    profileImage?: string;
    profile_image?: string;
  };
  client?: {
    name?: string;
    email?: string;
    phone?: string;
    profileImage?: string;
    profile_image?: string;
  };
  destinations?: Array<{
    id?: string;
    name?: string;
    description?: string;
    images?: string[];
    activities?: Array<{
      id?: string;
      type?: 'flight' | 'hotel' | 'transport' | 'activity' | string;
      title?: string;
      description?: string;
      time?: string;
      details?: Record<string, any>;
    }>;
  }>;
  // Fallback for any other structure
  [key: string]: any;
}



// Helper functions to safely extract data from API response
const getTitle = (data: ItineraryData) => data.title || data.name || 'Itinerary';
const getStartDate = (data: ItineraryData) => data.startDate || data.start_date || '';
const getEndDate = (data: ItineraryData) => data.endDate || data.end_date || '';
const getPrice = (data: ItineraryData) => data.pricePerPerson || data.price_per_person || data.price || 0;
const getCurrency = (data: ItineraryData) => data.currency || 'USD';
const getBackgroundImage = (data: ItineraryData) => data.backgroundImage || data.background_image;
const getCustomer = (data: ItineraryData) => {
  const customer = data.customer || data.client || {};
  return {
    name: customer.name || 'Guest',
    email: customer.email || '',
    phone: customer.phone || '',
    profileImage: customer.profileImage || customer.profile_image
  };
};
const getDestinations = (data: ItineraryData) => data.destinations || [];

export default function ItineraryPreviewPage() {
  const params = useParams();
  const id = params.id as string;

  const [itinerary, setItinerary] = useState<ItineraryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) return;

    const fetchItinerary = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/itinerary/${id}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        console.log('Response:', response);

        if (!response.ok) {
          throw new Error(`Failed to fetch itinerary: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        setItinerary(data);
      } catch (err) {
        console.error('Error fetching itinerary:', err);
        setError(err instanceof Error ? err.message : 'Failed to load itinerary');
      } finally {
        setLoading(false);
      }
    };

    fetchItinerary();
  }, [id]);

  // Loading state
  if (loading) {
    return (
      <div className="bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading itinerary...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !itinerary) {
    return (
      <div className="bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-xl font-semibold text-gray-800 mb-2">Itinerary Not Found</h1>
          <p className="text-gray-600 mb-4">
            {error || 'The requested itinerary could not be found or loaded.'}
          </p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Format dates
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  // Format price
  const formatPrice = (price: number, currency: string) => {
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency || 'USD'
      }).format(price);
    } catch {
      return `${currency || '$'}${price}`;
    }
  };

  // Extract data using helper functions
  const customer = getCustomer(itinerary);
  const title = getTitle(itinerary);
  const startDate = getStartDate(itinerary);
  const endDate = getEndDate(itinerary);
  const price = getPrice(itinerary);
  const currency = getCurrency(itinerary);
  const backgroundImage = getBackgroundImage(itinerary);
  const destinations = getDestinations(itinerary);

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header Section */}
      <div className="bg-gradient-to-br from-teal-500 via-cyan-500 to-blue-600 text-white">
        <div className="relative h-64 overflow-hidden">
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('${backgroundImage || 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'}')`
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-teal-600/80 via-cyan-600/70 to-blue-700/80"></div>
          </div>

          <div className="relative z-10 p-6 h-full flex flex-col justify-end">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-full overflow-hidden border-3 border-white/30 shadow-lg">
                  <img
                    src={customer.profileImage || "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">{customer.name}</h1>
                  {customer.email && (
                    <div className="flex items-center gap-1 text-white/90 text-sm mt-1">
                      <Mail className="w-3 h-3" />
                      <span>{customer.email}</span>
                    </div>
                  )}
                  {customer.phone && (
                    <div className="flex items-center gap-1 text-white/90 text-sm mt-0.5">
                      <Phone className="w-3 h-3" />
                      <span>{customer.phone}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="text-right">
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 border border-white/30">
                  {(startDate || endDate) && (
                    <div className="flex items-center gap-1 text-white/90 text-sm mb-1">
                      <Calendar className="w-3 h-3" />
                      <span>
                        {startDate && formatDate(startDate)}
                        {startDate && endDate && ' | '}
                        {endDate && formatDate(endDate)}
                      </span>
                    </div>
                  )}
                  <h2 className="text-xl font-bold text-white">{title}</h2>
                  {price > 0 && (
                    <p className="text-white/90 text-sm font-medium">
                      {formatPrice(price, currency)} per person
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6 space-y-6 -mt-8">
        {/* Render destinations and activities */}
        {destinations.length > 0 ? destinations.map((destination, destIndex) => (
          <React.Fragment key={destination.id}>
            {/* Information & Documents Card */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  INFORMATION & DOCUMENTS
                </h3>
              </div>
              
              <div className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <MapPin className="w-4 h-4 text-orange-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-800 mb-2">{destination.name || `Destination ${destIndex + 1}`}</h4>
                    {destination.description && (
                      <p className="text-gray-600 text-sm leading-relaxed mb-4">
                        {destination.description}
                      </p>
                    )}
                  </div>
                </div>

                {destination.images && destination.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-6">
                    {destination.images.slice(0, 4).map((image, imgIndex) => (
                      <img
                        key={imgIndex}
                        src={image}
                        alt={`${destination.name || 'Destination'} ${imgIndex + 1}`}
                        className="rounded-lg object-cover h-24 w-full"
                        onError={(e) => {
                          e.currentTarget.src = 'https://images.unsplash.com/photo-1544413966-1ad5ac882d5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80';
                        }}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Activities Card */}
            {destination.activities && destination.activities.length > 0 && (
              <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 border-b border-blue-200">
                  <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    {(destination.name || `DESTINATION ${destIndex + 1}`).toUpperCase()} ACTIVITIES
                  </h3>
                </div>

                <div className="p-6">
                  {(destination.activities || []).map((activity, actIndex) => (
                    <div key={activity.id || actIndex} className={`flex items-start gap-4 ${actIndex < (destination.activities || []).length - 1 ? 'mb-6' : ''}`}>
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                        {activity.type === 'flight' && (
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 font-bold text-sm">{actIndex + 1}</span>
                          </div>
                        )}
                        {activity.type === 'hotel' && (
                          <div className="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                            <Hotel className="w-4 h-4 text-teal-600" />
                          </div>
                        )}
                        {activity.type === 'transport' && (
                          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <Car className="w-4 h-4 text-purple-600" />
                          </div>
                        )}
                        {activity.type === 'activity' && (
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <span className="text-green-600 font-bold text-sm">{actIndex + 1}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex-1">
                        {activity.time && (
                          <div className="flex items-center gap-2 mb-2">
                            <Clock className="w-4 h-4 text-gray-500" />
                            <span className="font-semibold text-gray-800">{activity.time}</span>
                          </div>
                        )}
                        <h4 className="font-medium text-gray-800 mb-2">{activity.title}</h4>
                        <p className="text-gray-600 text-sm mb-3">{activity.description}</p>

                        {activity.details && Object.keys(activity.details).length > 0 && (
                          <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                            {Object.entries(activity.details).map(([key, value]) => (
                              <div key={key} className="flex justify-between items-center text-sm">
                                <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                                <span className="font-medium">{String(value)}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </React.Fragment>
        )) : (
          // Fallback content when no destinations are available
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <FileText className="w-5 h-5 text-blue-600" />
                ITINERARY INFORMATION
              </h3>
            </div>

            <div className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-4 h-4 text-orange-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-800 mb-2">{title}</h4>
                  {itinerary.description && (
                    <p className="text-gray-600 text-sm leading-relaxed mb-4">
                      {itinerary.description}
                    </p>
                  )}
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Your detailed itinerary information will be displayed here once available.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Download Button */}
        <div className="text-center pt-4">
          <button className="bg-gradient-to-r from-blue-600 to-teal-600 text-white px-8 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 mx-auto">
            <Download className="w-5 h-5" />
            Download Itinerary
          </button>
        </div>
      </div>
    </div>
  );
}
