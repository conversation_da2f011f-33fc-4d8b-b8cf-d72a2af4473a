"use server";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@flinkk/database/prisma";
import { getToken } from "@flinkk/shared-auth/token";

// PUT /api/units/[id] - Update a unit
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId, userId } = await getToken({ req });
    const { id } = await params;

    if (!tenantId || !userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const { name, displayName } = body;

    // Validate required fields
    if (!name || typeof name !== "string" || name.trim().length === 0) {
      return NextResponse.json(
        { error: "Unit name is required" },
        { status: 400 }
      );
    }

    if (
      !displayName ||
      typeof displayName !== "string" ||
      displayName.trim().length === 0
    ) {
      return NextResponse.json(
        { error: "Display name is required" },
        { status: 400 }
      );
    }

    // Check if unit exists and belongs to tenant
    const existingUnit = await prisma.unit.findFirst({
      where: {
        id: id,
        tenantId: tenantId,
        deleted: false,
      },
    });

    if (!existingUnit) {
      return NextResponse.json({ error: "Unit not found" }, { status: 404 });
    }

    // Check if it's a system unit
    if (existingUnit.isSystem) {
      return NextResponse.json(
        { error: "Cannot modify system units" },
        { status: 400 }
      );
    }

    // Check if unit name already exists for this tenant (excluding current unit)
    const duplicateUnit = await prisma.unit.findFirst({
      where: {
        tenantId: tenantId,
        name: name.trim().toLowerCase(),
        deleted: false,
        id: { not: id },
      },
    });

    if (duplicateUnit) {
      return NextResponse.json(
        { error: "A unit with this name already exists" },
        { status: 400 }
      );
    }

    // Update the unit
    const unit = await prisma.unit.update({
      where: { id: id },
      data: {
        name: name.trim().toLowerCase(),
        displayName: displayName.trim(),
      },
      select: {
        id: true,
        name: true,
        displayName: true,
        isSystem: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json(unit);
  } catch (error) {
    console.error("Error updating unit:", error);
    return NextResponse.json(
      { error: "Failed to update unit" },
      { status: 500 }
    );
  }
}

// DELETE /api/units/[id] - Delete a unit
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId, userId } = await getToken({ req });
    const { id } = await params;

    if (!tenantId || !userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if unit exists and belongs to tenant
    const existingUnit = await prisma.unit.findFirst({
      where: {
        id: id,
        tenantId: tenantId,
        deleted: false,
      },
    });

    if (!existingUnit) {
      return NextResponse.json({ error: "Unit not found" }, { status: 404 });
    }

    // Check if it's a system unit
    if (existingUnit.isSystem) {
      return NextResponse.json(
        { error: "Cannot delete system units" },
        { status: 400 }
      );
    }

    // Check if unit is being used by any products
    const productsUsingUnit = await prisma.product.findFirst({
      where: {
        unitId: id,
        tenantId: tenantId,
        deleted: false,
      },
    });

    if (productsUsingUnit) {
      return NextResponse.json(
        { error: "Cannot delete unit that is being used by products" },
        { status: 400 }
      );
    }

    // Soft delete the unit
    await prisma.unit.update({
      where: { id: id },
      data: {
        deleted: true,
        deletedAt: new Date(),
      },
    });

    return NextResponse.json({ message: "Unit deleted successfully" });
  } catch (error) {
    console.error("Error deleting unit:", error);
    return NextResponse.json(
      { error: "Failed to delete unit" },
      { status: 500 }
    );
  }
}
