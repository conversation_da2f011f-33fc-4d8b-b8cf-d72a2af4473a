import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// Mock room configurations data
const mockRoomConfigs = [
  // The Omnia - Zermatt
  {
    id: "room_1",
    hotelId: "hotel_1",
    name: "Deluxe Room with Matterhorn View",
    description: "Spacious room with floor-to-ceiling windows offering breathtaking Matterhorn views. Features contemporary design with luxury amenities.",
    imageUrl: "/images/rooms/omnia-deluxe.jpg",
    capacity: 2,
    size: 35,
    sizeUnit: "sqm",
    price: 800,
    currency: "CHF",
    features: ["Matterhorn View", "King Bed", "Marble Bathroom", "Minibar", "Safe", "WiFi"],
    bedConfiguration: "1 King Bed",
    bathrooms: 1,
    balcony: true,
    smokingAllowed: false,
  },
  {
    id: "room_2",
    hotelId: "hotel_1",
    name: "Junior Suite",
    description: "Elegant suite with separate living area and stunning mountain views. Perfect for couples seeking extra space and luxury.",
    imageUrl: "/images/rooms/omnia-suite.jpg",
    capacity: 2,
    size: 55,
    sizeUnit: "sqm",
    price: 1200,
    currency: "CHF",
    features: ["Mountain View", "Separate Living Area", "King Bed", "Marble Bathroom", "Minibar", "Nespresso Machine"],
    bedConfiguration: "1 King Bed",
    bathrooms: 1,
    balcony: true,
    smokingAllowed: false,
  },
  // Grand Hotel Zermatterhof
  {
    id: "room_3",
    hotelId: "hotel_2",
    name: "Classic Double Room",
    description: "Traditional Swiss-style room with modern amenities and mountain views. Comfortable and elegant accommodation in the heart of Zermatt.",
    imageUrl: "/images/rooms/zermatterhof-classic.jpg",
    capacity: 2,
    size: 28,
    sizeUnit: "sqm",
    price: 650,
    currency: "CHF",
    features: ["Mountain View", "Double Bed", "Traditional Decor", "Minibar", "Safe", "WiFi"],
    bedConfiguration: "1 Double Bed",
    bathrooms: 1,
    balcony: false,
    smokingAllowed: false,
  },
  // Hotel Mont-Blanc - Chamonix
  {
    id: "room_4",
    hotelId: "hotel_3",
    name: "Superior Room Mont Blanc View",
    description: "Luxurious room with direct views of Mont Blanc. Elegantly furnished with French Alpine style and premium amenities.",
    imageUrl: "/images/rooms/mont-blanc-superior.jpg",
    capacity: 2,
    size: 32,
    sizeUnit: "sqm",
    price: 750,
    currency: "EUR",
    features: ["Mont Blanc View", "King Bed", "French Balcony", "Marble Bathroom", "Minibar", "Turndown Service"],
    bedConfiguration: "1 King Bed",
    bathrooms: 1,
    balcony: true,
    smokingAllowed: false,
  },
  {
    id: "room_5",
    hotelId: "hotel_3",
    name: "Prestige Suite",
    description: "Spacious suite with panoramic mountain views and separate living area. The epitome of luxury in the heart of Chamonix.",
    imageUrl: "/images/rooms/mont-blanc-prestige.jpg",
    capacity: 4,
    size: 65,
    sizeUnit: "sqm",
    price: 1400,
    currency: "EUR",
    features: ["Panoramic Views", "Separate Living Room", "King Bed", "Sofa Bed", "Marble Bathroom", "Butler Service"],
    bedConfiguration: "1 King Bed + 1 Sofa Bed",
    bathrooms: 2,
    balcony: true,
    smokingAllowed: false,
  },
  // Hotel Post - St. Anton
  {
    id: "room_6",
    hotelId: "hotel_5",
    name: "Alpine Double Room",
    description: "Cozy Austrian-style room with traditional Alpine decor and modern amenities. Perfect for ski enthusiasts.",
    imageUrl: "/images/rooms/post-alpine.jpg",
    capacity: 2,
    size: 25,
    sizeUnit: "sqm",
    price: 450,
    currency: "EUR",
    features: ["Mountain View", "Twin Beds", "Alpine Decor", "Ski Storage", "WiFi", "Minibar"],
    bedConfiguration: "2 Twin Beds",
    bathrooms: 1,
    balcony: true,
    smokingAllowed: false,
  },
  // Cristallo Resort - Cortina
  {
    id: "room_7",
    hotelId: "hotel_6",
    name: "Deluxe Room Dolomites View",
    description: "Elegant room with stunning Dolomites views and Italian luxury touches. Features marble bathroom and premium amenities.",
    imageUrl: "/images/rooms/cristallo-deluxe.jpg",
    capacity: 2,
    size: 38,
    sizeUnit: "sqm",
    price: 600,
    currency: "EUR",
    features: ["Dolomites View", "King Bed", "Marble Bathroom", "Italian Design", "Minibar", "Concierge Service"],
    bedConfiguration: "1 King Bed",
    bathrooms: 1,
    balcony: true,
    smokingAllowed: false,
  },
  // Fairmont Chateau Whistler
  {
    id: "room_8",
    hotelId: "hotel_7",
    name: "Fairmont Room Mountain View",
    description: "Spacious room with mountain views and Canadian hospitality. Features rustic elegance and modern amenities.",
    imageUrl: "/images/rooms/fairmont-mountain.jpg",
    capacity: 4,
    size: 42,
    sizeUnit: "sqm",
    price: 520,
    currency: "CAD",
    features: ["Mountain View", "2 Queen Beds", "Fireplace", "Minibar", "Coffee Maker", "Ski Storage"],
    bedConfiguration: "2 Queen Beds",
    bathrooms: 1,
    balcony: false,
    smokingAllowed: false,
  },
  // The Vale Niseko
  {
    id: "room_9",
    hotelId: "hotel_8",
    name: "Deluxe Room with Tatami Area",
    description: "Unique blend of Western comfort and Japanese tradition. Features tatami seating area and mountain views.",
    imageUrl: "/images/rooms/vale-deluxe.jpg",
    capacity: 2,
    size: 45,
    sizeUnit: "sqm",
    price: 900,
    currency: "JPY",
    features: ["Mountain View", "Tatami Area", "King Bed", "Japanese Bathroom", "Minibar", "Yukata Robes"],
    bedConfiguration: "1 King Bed",
    bathrooms: 1,
    balcony: true,
    smokingAllowed: false,
  },
];

// GET /api/inventory/room-configs
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const hotelId = searchParams.get("hotelId");
    const search = searchParams.get("search");
    const minCapacity = searchParams.get("minCapacity");
    const maxPrice = searchParams.get("maxPrice");
    const features = searchParams.get("features")?.split(",") || [];
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    let filteredRooms = [...mockRoomConfigs];

    // Filter by hotel if provided
    if (hotelId) {
      filteredRooms = filteredRooms.filter(
        (room) => room.hotelId === hotelId
      );
    }

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredRooms = filteredRooms.filter(
        (room) =>
          room.name.toLowerCase().includes(searchLower) ||
          room.description.toLowerCase().includes(searchLower)
      );
    }

    // Apply capacity filter
    if (minCapacity) {
      filteredRooms = filteredRooms.filter(
        (room) => room.capacity >= parseInt(minCapacity)
      );
    }

    // Apply price filter
    if (maxPrice) {
      filteredRooms = filteredRooms.filter(
        (room) => room.price <= parseInt(maxPrice)
      );
    }

    // Apply features filter
    if (features.length > 0) {
      filteredRooms = filteredRooms.filter((room) =>
        features.every((feature) =>
          room.features.some((roomFeature) =>
            roomFeature.toLowerCase().includes(feature.toLowerCase())
          )
        )
      );
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedRooms = filteredRooms.slice(startIndex, endIndex);

    const response = {
      roomConfigs: paginatedRooms,
      pagination: {
        page,
        limit,
        total: filteredRooms.length,
        totalPages: Math.ceil(filteredRooms.length / limit),
        hasNext: endIndex < filteredRooms.length,
        hasPrev: page > 1,
      },
      filters: {
        features: [...new Set(mockRoomConfigs.flatMap(r => r.features))],
        capacityRange: {
          min: Math.min(...mockRoomConfigs.map(r => r.capacity)),
          max: Math.max(...mockRoomConfigs.map(r => r.capacity)),
        },
        priceRange: {
          min: Math.min(...mockRoomConfigs.map(r => r.price)),
          max: Math.max(...mockRoomConfigs.map(r => r.price)),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching room configurations:", error);
    return NextResponse.json(
      { error: "Failed to fetch room configurations" },
      { status: 500 }
    );
  }
}
