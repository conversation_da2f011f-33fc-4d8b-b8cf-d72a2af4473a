import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// Mock hotels data
const mockHotels = [
  // Zermatt Hotels
  {
    id: "hotel_1",
    destinationId: "dest_1",
    name: "The Omnia",
    description: "A contemporary luxury hotel perched on a rocky outcrop with stunning Matterhorn views. Features modern design, world-class spa, and gourmet dining.",
    imageUrl: "/images/hotels/omnia-zermatt.jpg",
    rating: 5,
    starRating: 5,
    basePrice: 800,
    currency: "CHF",
    amenities: ["Spa", "Fine Dining", "Ski Concierge", "Fitness Center", "Bar", "Room Service"],
    location: "Zermatt Village Center",
    roomCount: 30,
    checkInTime: "15:00",
    checkOutTime: "12:00",
  },
  {
    id: "hotel_2",
    destinationId: "dest_1",
    name: "Grand Hotel Zermatterhof",
    description: "Historic luxury hotel in the heart of Zermatt with traditional Swiss charm and modern amenities. Family-owned for over 150 years.",
    imageUrl: "/images/hotels/zermatterhof.jpg",
    rating: 4.8,
    starRating: 5,
    basePrice: 650,
    currency: "CHF",
    amenities: ["Spa", "Multiple Restaurants", "Ski Room", "Concierge", "Business Center"],
    location: "Zermatt Main Street",
    roomCount: 63,
    checkInTime: "15:00",
    checkOutTime: "11:00",
  },
  // Chamonix Hotels
  {
    id: "hotel_3",
    destinationId: "dest_2",
    name: "Hotel Mont-Blanc",
    description: "Legendary palace hotel in the center of Chamonix with breathtaking Mont Blanc views. A perfect blend of tradition and luxury.",
    imageUrl: "/images/hotels/mont-blanc-chamonix.jpg",
    rating: 4.9,
    starRating: 5,
    basePrice: 750,
    currency: "EUR",
    amenities: ["Spa", "Michelin Restaurant", "Ski Valet", "Heated Pool", "Fitness Center"],
    location: "Chamonix Center",
    roomCount: 40,
    checkInTime: "15:00",
    checkOutTime: "12:00",
  },
  {
    id: "hotel_4",
    destinationId: "dest_2",
    name: "Hameau Albert 1er",
    description: "Charming family-run hotel with Michelin-starred dining and authentic Alpine atmosphere. Located in peaceful Les Praz.",
    imageUrl: "/images/hotels/hameau-albert.jpg",
    rating: 4.7,
    starRating: 4,
    basePrice: 550,
    currency: "EUR",
    amenities: ["Michelin Dining", "Spa", "Garden", "Ski Shuttle", "Wine Cellar"],
    location: "Les Praz de Chamonix",
    roomCount: 37,
    checkInTime: "16:00",
    checkOutTime: "11:00",
  },
  // St. Anton Hotels
  {
    id: "hotel_5",
    destinationId: "dest_3",
    name: "Hotel Post",
    description: "Historic luxury hotel in the heart of St. Anton with direct access to the Arlberg ski area. Famous for its lively après-ski scene.",
    imageUrl: "/images/hotels/post-st-anton.jpg",
    rating: 4.6,
    starRating: 4,
    basePrice: 450,
    currency: "EUR",
    amenities: ["Ski-in/Ski-out", "Multiple Bars", "Spa", "Fitness Center", "Ski School"],
    location: "St. Anton Village Center",
    roomCount: 108,
    checkInTime: "15:00",
    checkOutTime: "11:00",
  },
  // Cortina Hotels
  {
    id: "hotel_6",
    destinationId: "dest_4",
    name: "Cristallo Resort & Spa",
    description: "Iconic luxury resort with panoramic Dolomites views. Features world-class spa, gourmet dining, and elegant Italian style.",
    imageUrl: "/images/hotels/cristallo-cortina.jpg",
    rating: 4.8,
    starRating: 5,
    basePrice: 600,
    currency: "EUR",
    amenities: ["Spa", "Fine Dining", "Ski Shuttle", "Indoor Pool", "Tennis Court"],
    location: "Cortina d'Ampezzo",
    roomCount: 74,
    checkInTime: "15:00",
    checkOutTime: "12:00",
  },
  // Whistler Hotels
  {
    id: "hotel_7",
    destinationId: "dest_5",
    name: "Fairmont Chateau Whistler",
    description: "Luxury castle-style resort at the base of Blackcomb Mountain. Offers ski-in/ski-out access and world-class amenities.",
    imageUrl: "/images/hotels/fairmont-whistler.jpg",
    rating: 4.5,
    starRating: 4,
    basePrice: 520,
    currency: "CAD",
    amenities: ["Ski-in/Ski-out", "Golf Course", "Spa", "Multiple Restaurants", "Heated Pool"],
    location: "Upper Village",
    roomCount: 550,
    checkInTime: "16:00",
    checkOutTime: "11:00",
  },
  // Niseko Hotels
  {
    id: "hotel_8",
    destinationId: "dest_6",
    name: "The Vale Niseko",
    description: "Ultra-luxury ski-in/ski-out resort with authentic Japanese hospitality and world-class powder skiing access.",
    imageUrl: "/images/hotels/vale-niseko.jpg",
    rating: 4.9,
    starRating: 5,
    basePrice: 900,
    currency: "JPY",
    amenities: ["Ski-in/Ski-out", "Onsen", "Japanese Restaurant", "Spa", "Concierge"],
    location: "Niseko Village",
    roomCount: 84,
    checkInTime: "15:00",
    checkOutTime: "11:00",
  },
];

// GET /api/inventory/hotels
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const destinationId = searchParams.get("destinationId");
    const search = searchParams.get("search");
    const minRating = searchParams.get("minRating");
    const maxPrice = searchParams.get("maxPrice");
    const amenities = searchParams.get("amenities")?.split(",") || [];
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    let filteredHotels = [...mockHotels];

    // Filter by destination if provided
    if (destinationId) {
      filteredHotels = filteredHotels.filter(
        (hotel) => hotel.destinationId === destinationId
      );
    }

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredHotels = filteredHotels.filter(
        (hotel) =>
          hotel.name.toLowerCase().includes(searchLower) ||
          hotel.description.toLowerCase().includes(searchLower) ||
          hotel.location.toLowerCase().includes(searchLower)
      );
    }

    // Apply rating filter
    if (minRating) {
      filteredHotels = filteredHotels.filter(
        (hotel) => hotel.rating >= parseFloat(minRating)
      );
    }

    // Apply price filter
    if (maxPrice) {
      filteredHotels = filteredHotels.filter(
        (hotel) => hotel.basePrice <= parseInt(maxPrice)
      );
    }

    // Apply amenities filter
    if (amenities.length > 0) {
      filteredHotels = filteredHotels.filter((hotel) =>
        amenities.every((amenity) =>
          hotel.amenities.some((hotelAmenity) =>
            hotelAmenity.toLowerCase().includes(amenity.toLowerCase())
          )
        )
      );
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedHotels = filteredHotels.slice(startIndex, endIndex);

    const response = {
      hotels: paginatedHotels,
      pagination: {
        page,
        limit,
        total: filteredHotels.length,
        totalPages: Math.ceil(filteredHotels.length / limit),
        hasNext: endIndex < filteredHotels.length,
        hasPrev: page > 1,
      },
      filters: {
        amenities: [...new Set(mockHotels.flatMap(h => h.amenities))],
        priceRange: {
          min: Math.min(...mockHotels.map(h => h.basePrice)),
          max: Math.max(...mockHotels.map(h => h.basePrice)),
        },
        ratingRange: {
          min: Math.min(...mockHotels.map(h => h.rating)),
          max: Math.max(...mockHotels.map(h => h.rating)),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching hotels:", error);
    return NextResponse.json(
      { error: "Failed to fetch hotels" },
      { status: 500 }
    );
  }
}
