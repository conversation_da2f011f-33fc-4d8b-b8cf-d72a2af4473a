import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// Mock destinations data
const mockDestinations = [
  {
    id: "dest_1",
    name: "Swiss Alps - Zermatt",
    description: "Experience the majestic beauty of the Swiss Alps with breathtaking views of the Matterhorn. Perfect for luxury skiing and mountain adventures.",
    imageUrl: "/images/destinations/zermatt.jpg",
    basePrice: 2500,
    currency: "CHF",
    country: "Switzerland",
    region: "Valais",
    activities: ["Skiing", "Mountaineering", "Hiking", "Spa"],
    bestSeason: "December - April",
    rating: 4.9,
  },
  {
    id: "dest_2",
    name: "French Alps - Chamonix",
    description: "The birthplace of extreme skiing and home to the legendary Vallée Blanche. Chamonix offers world-class skiing and alpine adventures.",
    imageUrl: "/images/destinations/chamonix.jpg",
    basePrice: 2200,
    currency: "EUR",
    country: "France",
    region: "Haute-Savoie",
    activities: ["Skiing", "Ice Climbing", "Paragliding", "Mountaineering"],
    bestSeason: "December - April",
    rating: 4.8,
  },
  {
    id: "dest_3",
    name: "Austrian Alps - St. Anton",
    description: "Renowned for its challenging slopes and vibrant après-ski scene. St. Anton am Arlberg is a paradise for advanced skiers and party enthusiasts.",
    imageUrl: "/images/destinations/st-anton.jpg",
    basePrice: 1800,
    currency: "EUR",
    country: "Austria",
    region: "Tyrol",
    activities: ["Skiing", "Snowboarding", "Après-ski", "Winter Hiking"],
    bestSeason: "December - April",
    rating: 4.7,
  },
  {
    id: "dest_4",
    name: "Italian Dolomites - Cortina",
    description: "Stunning mountain scenery and excellent skiing in the heart of the Dolomites. Cortina d'Ampezzo combines Italian elegance with alpine adventure.",
    imageUrl: "/images/destinations/cortina.jpg",
    basePrice: 2000,
    currency: "EUR",
    country: "Italy",
    region: "Veneto",
    activities: ["Skiing", "Via Ferrata", "Hiking", "Gourmet Dining"],
    bestSeason: "December - April",
    rating: 4.6,
  },
  {
    id: "dest_5",
    name: "Canadian Rockies - Whistler",
    description: "North America's premier ski destination with two massive mountains and a vibrant village. Perfect for families and adventure seekers.",
    imageUrl: "/images/destinations/whistler.jpg",
    basePrice: 1900,
    currency: "CAD",
    country: "Canada",
    region: "British Columbia",
    activities: ["Skiing", "Snowboarding", "Zip-lining", "Mountain Biking"],
    bestSeason: "November - April",
    rating: 4.5,
  },
  {
    id: "dest_6",
    name: "Japanese Alps - Niseko",
    description: "Famous for its powder snow and unique Japanese culture. Niseko offers an authentic Japanese skiing experience with world-class hospitality.",
    imageUrl: "/images/destinations/niseko.jpg",
    basePrice: 2300,
    currency: "JPY",
    country: "Japan",
    region: "Hokkaido",
    activities: ["Skiing", "Snowboarding", "Hot Springs", "Cultural Tours"],
    bestSeason: "December - March",
    rating: 4.8,
  },
];

// GET /api/inventory/destinations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search");
    const country = searchParams.get("country");
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    let filteredDestinations = [...mockDestinations];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredDestinations = filteredDestinations.filter(
        (dest) =>
          dest.name.toLowerCase().includes(searchLower) ||
          dest.description.toLowerCase().includes(searchLower) ||
          dest.country.toLowerCase().includes(searchLower) ||
          dest.region.toLowerCase().includes(searchLower)
      );
    }

    // Apply country filter
    if (country) {
      filteredDestinations = filteredDestinations.filter(
        (dest) => dest.country.toLowerCase() === country.toLowerCase()
      );
    }

    // Apply price filters
    if (minPrice) {
      filteredDestinations = filteredDestinations.filter(
        (dest) => dest.basePrice >= parseInt(minPrice)
      );
    }

    if (maxPrice) {
      filteredDestinations = filteredDestinations.filter(
        (dest) => dest.basePrice <= parseInt(maxPrice)
      );
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedDestinations = filteredDestinations.slice(startIndex, endIndex);

    const response = {
      destinations: paginatedDestinations,
      pagination: {
        page,
        limit,
        total: filteredDestinations.length,
        totalPages: Math.ceil(filteredDestinations.length / limit),
        hasNext: endIndex < filteredDestinations.length,
        hasPrev: page > 1,
      },
      filters: {
        countries: [...new Set(mockDestinations.map(d => d.country))],
        priceRange: {
          min: Math.min(...mockDestinations.map(d => d.basePrice)),
          max: Math.max(...mockDestinations.map(d => d.basePrice)),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching destinations:", error);
    return NextResponse.json(
      { error: "Failed to fetch destinations" },
      { status: 500 }
    );
  }
}
