import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// Mock add-ons data
const mockAddons = [
  // Ski Services
  {
    id: "addon_1",
    name: "Private Ski Instructor",
    description: "Professional ski instruction tailored to your skill level. Full-day private lessons with certified instructors.",
    imageUrl: "/images/addons/ski-instructor.jpg",
    price: 450,
    currency: "CHF",
    category: "Ski Services",
    duration: "Full Day",
    maxParticipants: 4,
    difficulty: "All Levels",
    includes: ["Professional Instructor", "Lift Tickets", "Equipment Check"],
    bookingRequired: true,
    cancellationPolicy: "24 hours",
  },
  {
    id: "addon_2",
    name: "Ski Equipment Rental - Premium",
    description: "Top-of-the-line ski equipment including skis, boots, poles, and helmet. Latest models from leading brands.",
    imageUrl: "/images/addons/ski-equipment.jpg",
    price: 85,
    currency: "CHF",
    category: "Equipment",
    duration: "Per Day",
    maxParticipants: 1,
    difficulty: "All Levels",
    includes: ["Skis", "Boots", "Poles", "Helmet", "Daily Maintenance"],
    bookingRequired: false,
    cancellationPolicy: "Same day",
  },
  {
    id: "addon_3",
    name: "Helicopter Skiing",
    description: "Ultimate off-piste experience with helicopter access to pristine powder slopes. Includes guide and safety equipment.",
    imageUrl: "/images/addons/heli-skiing.jpg",
    price: 1200,
    currency: "CHF",
    category: "Adventure",
    duration: "Half Day",
    maxParticipants: 6,
    difficulty: "Advanced",
    includes: ["Helicopter Transport", "Professional Guide", "Safety Equipment", "Lunch"],
    bookingRequired: true,
    cancellationPolicy: "48 hours",
  },
  // Spa & Wellness
  {
    id: "addon_4",
    name: "Alpine Spa Treatment",
    description: "Relaxing spa treatment using local Alpine herbs and minerals. Perfect after a day on the slopes.",
    imageUrl: "/images/addons/spa-treatment.jpg",
    price: 180,
    currency: "CHF",
    category: "Spa & Wellness",
    duration: "90 minutes",
    maxParticipants: 1,
    difficulty: "All Levels",
    includes: ["Full Body Massage", "Alpine Herbs", "Relaxation Area Access"],
    bookingRequired: true,
    cancellationPolicy: "4 hours",
  },
  {
    id: "addon_5",
    name: "Hot Stone Massage",
    description: "Therapeutic hot stone massage to relieve muscle tension and promote deep relaxation.",
    imageUrl: "/images/addons/hot-stone.jpg",
    price: 220,
    currency: "CHF",
    category: "Spa & Wellness",
    duration: "60 minutes",
    maxParticipants: 1,
    difficulty: "All Levels",
    includes: ["Hot Stone Treatment", "Aromatherapy", "Relaxation Tea"],
    bookingRequired: true,
    cancellationPolicy: "4 hours",
  },
  // Dining
  {
    id: "addon_6",
    name: "Mountain Hut Dining Experience",
    description: "Authentic Alpine dining experience at a traditional mountain hut. Includes transportation and multi-course meal.",
    imageUrl: "/images/addons/mountain-dining.jpg",
    price: 150,
    currency: "CHF",
    category: "Dining",
    duration: "4 hours",
    maxParticipants: 8,
    difficulty: "All Levels",
    includes: ["Transportation", "3-Course Meal", "Local Wine", "Traditional Music"],
    bookingRequired: true,
    cancellationPolicy: "24 hours",
  },
  {
    id: "addon_7",
    name: "Private Chef Experience",
    description: "Personal chef service in your accommodation. Customized menu featuring local specialties and premium ingredients.",
    imageUrl: "/images/addons/private-chef.jpg",
    price: 350,
    currency: "CHF",
    category: "Dining",
    duration: "3 hours",
    maxParticipants: 8,
    difficulty: "All Levels",
    includes: ["Personal Chef", "Premium Ingredients", "Wine Pairing", "Service Staff"],
    bookingRequired: true,
    cancellationPolicy: "48 hours",
  },
  // Transportation
  {
    id: "addon_8",
    name: "Airport Transfer - Luxury",
    description: "Premium airport transfer service with luxury vehicle and professional driver. Door-to-door service.",
    imageUrl: "/images/addons/luxury-transfer.jpg",
    price: 280,
    currency: "CHF",
    category: "Transportation",
    duration: "One Way",
    maxParticipants: 4,
    difficulty: "All Levels",
    includes: ["Luxury Vehicle", "Professional Driver", "Meet & Greet", "Luggage Assistance"],
    bookingRequired: true,
    cancellationPolicy: "2 hours",
  },
  {
    id: "addon_9",
    name: "Helicopter Transfer",
    description: "Scenic helicopter transfer with breathtaking mountain views. The ultimate arrival experience.",
    imageUrl: "/images/addons/heli-transfer.jpg",
    price: 800,
    currency: "CHF",
    category: "Transportation",
    duration: "30 minutes",
    maxParticipants: 4,
    difficulty: "All Levels",
    includes: ["Helicopter Flight", "Scenic Route", "Professional Pilot", "Champagne Service"],
    bookingRequired: true,
    cancellationPolicy: "24 hours",
  },
  // Activities
  {
    id: "addon_10",
    name: "Snowshoe Hiking Tour",
    description: "Guided snowshoe hiking through pristine winter landscapes. Suitable for all fitness levels.",
    imageUrl: "/images/addons/snowshoe.jpg",
    price: 95,
    currency: "CHF",
    category: "Activities",
    duration: "3 hours",
    maxParticipants: 10,
    difficulty: "Beginner",
    includes: ["Snowshoes", "Poles", "Professional Guide", "Hot Drinks"],
    bookingRequired: true,
    cancellationPolicy: "4 hours",
  },
  {
    id: "addon_11",
    name: "Ice Climbing Experience",
    description: "Thrilling ice climbing adventure with professional instruction and safety equipment. For adventurous spirits.",
    imageUrl: "/images/addons/ice-climbing.jpg",
    price: 320,
    currency: "CHF",
    category: "Activities",
    duration: "Half Day",
    maxParticipants: 4,
    difficulty: "Intermediate",
    includes: ["Professional Instruction", "Safety Equipment", "Ice Tools", "Certification"],
    bookingRequired: true,
    cancellationPolicy: "24 hours",
  },
  // Concierge Services
  {
    id: "addon_12",
    name: "Personal Concierge Service",
    description: "Dedicated concierge service for restaurant reservations, activity bookings, and personal assistance.",
    imageUrl: "/images/addons/concierge.jpg",
    price: 200,
    currency: "CHF",
    category: "Concierge",
    duration: "Per Day",
    maxParticipants: 8,
    difficulty: "All Levels",
    includes: ["Personal Assistant", "Reservation Service", "Activity Planning", "24/7 Support"],
    bookingRequired: false,
    cancellationPolicy: "Same day",
  },
];

// GET /api/inventory/addons
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search");
    const category = searchParams.get("category");
    const maxPrice = searchParams.get("maxPrice");
    const difficulty = searchParams.get("difficulty");
    const maxParticipants = searchParams.get("maxParticipants");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    let filteredAddons = [...mockAddons];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredAddons = filteredAddons.filter(
        (addon) =>
          addon.name.toLowerCase().includes(searchLower) ||
          addon.description.toLowerCase().includes(searchLower) ||
          addon.category.toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (category) {
      filteredAddons = filteredAddons.filter(
        (addon) => addon.category.toLowerCase() === category.toLowerCase()
      );
    }

    // Apply price filter
    if (maxPrice) {
      filteredAddons = filteredAddons.filter(
        (addon) => addon.price <= parseInt(maxPrice)
      );
    }

    // Apply difficulty filter
    if (difficulty) {
      filteredAddons = filteredAddons.filter(
        (addon) => addon.difficulty.toLowerCase() === difficulty.toLowerCase()
      );
    }

    // Apply participants filter
    if (maxParticipants) {
      filteredAddons = filteredAddons.filter(
        (addon) => addon.maxParticipants >= parseInt(maxParticipants)
      );
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedAddons = filteredAddons.slice(startIndex, endIndex);

    const response = {
      addons: paginatedAddons,
      pagination: {
        page,
        limit,
        total: filteredAddons.length,
        totalPages: Math.ceil(filteredAddons.length / limit),
        hasNext: endIndex < filteredAddons.length,
        hasPrev: page > 1,
      },
      filters: {
        categories: [...new Set(mockAddons.map(a => a.category))],
        difficulties: [...new Set(mockAddons.map(a => a.difficulty))],
        priceRange: {
          min: Math.min(...mockAddons.map(a => a.price)),
          max: Math.max(...mockAddons.map(a => a.price)),
        },
        participantsRange: {
          min: Math.min(...mockAddons.map(a => a.maxParticipants)),
          max: Math.max(...mockAddons.map(a => a.maxParticipants)),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching add-ons:", error);
    return NextResponse.json(
      { error: "Failed to fetch add-ons" },
      { status: 500 }
    );
  }
}
