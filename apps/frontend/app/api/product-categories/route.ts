"use server";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@flinkk/database/prisma";
import { getToken } from "@flinkk/shared-auth/token";

// GET /api/product-categories - Get product categories
export async function GET(req: NextRequest) {
  try {
    const { tenantId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const categories = await prisma.productCategory.findMany({
      where: {
        tenantId: tenantId,
        deleted: false,
      },
      select: {
        id: true,
        name: true,
        description: true,
        isSystem: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: [
        { isSystem: "desc" }, // System categories first
        { name: "asc" }, // Then alphabetical
      ],
    });

    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error fetching product categories:", error);
    return NextResponse.json(
      { error: "Failed to fetch product categories" },
      { status: 500 }
    );
  }
}

// POST /api/product-categories - Create a new product category
export async function POST(req: NextRequest) {
  try {
    const { tenantId, userId } = await getToken({ req });

    if (!tenantId || !userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const { name, description } = body;

    // Validate required fields
    if (!name || typeof name !== "string" || name.trim().length === 0) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      );
    }

    // Check if category name already exists for this tenant
    const existingCategory = await prisma.productCategory.findFirst({
      where: {
        tenantId: tenantId,
        name: name.trim(),
        deleted: false,
      },
    });

    if (existingCategory) {
      return NextResponse.json(
        { error: "A category with this name already exists" },
        { status: 400 }
      );
    }

    // Create the category
    const category = await prisma.productCategory.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        tenantId: tenantId,
        isSystem: false, // Custom categories are not system categories
      },
      select: {
        id: true,
        name: true,
        description: true,
        isSystem: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error("Error creating product category:", error);
    return NextResponse.json(
      { error: "Failed to create product category" },
      { status: 500 }
    );
  }
}
