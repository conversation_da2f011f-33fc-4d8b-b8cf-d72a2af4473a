"use server";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@flinkk/database/prisma";
import { getToken } from "@flinkk/shared-auth/token";

// PUT /api/product-categories/[id] - Update a product category
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId, userId } = await getToken({ req });
    const { id } = await params;

    if (!tenantId || !userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const { name, description } = body;

    // Validate required fields
    if (!name || typeof name !== "string" || name.trim().length === 0) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      );
    }

    // Check if category exists and belongs to tenant
    const existingCategory = await prisma.productCategory.findFirst({
      where: {
        id: id,
        tenantId: tenantId,
        deleted: false,
      },
    });

    if (!existingCategory) {
      return NextResponse.json({ error: "Category not found" }, { status: 404 });
    }

    // Check if it's a system category
    if (existingCategory.isSystem) {
      return NextResponse.json(
        { error: "Cannot modify system categories" },
        { status: 400 }
      );
    }

    // Check if category name already exists for this tenant (excluding current category)
    const duplicateCategory = await prisma.productCategory.findFirst({
      where: {
        tenantId: tenantId,
        name: name.trim(),
        deleted: false,
        id: { not: id },
      },
    });

    if (duplicateCategory) {
      return NextResponse.json(
        { error: "A category with this name already exists" },
        { status: 400 }
      );
    }

    // Update the category
    const category = await prisma.productCategory.update({
      where: { id: id },
      data: {
        name: name.trim(),
        description: description?.trim() || null,
      },
      select: {
        id: true,
        name: true,
        description: true,
        isSystem: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json(category);
  } catch (error) {
    console.error("Error updating product category:", error);
    return NextResponse.json(
      { error: "Failed to update product category" },
      { status: 500 }
    );
  }
}

// DELETE /api/product-categories/[id] - Delete a product category
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId, userId } = await getToken({ req });
    const { id } = await params;

    if (!tenantId || !userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if category exists and belongs to tenant
    const existingCategory = await prisma.productCategory.findFirst({
      where: {
        id: id,
        tenantId: tenantId,
        deleted: false,
      },
    });

    if (!existingCategory) {
      return NextResponse.json({ error: "Category not found" }, { status: 404 });
    }

    // Check if it's a system category
    if (existingCategory.isSystem) {
      return NextResponse.json(
        { error: "Cannot delete system categories" },
        { status: 400 }
      );
    }

    // Check if category is being used by any products
    const productsUsingCategory = await prisma.product.findFirst({
      where: {
        categoryId: id,
        tenantId: tenantId,
        deleted: false,
      },
    });

    if (productsUsingCategory) {
      return NextResponse.json(
        { error: "Cannot delete category that is being used by products" },
        { status: 400 }
      );
    }

    // Soft delete the category
    await prisma.productCategory.update({
      where: { id: id },
      data: {
        deleted: true,
        deletedAt: new Date(),
      },
    });

    return NextResponse.json({ message: "Category deleted successfully" });
  } catch (error) {
    console.error("Error deleting product category:", error);
    return NextResponse.json(
      { error: "Failed to delete product category" },
      { status: 500 }
    );
  }
}
