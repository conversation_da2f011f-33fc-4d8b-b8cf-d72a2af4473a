import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  _request: NextRequest,
  { params }: { params: any }
) {
  try {
    const { id } = await params;

    // Make the API call from the server side to avoid CORS issues
    const response = await fetch(
      `http://dev.perfectpiste.ops.flinkk.io/store/concierge-management/itineraries/${id}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-publishable-api-key': 'pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b',
        },
      }
    );

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch itinerary: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching itinerary:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
