import { prisma } from "@flinkk/database/prisma";
import { analyzeEmailContentAdvanced } from "./content-analyzer";
import {
  processEmailAttachments,
  getAttachmentMetadata,
} from "./attachment-processor";
import { EmailSecurityValidator } from "./security-validator";
import type {
  EmailProcessingRequest,
  EmailProcessingResult,
  EmailContentAnalysis,
} from "@flinkk/database/types/support-email";

/**
 * Main function to process incoming emails and create support tickets
 */
export async function processIncomingEmail(
  emailData: EmailProcessingRequest,
): Promise<EmailProcessingResult> {
  let processingLogId: string;

  try {
    // Step 1: Process attachment metadata
    const attachmentMetadata = emailData.attachments
      ? getAttachmentMetadata(emailData.attachments)
      : { count: 0, totalSize: 0, types: [], filenames: [] };

    // Step 2: Create initial processing log
    const processingLog = await prisma.emailProcessingLog.create({
      data: {
        messageId: emailData.messageId,
        fromEmail: emailData.from.email,
        fromName: emailData.from.name,
        toEmail: emailData.to,
        subject: emailData.subject,
        bodyText: emailData.bodyText,
        bodyHtml: emailData.bodyHtml,
        attachmentCount: attachmentMetadata.count,
        attachmentData:
          attachmentMetadata.count > 0
            ? JSON.stringify(attachmentMetadata)
            : null,
        processingStatus: "PROCESSING",
        rawEmailData: JSON.stringify(emailData),
        processingAttempts: 1,
        lastProcessedAt: new Date(),
      },
    });

    processingLogId = processingLog.id;

    // Step 3: Get email configuration (try Inbox first, then fallback to SupportEmailConfig)
    const emailConfig = await getEmailConfiguration(
      emailData?.tenantId ?? "",
      emailData.to,
    );

    if (!emailConfig) {
      await updateProcessingLog(processingLogId, {
        processingStatus: "FAILED",
        processingError: "No active email configuration found",
        tenantId: emailData?.tenantId ?? "",
      });
      return {
        success: false,
        error: "Email not configured",
        processingLogId,
      };
    }

    // Step 4: Check if auto-ticket creation is enabled
    if (!emailConfig.autoCreateTickets) {
      await updateProcessingLog(processingLogId, {
        processingStatus: "SUCCESS",
        processingError: "Auto-ticket creation disabled",
        tenantId: emailData?.tenantId ?? "",
      });
      return {
        success: true,
        processingLogId,
      };
    }

    // Step 5: Comprehensive security validation
    const securityValidation =
      await EmailSecurityValidator.validateEmailSecurity(
        emailData,
        emailConfig,
      );

    if (!securityValidation.isValid) {
      await updateProcessingLog(processingLogId, {
        processingStatus: securityValidation.spamResult.isSpam
          ? "SPAM_DETECTED"
          : "FAILED",
        processingError: `Security validation failed: ${securityValidation.securityIssues.join(", ")}`,
        spamScore: securityValidation.riskScore,
        isSpam: securityValidation.spamResult.isSpam,
        tenantId: emailData?.tenantId ?? "",
      });
      return {
        success: false,
        error: `Security validation failed: ${securityValidation.securityIssues.join(", ")}`,
        processingLogId,
      };
    }

    // Step 6: Check for duplicate emails
    const isDuplicate = await checkForDuplicate(emailData.messageId);
    if (isDuplicate) {
      await updateProcessingLog(processingLogId, {
        processingStatus: "DUPLICATE",
        processingError: "Duplicate email message",
        tenantId: emailData?.tenantId ?? "",
      });
      return {
        success: false,
        error: "Duplicate email",
        processingLogId,
      };
    }

    // Step 7: Find or create contact
    const contact = await findOrCreateContact(
      emailData,
      emailData?.tenantId ?? "",
      emailConfig,
    );
    if (!contact) {
      await updateProcessingLog(processingLogId, {
        processingStatus: "FAILED",
        processingError: "Failed to create or find contact",
        tenantId: emailData?.tenantId ?? "",
      });
      return {
        success: false,
        error: "Contact creation failed",
        processingLogId,
      };
    }

    // Step 8: Analyze email content for categorization
    const contentAnalysis = await analyzeEmailContentAdvanced(emailData);

    // Step 9: Create support ticket
    const ticket = await createSupportTicket(
      emailData,
      emailData?.tenantId ?? "",
      contact?.id,
      contact?.userId,
      emailConfig,
      contentAnalysis,
    );

    // Step 10: Process email attachments
    let attachmentIds: string[] = [];
    if (emailData.attachments && emailData.attachments.length > 0) {
      attachmentIds = await processEmailAttachments(
        emailData.attachments,
        ticket.id,
        emailData?.tenantId ?? "",
        contact?.userId,
      );
    }

    // Step 11: Create activity log for ticket creation
    await createTicketActivityLog(
      ticket.id,
      emailData,
      emailData?.tenantId ?? "",
      attachmentIds,
    );

    // Step 12: Send auto-response if enabled
    if (emailConfig.autoResponseEnabled) {
      await sendAutoResponse(emailData, ticket, emailConfig);
    }

    // Step 13: Update processing log with success
    await updateProcessingLog(processingLogId, {
      processingStatus: "SUCCESS",
      ticketId: ticket.id,
      contactId: contact.id,
      tenantId: emailData?.tenantId ?? "",
      spamScore: securityValidation.riskScore,
    });

    return {
      success: true,
      ticketId: ticket.id,
      contactId: contact.id,
      processingLogId,
    };
  } catch (error) {
    console.error("Email processing error:", error);

    // Update processing log with error
    if (processingLogId!) {
      await updateProcessingLog(processingLogId, {
        processingStatus: "FAILED",
        processingError:
          error instanceof Error ? error.message : "Unknown error",
      });
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Processing failed",
      processingLogId: processingLogId!,
    };
  }
}

/**
 * Get email configuration from Inbox or SupportEmailConfig
 */
async function getEmailConfiguration(tenantId: string, emailAddress: string) {
  // First, try to find Inbox-based email configuration

  console.log("Fetching email inboxes for tenant:", tenantId);

  const inboxes = await prisma.inbox.findMany({
    where: {
      tenantId,
      type: "EMAIL",
      isActive: true,
      deleted: false,
    },
    include: {
      agents: true,
    },
  });

  // Filter by supportEmail in the settings JSON field
  const inbox = inboxes.find(
    (inboxItem: any) =>
      (inboxItem.settings as any)?.supportEmail === emailAddress,
  );

  if (inbox && inbox.settings) {
    const settings = inbox.settings as any;
    return {
      id: inbox.id,
      type: "INBOX",
      assigneeId: inbox.agents?.[0]?.userId,
      supportEmail: settings.supportEmail,
      autoCreateTickets: true, // Always enabled for inbox-based config
      autoResponseEnabled: settings.autoResponseEnabled || false,
      autoResponseTemplate: settings.autoResponseTemplate || "",
      requireValidContact: settings.requireValidContact || false,
      tenantId,
    };
  }

  return null;
}

/**
 * Update processing log with new data
 */
async function updateProcessingLog(
  logId: string,
  data: Partial<{
    processingStatus: string;
    processingError: string;
    ticketId: string;
    contactId: string;
    tenantId: string;
    spamScore: number;
    isSpam: boolean;
  }>,
) {
  await prisma.emailProcessingLog.update({
    where: { id: logId },
    data: {
      ...data,
      lastProcessedAt: new Date(),
    },
  });
}

/**
 * Check for duplicate emails
 */
async function checkForDuplicate(messageId: string): Promise<boolean> {
  const existing = await prisma.emailProcessingLog.findFirst({
    where: {
      messageId,
      processingStatus: "SUCCESS",
    },
  });

  return !!existing;
}

/**
 * Find or create contact from email
 */
async function findOrCreateContact(
  emailData: EmailProcessingRequest,
  tenantId: string,
  emailConfig: any,
) {
  let user = await prisma.user.findFirst({
    where: {
      email: emailData?.from?.email,
    },
  });
  if (!user) {
    // If user exists, return their contact
    user = await prisma.user.create({
      data: {
        email: emailData?.from?.email,
        name: emailData?.from?.name || emailData?.from?.email,
      },
    });
  }

  // First, try to find existing contact
  let contact = await prisma.contact.findFirst({
    where: {
      email: emailData?.from?.email,
      tenantId,
      deleted: false,
    },
  });

  if (contact) {
    return contact;
  }

  // If requireValidContact is enabled and no contact found, return null
  if (emailConfig.requireValidContact) {
    return null;
  }

  // Create new contact
  const names = parseEmailName(emailData?.from?.name || emailData?.from?.email);

  contact = await prisma.contact.create({
    data: {
      firstName: names.firstName,
      lastName: names.lastName,
      email: emailData?.from?.email,
      phoneNumber: "", // Required field, will be empty for email contacts
      tenantId,
      userId: user?.id,
      source: "Email Support",
      description: `Contact created from email: ${emailData.subject}`,
      createdById: user?.id || undefined,
      updatedById: user?.id || undefined,
    },
  });

  return contact;
}

/**
 * Parse name from email display name
 */
function parseEmailName(displayName: string): {
  firstName: string;
  lastName: string;
} {
  if (!displayName || displayName.includes("@")) {
    // If no display name or it's just an email, use email prefix
    const emailPrefix = displayName.split("@")[0];
    return {
      firstName: emailPrefix,
      lastName: "",
    };
  }

  const parts = displayName.trim().split(" ");
  if (parts.length === 1) {
    return {
      firstName: parts[0],
      lastName: "",
    };
  }

  return {
    firstName: parts[0],
    lastName: parts.slice(1).join(" "),
  };
}

/**
 * Create support ticket from email
 */
async function createSupportTicket(
  emailData: EmailProcessingRequest,
  tenantId: string,
  contactId: string,
  contactUserId: string,
  emailConfig: any,
  contentAnalysis: EmailContentAnalysis,
) {
  // Generate ticket number
  const ticketCount = await prisma.supportTicket.count({
    where: { tenantId },
  }); // Generate ticket number

  const assigneeId = emailConfig.assigneeId;

  const ticketNumber = `TCK-${String(ticketCount + 1).padStart(6, "0")}`;

  // Create the ticket
  const ticket = await prisma.supportTicket.create({
    data: {
      ticketNumber,
      subject: emailData.subject,
      description: emailData.bodyText || emailData.bodyHtml || "",
      status: "OPEN",
      priority: contentAnalysis.priority,
      type: contentAnalysis.category,
      channel: "EMAIL",
      creationSource: "SYSTEM",
      contactId,
      ...(assigneeId ? { assigneeId: assigneeId } : {}),
      tenantId,
      createdById: contactUserId,
      updatedById: contactUserId,
      aiConfidenceScore: contentAnalysis.confidence,
      aiModelVersion: "email-processor-v1",
    },
  });

  if (ticket.type === "FEATURE") {
    try {
      await prisma.ticketApproval.create({
        data: {
          ticketId: ticket.id,
          requesterId: contactUserId,
          tenantId: tenantId,
          comments: "Automatically created from feature request ticket",
        },
      });
    } catch (approvalError) {
      console.error("Failed to create approval request:", approvalError);
      // Don't fail the ticket creation if approval creation fails
    }
  }

  return ticket;
}

/**
 * Create activity log for ticket creation from email
 */
async function createTicketActivityLog(
  ticketId: string,
  emailData: EmailProcessingRequest,
  tenantId: string,
  attachmentIds: string[] = [],
) {
  try {
    const attachmentInfo =
      attachmentIds.length > 0
        ? ` with ${attachmentIds.length} attachment(s)`
        : "";

    await prisma.activityLog.create({
      data: {
        title: "Ticket created from email",
        description: `Ticket automatically created from email: ${emailData.subject}${attachmentInfo}`,
        activity_type: "EMAIL",
        action: "CREATE",
        related_to_type: "TICKET",
        related_to_id: ticketId,
        tenantId,
        visibility: "INTERNAL",
      },
    });
  } catch (error) {
    console.error("Failed to create activity log:", error);
    // Don't throw error as this is not critical for ticket creation
  }
}

/**
 * Send auto-response email to the sender
 */
async function sendAutoResponse(
  emailData: EmailProcessingRequest,
  ticket: any,
  emailConfig: any,
) {
  try {
    // Skip auto-response if no template is configured
    if (!emailConfig.autoResponseTemplate) {
      return;
    }

    // Replace template variables
    const template = emailConfig.autoResponseTemplate
      .replace(/\{\{ticketNumber\}\}/g, ticket.ticketNumber)
      .replace(/\{\{subject\}\}/g, emailData.subject);

    // Import the mail API helper
    const { createMailAPIFromConfig } = await import("@/lib/mail-config-helper");

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(emailConfig.tenantId);

    if (!mailConfig) {
      console.warn("No mail configuration found for tenant:", emailConfig.tenantId);
      return; // Skip auto-response if no mail configuration
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Send auto-response
    await mailAPI.sendEmail({
      from: emailConfig.supportEmail,
      to: emailData.from.email,
      subject: `Re: ${emailData.subject} [Ticket #${ticket.ticketNumber}]`,
      htmlContent: `<p>${template}</p>`,
      textContent: template,
      tenantId: mailTenantId,
      ticketId: ticket.id,
    });

    console.log(`Auto-response sent for ticket ${ticket.ticketNumber}`);
  } catch (error) {
    console.error("Failed to send auto-response:", error);
    // Don't throw error as this is not critical for ticket creation
  }
}
