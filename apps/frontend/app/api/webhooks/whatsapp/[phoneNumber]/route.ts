import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@flinkk/database/prisma";
import { WhatsAppWebhookService } from "lib/services/whatsapp-webhook-service";
import { io } from "socket.io-client";

// GET /api/webhooks/whatsapp/[phoneNumber] - Webhook verification for WhatsApp Cloud API
export async function GET(
  req: NextRequest,
  props: { params: Promise<{ phoneNumber: string }> }
) {
  const params = await props.params;
  try {
    const { searchParams } = new URL(req.url);
    const mode = searchParams.get("hub.mode");
    const token = searchParams.get("hub.verify_token");
    const challenge = searchParams.get("hub.challenge");

    console.log("WhatsApp webhook verification:", {
      mode,
      token,
      challenge,
      phoneNumber: params.phoneNumber,
    });

    // Verify the webhook
    if (mode === "subscribe") {
      // Find the inbox with this phone number
      const inboxs = await prisma.inbox.findMany({
        where: {
          type: "WHATSAPP",
          isActive: true,
          deleted: false,
        },
      });

      const inbox = inboxs.find(
        (inboxItem: any) =>
          (inboxItem.settings as any)?.phoneNumber === `+${params.phoneNumber}`
      );

      if (!inbox) {
        console.error(
          "No WhatsApp inbox found for phone number:",
          params.phoneNumber
        );
        return NextResponse.json({ error: "Inbox not found" }, { status: 404 });
      }

      const settings = inbox.settings as any;
      const expectedToken = settings?.webhookVerifyToken;

      if (!expectedToken) {
        console.error(
          "No webhook verify token configured for inbox:",
          inbox.id
        );
        return NextResponse.json(
          { error: "Webhook verify token not configured" },
          { status: 400 }
        );
      }

      if (token === expectedToken) {
        console.log(
          "Webhook verification successful for phone:",
          params.phoneNumber
        );
        return new NextResponse(challenge, { status: 200 });
      } else {
        console.error(
          "Invalid webhook verify token for phone:",
          params.phoneNumber
        );
        return NextResponse.json(
          { error: "Invalid verify token" },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(
      { error: "Invalid verification request" },
      { status: 400 }
    );
  } catch (error) {
    console.error("WhatsApp webhook verification error:", error);
    return NextResponse.json(
      { error: "Webhook verification failed" },
      { status: 500 }
    );
  }
}

// POST /api/webhooks/whatsapp/[phoneNumber] - Handle incoming WhatsApp messages
export async function POST(
  req: NextRequest,
  props: { params: Promise<{ phoneNumber: string }> }
) {
  const params = await props.params;
  try {
    const body = await req.json();

    console.log("Webhook body:", JSON.stringify(body, null, 2));

    // Validate basic webhook structure
    if (!body.object || !body.entry || !Array.isArray(body.entry)) {
      console.error("Invalid webhook payload structure");
      return NextResponse.json(
        { error: "Invalid webhook payload structure" },
        { status: 400 }
      );
    }

    // Find the inbox with this phone number
    const inboxs = await prisma.inbox.findMany({
      where: {
        type: "WHATSAPP",
        isActive: true,
        deleted: false,
      },
      include: {
        tenant: true,
      },
    });
    const inbox = inboxs.find(
      (inboxItem: any) =>
        (inboxItem.settings as any)?.phoneNumberId ===
        body?.entry?.[0]?.changes?.[0]?.value?.metadata?.phone_number_id
    );
    console.log("Inbox found:", inbox);

    if (!inbox) {
      console.error(
        "No WhatsApp inbox found for phone number:",
        params.phoneNumber
      );
      return NextResponse.json({ error: "Inbox not found" }, { status: 404 });
    }

    // Process the webhook payload
    const result = await WhatsAppWebhookService.processWebhook(inbox, body);

    // Emit real-time events for any new messages
    if (result.success && body.entry) {
      try {
        for (const entry of body.entry) {
          if (entry.changes) {
            for (const change of entry.changes) {
              if (change.value?.messages) {
                for (const message of change.value.messages) {
                  // Find the conversation for this message
                  const conversations = await prisma.conversation.findMany({
                    where: {
                      inboxId: inbox.id,
                      contact: {
                        phoneNumber: message?.from,
                      },
                    },
                    include: {
                      messages: {
                        where: {
                          meta: {
                            path: ["whatsapp_message_id"],
                            equals: message.id,
                          },
                        },
                        include: {
                          sender: {
                            select: {
                              id: true,
                              name: true,
                              email: true,
                            },
                          },
                        },
                        orderBy: {
                          createdAt: "desc",
                        },
                        take: 1,
                      },
                    },
                  });

                  if (
                    conversations.length > 0 &&
                    conversations[0].messages.length > 0
                  ) {
                    const conversation = conversations[0];
                    const latestMessage = conversations[0].messages[0];

                    // Emit real-time event via HTTP to socket server
                    try {
                      const socketServerUrl =
                        process.env.SOCKET_SERVER_URL ||
                        "http://localhost:4000";

                      const eventData = {
                        id: latestMessage.id,
                        content: latestMessage.content,
                        contentType: latestMessage.contentType,
                        isIncoming: latestMessage.isIncoming,
                        status: latestMessage.status,
                        createdAt: latestMessage.createdAt,
                        conversationId: conversation.id,
                        tenantId: conversation.tenantId,
                        meta: latestMessage.meta,
                        sender: latestMessage.sender,
                      };

                      console.log(
                        `Emitting real-time event for conversation ${conversation.id}`
                      );

                      const socketResponse = await fetch(
                        `${socketServerUrl}/broadcast`,
                        {
                          method: "POST",
                          headers: {
                            "Content-Type": "application/json",
                          },
                          body: JSON.stringify({
                            room: `conversation:${conversation.id}`,
                            event: "new_message",
                            data: eventData,
                          }),
                        }
                      );

                      if (socketResponse.ok) {
                        console.log(
                          `Successfully emitted real-time event for conversation ${conversation.id}`
                        );
                      } else {
                        console.error(
                          `Failed to emit real-time event:`,
                          socketResponse.statusText
                        );
                      }
                    } catch (socketError) {
                      console.error(
                        "Error emitting real-time event:",
                        socketError
                      );
                    }
                  }
                }
              }
            }
          }
        }
      } catch (realtimeError) {
        console.error("Error processing real-time events:", realtimeError);
        // Don't fail the webhook if real-time emission fails
      }
    }

    // Record webhook processing in activity log
    // try {
    //   await prisma.activityLog.create({
    //     data: {
    //       action: "WEBHOOK_PROCESSED",
    //       entityType: "INBOX",
    //       entityId: inbox.id,
    //       tenantId: inbox.tenantId,
    //       meta: {
    //         channel: "WHATSAPP",
    //         phoneNumber: params.phoneNumber,
    //         success: result.success,
    //         error: result.error,
    //         messageCount: body.entry.reduce((count: number, entry: any) => {
    //           return (
    //             count +
    //             (entry.changes?.reduce((msgCount: number, change: any) => {
    //               return msgCount + (change.value?.messages?.length || 0);
    //             }, 0) || 0)
    //           );
    //         }, 0),
    //         statusCount: body.entry.reduce((count: number, entry: any) => {
    //           return (
    //             count +
    //             (entry.changes?.reduce((statCount: number, change: any) => {
    //               return statCount + (change.value?.statuses?.length || 0);
    //             }, 0) || 0)
    //           );
    //         }, 0),
    //       },
    //       createdAt: new Date(),
    //       updatedAt: new Date(),
    //     },
    //   });
    // } catch (logError) {
    //   console.error("Failed to record webhook activity log:", logError);
    //   // Continue processing even if logging fails
    // }

    if (result.success) {
      return NextResponse.json(
        {
          status: "success",
          message: "Webhook processed successfully",
        },
        { status: 200 }
      );
    } else {
      console.error("Failed to process WhatsApp webhook:", result.error);
      return NextResponse.json({ error: result.error }, { status: 400 });
    }
  } catch (error) {
    console.error("WhatsApp webhook processing error:", error);
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}
