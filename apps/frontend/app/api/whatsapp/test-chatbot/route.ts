import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { WhatsAppAIChatbotService } from "lib/services/whatsapp-ai-chatbot-service";
import { prisma } from "@flinkk/database/prisma";

// POST /api/whatsapp/test-chatbot - Test AI chatbot response
export async function POST(req: NextRequest) {
  try {
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { inboxId, message, phoneNumber } = await req.json();

    // Validate required fields
    if (!inboxId || !message) {
      return NextResponse.json(
        { error: "Missing required fields: inboxId, message" },
        { status: 400 }
      );
    }

    // Verify inbox belongs to tenant
    const inbox = await prisma.inbox.findFirst({
      where: {
        id: inboxId,
        tenantId,
        type: "WHATSAPP",
      },
      include: {
        tenant: true,
      },
    });

    if (!inbox) {
      return NextResponse.json(
        { error: "WhatsApp inbox not found" },
        { status: 404 }
      );
    }

    // Create or find test contact
    let contact = await prisma.contact.findFirst({
      where: {
        tenantId,
        phone: phoneNumber || "+1234567890",
      },
    });

    if (!contact) {
      contact = await prisma.contact.create({
        data: {
          name: "Test User",
          phone: phoneNumber || "+1234567890",
          tenantId,
          source: "WHATSAPP",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    }

    // Create or find test conversation
    let conversation = await prisma.conversation.findFirst({
      where: {
        tenantId,
        inboxId,
        contactId: contact.id,
        status: "OPEN",
      },
    });

    if (!conversation) {
      conversation = await prisma.conversation.create({
        data: {
          subject: `Test WhatsApp conversation with ${contact.name}`,
          status: "OPEN",
          priority: 0,
          tenantId,
          inboxId,
          contactId: contact.id,
          lastMessageAt: new Date(),
          meta: {
            channel: "WHATSAPP",
            whatsapp_phone: contact.phone,
            test_conversation: true,
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    }

    // Get recent messages for context
    const recentMessages = await prisma.message.findMany({
      where: { conversationId: conversation.id },
      orderBy: { createdAt: "desc" },
      take: 10,
      select: {
        id: true,
        content: true,
        isIncoming: true,
        createdAt: true,
      },
    });

    // Create test message object
    const testMessage = {
      id: `test_${Date.now()}`,
      content: message,
      isIncoming: true,
      createdAt: new Date(),
    };

    // Test AI chatbot response
    const aiResult = await WhatsAppAIChatbotService.processIncomingMessage(
      conversation,
      testMessage,
      {
        conversation,
        contact,
        inbox,
        tenant: inbox.tenant,
        recentMessages,
      }
    );

    return NextResponse.json({
      success: true,
      testMessage: {
        content: message,
        from: contact.phone,
      },
      aiResult: {
        shouldReply: aiResult.shouldReply,
        response: aiResult.response,
        reason: aiResult.reason,
      },
      context: {
        conversationId: conversation.id,
        contactName: contact.name,
        contactPhone: contact.phone,
        inboxName: inbox.name,
        recentMessagesCount: recentMessages.length,
      },
    });
  } catch (error) {
    console.error("WhatsApp chatbot test error:", error);
    return NextResponse.json(
      { error: "Failed to test chatbot" },
      { status: 500 }
    );
  }
}
