import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";

// POST /api/whatsapp/create-conversation - Create a new WhatsApp conversation
export async function POST(req: NextRequest) {
  try {
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { inboxId, contactId, phoneNumber, initialMessage } =
      await req.json();

    // Validate required fields
    if (!inboxId || (!contactId && !phoneNumber)) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: inboxId and either contactId or phoneNumber",
        },
        { status: 400 }
      );
    }

    // Verify inbox belongs to tenant
    const inbox = await prisma.inbox.findFirst({
      where: {
        id: inboxId,
        tenantId,
        type: "WHATSAPP",
      },
    });

    if (!inbox) {
      return NextResponse.json(
        { error: "WhatsApp inbox not found" },
        { status: 404 }
      );
    }

    // Find or create contact
    let contact;
    if (contactId) {
      contact = await prisma.contact.findFirst({
        where: {
          id: contactId,
          tenantId,
        },
      });

      if (!contact) {
        return NextResponse.json(
          { error: "Contact not found" },
          { status: 404 }
        );
      }
    } else {
      // Find contact by phone number
      contact = await prisma.contact.findFirst({
        where: {
          tenantId,
          phoneNumber,
        },
      });

      // Create new contact if not found
      if (!contact) {
        contact = await prisma.contact.create({
          data: {
            name: `WhatsApp User ${phoneNumber}`,
            phoneNumber,
            tenantId,
            source: "WHATSAPP",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });
      }
    }

    // Create new conversation
    const conversation = await prisma.conversation.create({
      data: {
        subject: `WhatsApp conversation with ${contact.firstName}`,
        status: "OPEN",
        priority: 0,
        tenantId,
        inboxId,
        contactId: contact.id,
        lastMessageAt: new Date(),
        meta: {
          channel: "WHATSAPP",
          whatsapp_phone: contact.phoneNumber,
          inbox_name: inbox.name,
          contact_source: "WHATSAPP",
          created_by_user: userId,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // Create initial message if provided
    let message;
    if (initialMessage) {
      message = await prisma.message.create({
        data: {
          content: initialMessage,
          contentType: "text",
          status: "PENDING",
          isIncoming: false,
          conversationId: conversation.id,
          tenantId,
          senderId: userId,
          meta: {
            whatsapp_to: contact.phoneNumber,
            initiated_by_user: userId,
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    }

    return NextResponse.json({
      success: true,
      conversation: {
        id: conversation.id,
        subject: conversation.subject,
        status: conversation.status,
        contactId: contact.id,
        contactName: contact.firstName,
        contactPhone: contact.phoneNumber,
      },
      message: message
        ? {
            id: message.id,
            content: message.content,
            status: message.status,
          }
        : undefined,
    });
  } catch (error) {
    console.error("WhatsApp create conversation error:", error);
    return NextResponse.json(
      { error: "Failed to create WhatsApp conversation" },
      { status: 500 }
    );
  }
}
