import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";

// GET /api/whatsapp/media/[mediaId] - Proxy WhatsApp media through our server
export async function GET(
  req: NextRequest,
  props: { params: Promise<{ mediaId: string }> }
) {
  const params = await props.params;

  try {
    const { tenantId, userId } = await getServerSession();

    const url = new URL(req.url);
    const conversationId = url.searchParams.get("conversationId");

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const mediaId = params.mediaId;
    const conversation = await prisma.conversation.findFirst({
      where: {
        tenantId,
        id: conversationId,
        inbox: {
          type: "WHATSAPP",
        },
      },
      include: {
        tenant: true,
        inbox: true,
      },
    });
    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    const inbox = conversation?.inbox;
    console.log({
      conversation,
      inbox,
    });

    // Get WhatsApp configuration
    const whatsappConfig = inbox.settings as any;
    if (!whatsappConfig?.apiKey || !whatsappConfig?.phoneNumberId) {
      return NextResponse.json(
        { error: "WhatsApp configuration missing" },
        { status: 400 }
      );
    }

    // First, get the media URL from WhatsApp API
    const mediaUrlResponse = await fetch(
      `https://graph.facebook.com/v18.0/${mediaId}`,
      {
        headers: {
          Authorization: `Bearer ${whatsappConfig.apiKey}`,
        },
      }
    );

    if (!mediaUrlResponse.ok) {
      console.error(
        "Failed to get media URL from WhatsApp:",
        mediaUrlResponse.statusText
      );
      return NextResponse.json(
        { error: "Failed to get media URL" },
        { status: 500 }
      );
    }

    const mediaData = await mediaUrlResponse.json();
    const mediaUrl = mediaData.url;

    if (!mediaUrl) {
      return NextResponse.json(
        { error: "No media URL returned" },
        { status: 500 }
      );
    }

    // Download the media from WhatsApp
    const mediaResponse = await fetch(mediaUrl, {
      headers: {
        Authorization: `Bearer ${whatsappConfig.apiKey}`,
      },
    });

    if (!mediaResponse.ok) {
      console.error(
        "Failed to download media from WhatsApp:",
        mediaResponse.statusText
      );
      return NextResponse.json(
        { error: "Failed to download media" },
        { status: 500 }
      );
    }

    // Get the content type from the response
    const contentType =
      mediaResponse.headers.get("content-type") || "application/octet-stream";

    // Stream the media back to the client
    const mediaBuffer = await mediaResponse.arrayBuffer();

    return new NextResponse(mediaBuffer, {
      headers: {
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=3600", // Cache for 1 hour
        "Content-Length": mediaBuffer.byteLength.toString(),
      },
    });
  } catch (error) {
    console.error("WhatsApp media proxy error:", error);
    return NextResponse.json(
      { error: "Failed to proxy WhatsApp media" },
      { status: 500 }
    );
  }
}
