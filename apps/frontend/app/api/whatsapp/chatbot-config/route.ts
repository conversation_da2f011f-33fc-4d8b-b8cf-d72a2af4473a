import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";

// GET /api/whatsapp/chatbot-config - Get chatbot configuration for an inbox
export async function GET(req: NextRequest) {
  try {
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const inboxId = searchParams.get("inboxId");

    if (!inboxId) {
      return NextResponse.json(
        { error: "Missing required parameter: inboxId" },
        { status: 400 }
      );
    }

    // Verify inbox belongs to tenant
    const inbox = await prisma.inbox.findFirst({
      where: {
        id: inboxId,
        tenantId,
        type: "WHATSAPP",
      },
    });

    if (!inbox) {
      return NextResponse.json(
        { error: "WhatsApp inbox not found" },
        { status: 404 }
      );
    }

    const settings = inbox.settings as any;
    const chatbotConfig = settings?.chatbot || {
      enabled: false,
      model: "gpt-4o-mini",
      temperature: 0.7,
      maxTokens: 500,
      systemPrompt: "",
      autoReplyDelay: 2,
      businessHours: {
        enabled: false,
        timezone: "UTC",
        schedule: {
          mon: { start: "09:00", end: "17:00", enabled: true },
          tue: { start: "09:00", end: "17:00", enabled: true },
          wed: { start: "09:00", end: "17:00", enabled: true },
          thu: { start: "09:00", end: "17:00", enabled: true },
          fri: { start: "09:00", end: "17:00", enabled: true },
          sat: { start: "09:00", end: "17:00", enabled: false },
          sun: { start: "09:00", end: "17:00", enabled: false },
        },
      },
      fallbackToHuman: true,
      keywords: {
        humanHandoff: ["human", "agent", "representative", "speak to someone"],
        noReply: ["stop", "unsubscribe", "opt out"],
      },
    };

    return NextResponse.json({ chatbotConfig });
  } catch (error) {
    console.error("Get chatbot config error:", error);
    return NextResponse.json(
      { error: "Failed to get chatbot configuration" },
      { status: 500 }
    );
  }
}

// POST /api/whatsapp/chatbot-config - Update chatbot configuration for an inbox
export async function POST(req: NextRequest) {
  try {
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { inboxId, chatbotConfig } = await req.json();

    if (!inboxId || !chatbotConfig) {
      return NextResponse.json(
        { error: "Missing required fields: inboxId, chatbotConfig" },
        { status: 400 }
      );
    }

    // Verify inbox belongs to tenant
    const inbox = await prisma.inbox.findFirst({
      where: {
        id: inboxId,
        tenantId,
        type: "WHATSAPP",
      },
    });

    if (!inbox) {
      return NextResponse.json(
        { error: "WhatsApp inbox not found" },
        { status: 404 }
      );
    }

    // Validate chatbot configuration
    const validationResult = validateChatbotConfig(chatbotConfig);
    if (!validationResult.isValid) {
      return NextResponse.json(
        { error: validationResult.error },
        { status: 400 }
      );
    }

    // Update inbox settings with chatbot configuration
    const currentSettings = inbox.settings as any;
    const updatedSettings = {
      ...currentSettings,
      chatbot: chatbotConfig,
    };

    const updatedInbox = await prisma.inbox.update({
      where: { id: inboxId },
      data: {
        settings: updatedSettings,
        updatedAt: new Date(),
      },
    });

    // Log configuration change
    await prisma.activityLog.create({
      data: {
        action: "CHATBOT_CONFIG_UPDATED",
        entityType: "INBOX",
        entityId: inboxId,
        tenantId,
        userId,
        meta: {
          enabled: chatbotConfig.enabled,
          model: chatbotConfig.model,
          autoReplyDelay: chatbotConfig.autoReplyDelay,
          businessHoursEnabled: chatbotConfig.businessHours?.enabled,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: "Chatbot configuration updated successfully",
      chatbotConfig,
    });
  } catch (error) {
    console.error("Update chatbot config error:", error);
    return NextResponse.json(
      { error: "Failed to update chatbot configuration" },
      { status: 500 }
    );
  }
}

// Validate chatbot configuration
function validateChatbotConfig(config: any): { isValid: boolean; error?: string } {
  if (typeof config.enabled !== "boolean") {
    return { isValid: false, error: "enabled must be a boolean" };
  }

  if (config.enabled) {
    if (!config.model || typeof config.model !== "string") {
      return { isValid: false, error: "model is required when chatbot is enabled" };
    }

    if (config.temperature !== undefined && (typeof config.temperature !== "number" || config.temperature < 0 || config.temperature > 2)) {
      return { isValid: false, error: "temperature must be a number between 0 and 2" };
    }

    if (config.maxTokens !== undefined && (typeof config.maxTokens !== "number" || config.maxTokens < 1 || config.maxTokens > 4000)) {
      return { isValid: false, error: "maxTokens must be a number between 1 and 4000" };
    }

    if (config.autoReplyDelay !== undefined && (typeof config.autoReplyDelay !== "number" || config.autoReplyDelay < 0)) {
      return { isValid: false, error: "autoReplyDelay must be a non-negative number" };
    }

    if (config.systemPrompt !== undefined && typeof config.systemPrompt !== "string") {
      return { isValid: false, error: "systemPrompt must be a string" };
    }
  }

  return { isValid: true };
}
