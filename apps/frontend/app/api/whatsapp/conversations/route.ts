import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";

// GET /api/whatsapp/conversations - Get WhatsApp conversations
export async function GET(req: NextRequest) {
  try {
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const inboxId = searchParams.get("inboxId");
    const contactId = searchParams.get("contactId");
    const status = searchParams.get("status") || "OPEN";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    // Build query filters
    const filters: any = {
      tenantId,
      meta: {
        path: ["channel"],
        equals: "WHATSAPP",
      },
    };

    if (inboxId) {
      filters.inboxId = inboxId;
    }

    if (contactId) {
      filters.contactId = contactId;
    }

    if (status !== "ALL") {
      filters.status = status;
    }

    // Get total count for pagination
    const totalCount = await prisma.conversation.count({
      where: filters,
    });

    // Get conversations
    const conversations = await prisma.conversation.findMany({
      where: filters,
      include: {
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },
        inbox: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        messages: {
          take: 1,
          orderBy: {
            createdAt: "desc",
          },
          select: {
            id: true,
            content: true,
            contentType: true,
            isIncoming: true,
            status: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        lastMessageAt: "desc",
      },
      skip,
      take: limit,
    });

    return NextResponse.json({
      conversations,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("WhatsApp conversations fetch error:", error);
    return NextResponse.json(
      { error: "Failed to fetch WhatsApp conversations" },
      { status: 500 }
    );
  }
}
