import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";

// GET /api/whatsapp/conversations/[conversationId]/messages - Get messages for a WhatsApp conversation
export async function GET(
  req: NextRequest,
  props: { params: Promise<{ conversationId: string }> }
) {
  const params = await props.params;
  try {
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const skip = (page - 1) * limit;

    // Verify conversation belongs to tenant
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: params.conversationId,
        tenantId,
      },
      include: {
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },
        inbox: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });

    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    // Get total message count
    const totalCount = await prisma.message.count({
      where: {
        conversationId: params.conversationId,
        tenantId,
      },
    });

    // Get messages
    const messages = await prisma.message.findMany({
      where: {
        conversationId: params.conversationId,
        tenantId,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
      skip,
      take: limit,
    });

    // Format messages for WhatsApp display
    const formattedMessages = messages.map((message: any) => ({
      id: message.id,
      content: message.content,
      contentType: message.contentType,
      isIncoming: message.isIncoming,
      status: message.status,
      createdAt: message.createdAt,
      sender: message.sender,
      meta: message.meta,
      whatsappMessageId: message.meta?.whatsapp_message_id,
      whatsappType: message.meta?.whatsapp_type,
      attachments: message.meta?.attachments,
    }));

    return NextResponse.json({
      conversation: {
        id: conversation.id,
        subject: conversation.subject,
        status: conversation.status,
        contact: conversation.contact,
        inbox: conversation.inbox,
        lastMessageAt: conversation.lastMessageAt,
        messageCount: conversation.messageCount,
        meta: conversation.meta,
      },
      messages: formattedMessages,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("WhatsApp conversation messages fetch error:", error);
    return NextResponse.json(
      { error: "Failed to fetch conversation messages" },
      { status: 500 }
    );
  }
}
