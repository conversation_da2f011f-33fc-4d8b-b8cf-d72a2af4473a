import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";
import { WhatsAppInboxService } from "lib/services/whatsapp-inbox-service";
import { conversationEventsService } from "lib/services/conversation-events-service";

// POST /api/conversations/[id]/whatsapp-reply - Send WhatsApp reply from conversation
export async function POST(
  req: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { message, replyToMessageId, attachments } = await req.json();

    // Validate required fields
    if (!message) {
      return NextResponse.json(
        { error: "Message content is required" },
        { status: 400 }
      );
    }

    // Get conversation with inbox and contact details
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: params.id,
        tenantId,
      },
      include: {
        inbox: true,
        contact: true,
      },
    });

    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    // Verify this is a WhatsApp conversation
    if (conversation.inbox.type !== "WHATSAPP") {
      return NextResponse.json(
        { error: "This is not a WhatsApp conversation" },
        { status: 400 }
      );
    }

    // Get contact phone number
    const phoneNumber = conversation.contact.phoneNumber;
    if (!phoneNumber) {
      return NextResponse.json(
        { error: "Contact phone number not found" },
        { status: 400 }
      );
    }

    // Send the WhatsApp message
    const sendResult = await WhatsAppInboxService.sendMessage(
      conversation.inbox.id,
      {
        to: phoneNumber,
        text: message,
        conversationId: conversation.id,
        replyToMessageId,
      }
    );

    if (!sendResult.success) {
      return NextResponse.json(
        { error: sendResult.error || "Failed to send WhatsApp message" },
        { status: 400 }
      );
    }

    // Create message record in database
    const messageRecord = await prisma.message.create({
      data: {
        content: message,
        contentType: "text",
        status: "SENT",
        isIncoming: false,
        conversationId: conversation.id,
        tenantId,
        senderId: userId,
        meta: {
          whatsapp_message_id: sendResult.messageId,
          whatsapp_to: phoneNumber,
          reply_to_message_id: replyToMessageId,
          sent_from_conversation: true,
          attachments: attachments || [],
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Update conversation last message time and increment message count
    await prisma.conversation.update({
      where: { id: conversation.id },
      data: {
        lastMessageAt: new Date(),
        messageCount: {
          increment: 1,
        },
        updatedAt: new Date(),
      },
    });

    // Add to events service for real-time updates
    conversationEventsService.addNewMessageEvent(conversation.id, {
      id: messageRecord.id,
      content: messageRecord.content,
      contentType: messageRecord.contentType,
      isIncoming: messageRecord.isIncoming,
      status: messageRecord.status,
      createdAt: messageRecord.createdAt,
      conversationId: conversation.id,
      tenantId,
      meta: messageRecord.meta,
      sender: messageRecord.sender,
      attachments: attachments || [],
    });

    return NextResponse.json({
      success: true,
      message: {
        id: messageRecord.id,
        content: messageRecord.content,
        contentType: messageRecord.contentType,
        status: messageRecord.status,
        isIncoming: messageRecord.isIncoming,
        createdAt: messageRecord.createdAt,
        sender: messageRecord.sender,
        meta: messageRecord.meta,
        whatsappMessageId: sendResult.messageId,
      },
      conversation: {
        id: conversation.id,
        lastMessageAt: new Date(),
      },
    });
  } catch (error) {
    console.error("WhatsApp reply error:", error);
    return NextResponse.json(
      { error: "Failed to send WhatsApp reply" },
      { status: 500 }
    );
  }
}
