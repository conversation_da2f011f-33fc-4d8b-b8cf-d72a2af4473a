import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";
import { conversationEventsService } from "lib/services/conversation-events-service";

// GET /api/conversations/[id]/events - Server-Sent Events for real-time updates
export async function GET(
  req: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;

  try {
    const { tenantId, userId } = await getServerSession();

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify conversation access
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: params.id,
        tenantId,
      },
    });

    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    // Create a readable stream for Server-Sent Events
    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection event
        const data = `data: ${JSON.stringify({
          type: "connected",
          conversationId: params.id,
          timestamp: new Date().toISOString(),
        })}\n\n`;
        controller.enqueue(new TextEncoder().encode(data));

        // Keep track of last event check time
        let lastEventCheck = new Date();

        // Set up polling for new events
        const pollInterval = setInterval(async () => {
          try {
            // Get events since last check
            const newEvents = conversationEventsService.getEventsSince(
              params.id,
              lastEventCheck
            );

            if (newEvents.length > 0) {
              // Update last check time
              lastEventCheck = new Date();

              // Send each new event
              for (const event of newEvents) {
                const eventData = {
                  type: event.type,
                  data: event.data,
                  timestamp: event.timestamp.toISOString(),
                };

                const sseData = `data: ${JSON.stringify(eventData)}\n\n`;
                controller.enqueue(new TextEncoder().encode(sseData));
              }
            }
          } catch (error) {
            console.error("Error in SSE polling:", error);
          }
        }, 500); // Poll every 500ms for faster response

        // Send heartbeat every 30 seconds to keep connection alive
        const heartbeatInterval = setInterval(() => {
          const heartbeat = `data: ${JSON.stringify({
            type: "heartbeat",
            timestamp: new Date().toISOString(),
          })}\n\n`;
          controller.enqueue(new TextEncoder().encode(heartbeat));
        }, 30000);

        // Clean up on close
        req.signal.addEventListener("abort", () => {
          clearInterval(pollInterval);
          clearInterval(heartbeatInterval);
          controller.close();
        });
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
  } catch (error) {
    console.error("SSE endpoint error:", error);
    return NextResponse.json(
      { error: "Failed to establish SSE connection" },
      { status: 500 }
    );
  }
}
