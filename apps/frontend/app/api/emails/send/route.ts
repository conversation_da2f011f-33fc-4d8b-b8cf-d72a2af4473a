import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { createMailAPIFromConfig } from "lib/mail-config-helper";


export async function POST(req: NextRequest) {
  try {
    // Check authentication
    let tenantId: string;
    let userId: string;

    try {
      const authResult = await getToken({ req: req });
      tenantId = authResult.tenantId;
      userId = authResult.userId;
    } catch (authError) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await req.json();

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return NextResponse.json(
        {
          error: "No mail configuration found. Please configure mail settings first.",
          success: false
        },
        { status: 400 }
      );
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Send email using the configured API
    const result = await mailAPI.sendEmail({
      from: data.from,
      to: data.to,
      subject: data.subject,
      htmlContent: data.htmlContent,
      textContent: data.textContent,
      cc: data.cc,
      tenantId: mailTenantId,
      attachments: data.attachments,
      leadId: data.leadId,
      opportunityId: data.opportunityId,
      ticketId: data.ticketId || data.entityId,
    });

    return NextResponse.json({
      success: true,
      message: "Email sent successfully",
      data: result,
    });
  } catch (error: any) {
    console.error("Error sending email:", error);
    return NextResponse.json(
      {
        error: error.message || "Failed to send email",
        success: false,
      },
      { status: 500 },
    );
  }
}
