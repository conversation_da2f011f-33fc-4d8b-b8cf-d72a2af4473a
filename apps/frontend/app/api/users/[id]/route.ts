import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@flinkk/database/prisma";
import { getToken } from "@flinkk/shared-auth/token";

export const dynamic = "force-dynamic";

// GET /api/users/[id] - Get a specific user by ID
export async function GET(
  req: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { tenantId } = await getToken({ req });

    // Get the user
    const user = await prisma.user.findUnique({
      where: {
        id: params.id,
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        lastLogin: true,
        membership: {
          select: {
            id: true,
            tenantId: true,
            roleId: true,
            tenant: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if the user belongs to the same organization as the requester
    const hasAccess = user.membership.some(
      (membership: any) => membership.tenantId === tenantId
    );

    if (!hasAccess) {
      return NextResponse.json(
        { error: "You don't have access to this user" },
        { status: 403 }
      );
    }

    return NextResponse.json({ user });
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 }
    );
  }
}

// PATCH /api/users/[id] - Update a user
export async function PATCH(
  req: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { tenantId, userId } = await getToken({ req });

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await req.json();
    const { name, email, role, roleId, isActive } = data;

    // Check if the requester has permission to edit members (OWNER or ADMIN)
    const requesterMembership = await prisma.memberShip.findFirst({
      where: {
        userId: userId,
        tenantId: tenantId,
      },
    });

    if (!requesterMembership) {
      return NextResponse.json(
        { error: "You don't have permission to edit members" },
        { status: 403 }
      );
    }

    // Check if the user exists
    const existingUser = await prisma.user.findUnique({
      where: {
        id: params.id,
      },
      include: {
        membership: true,
      },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if the user belongs to the same organization as the requester
    const hasAccess = existingUser.membership.some(
      (membership: any) => membership.tenantId === tenantId
    );

    if (!hasAccess) {
      return NextResponse.json(
        { error: "You don't have access to this user" },
        { status: 403 }
      );
    }

    // If email is being changed, check if it's already in use
    if (email && email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: {
          email,
        },
      });

      if (emailExists) {
        return NextResponse.json(
          { error: "Email is already in use" },
          { status: 400 }
        );
      }
    }

    // Update the user and membership in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the user
      const updatedUser = await tx.user.update({
        where: {
          id: params.id,
        },
        data: {
          name,
          email,
          role,
          isActive,
        },
      });

      // Update the membership if roleId is provided
      if (roleId !== undefined) {
        await tx.memberShip.updateMany({
          where: {
            userId: params.id,
            tenantId: tenantId,
          },
          data: {
            roleId: roleId || null,
          },
        });
      }

      return updatedUser;
    });

    return NextResponse.json({ user: result });
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 }
    );
  }
}
