import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { createMailAPIFromConfig } from "lib/mail-config-helper";


// DELETE /api/domain/[id]/email-identities/[identityId] - Delete an email identity
export async function DELETE(
  req: NextRequest,
  props: { params: Promise<{ id: string; identityId: string }> },
) {
  const params = await props.params;
  try {
    const identityId = params.identityId;

    // Check authentication
    let tenantId: string;
    try {
      const authResult = await getToken({ req: req });
      tenantId = authResult.tenantId;
    } catch (authError) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return NextResponse.json(
        { error: "No mail configuration found. Please configure mail settings first." },
        { status: 400 }
      );
    }

    const { mailAPI } = mailConfig;

    // Delete email identity using the configured API
    const success = await mailAPI.deleteEmailIdentity(identityId);

    return NextResponse.json({ success });
  } catch (error) {
    console.error("Error deleting email identity:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
