import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { createMailAPIFromConfig } from "lib/mail-config-helper";


// POST /api/domain/[id]/email-identities - Create a new email identity
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    let tenantId: string;

    try {
      const authResult = await getToken({ req: req });
      tenantId = authResult.tenantId;
    } catch (authError) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await req.json();

    // Validate required fields
    if (!data.domainId) {
      return NextResponse.json(
        { error: "Domain ID is required" },
        { status: 400 },
      );
    }

    if (!data.name) {
      return NextResponse.json(
        { error: "Email identity name is required" },
        { status: 400 },
      );
    }

    const domainId = data.domainId;

    // Validate domain ID
    if (!domainId || domainId === "undefined") {
      return NextResponse.json(
        { error: "Valid domain ID is required" },
        { status: 400 },
      );
    }

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: "Email identity name is required" },
        { status: 400 },
      );
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return NextResponse.json(
        { error: "No mail configuration found. Please configure mail settings first." },
        { status: 400 }
      );
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Create email identity using the configured API
    const result = await mailAPI.createEmailIdentity({
      name: data.name,
      domainId: domainId,
      tenantId: mailTenantId,
    });

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error creating email identity:", error);
    return NextResponse.json(
      {
        error: "Something went wrong",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
