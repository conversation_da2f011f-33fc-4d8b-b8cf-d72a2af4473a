import { NextRequest, NextResponse } from "next/server";
import { withTenantRBAC } from "@flinkk/shared-rbac";
import { getServerSession } from "@flinkk/shared-auth/server-session";

/**
 * DELETE /api/method/[model]/delete/[id] - Delete a record for any model
 *
 * This is a generic endpoint that can handle deletion for any model type.
 * The model name and record ID are passed as URL parameters.
 *
 * @param request - The request object
 * @param params - URL parameters containing the model name and record ID
 * @returns Success response or error response
 */
export const DELETE = withTenantRBAC(
  async (
    request: NextRequest,
    rbacPrisma: any,
    tenantId: string,
    { params }: { params: Promise<{ model: string; id: string }> },
  ) => {
    try {
      // Get user ID from session
      const { userId } = await getServerSession();
      const { model, id } = await params;

      if (!model) {
        return NextResponse.json(
          { error: "Model parameter is required" },
          { status: 400 },
        );
      }

      if (!id) {
        return NextResponse.json(
          { error: "Record ID is required" },
          { status: 400 },
        );
      }

      // Map model names to actual Prisma model names
      const modelNameMapping: Record<string, string> = {
        customField: "customField",
        lead: "lead",
        opportunity: "opportunity",
        contact: "contact",
        account: "businessAccount",
        businessAccount: "businessAccount",
        task: "task",
        campaign: "campaign",
        quote: "quote",
        supportTicket: "supportTicket",
        ticket: "supportTicket",
        priceBook: "priceBook",
        pricebook: "priceBook",
      };

      const actualModelName = modelNameMapping[model] || model;

      // Validate that the model exists in Prisma schema
      if (!(rbacPrisma as any)[actualModelName]) {
        return NextResponse.json(
          { error: `Model "${model}" does not exist or is invalid.` },
          { status: 400 },
        );
      }

      // Check if the record exists and belongs to the tenant
      // Special handling for models that don't have a tenantId field
      let whereClause: any;
      const modelsWithoutTenantId = [
        "tenant",
        "user",
        "session",
        "authAccount",
        "verificationToken",
        "participant",
      ];

      if (modelsWithoutTenantId.includes(model.toLowerCase())) {
        // For models without tenantId, just check the ID
        whereClause = { id };
      } else {
        // For all other models, check both ID and tenantId
        whereClause = {
          id,
          tenantId,
        };
      }

      // For models with unique ID, use findUnique, otherwise use findFirst
      let existingRecord;
      if (model === "customField") {
        existingRecord = await (rbacPrisma as any)[actualModelName].findUnique({
          where: { id },
        });
        // Verify the record belongs to the correct tenant
        if (existingRecord && existingRecord.tenantId !== tenantId) {
          existingRecord = null;
        }
      } else {
        existingRecord = await (rbacPrisma as any)[actualModelName].findFirst({
          where: whereClause,
        });
      }

      if (!existingRecord) {
        return NextResponse.json(
          {
            error: `Record with ID "${id}" not found or does not belong to the tenant.`,
          },
          { status: 404 },
        );
      }

      // Special handling for User deletion - implement soft delete through membership removal
      if (model.toLowerCase() === "user") {
        // Check if the requester has permission to delete users (OWNER or ADMIN)
        const requesterMembership = await rbacPrisma.memberShip.findFirst({
          where: {
            userId: userId,
            tenantId: tenantId,
          },
        });

        if (!requesterMembership) {
          return NextResponse.json(
            {
              error:
                "You don't have permission to remove users. Only organization owners and admins can remove users.",
            },
            { status: 403 },
          );
        }

        // For user deletion, we implement soft delete by removing the membership
        // This preserves the user record while removing access to the tenant

        // First, check if the user has membership in the current tenant
        const membership = await rbacPrisma.memberShip.findFirst({
          where: {
            userId: id,
            tenantId: tenantId,
          },
        });

        if (!membership) {
          return NextResponse.json(
            {
              error: `User with ID "${id}" is not a member of the current tenant.`,
            },
            { status: 404 },
          );
        }

        // Prevent users from removing themselves
        if (userId === id) {
          return NextResponse.json(
            {
              error: "You cannot remove yourself from the organization.",
            },
            { status: 400 },
          );
        }

        // Prevent non-owners from removing owners
        if (
          membership.customRole?.name === "Owner" &&
          requesterMembership.customRole?.name !== "Owner"
        ) {
          return NextResponse.json(
            {
              error: "Only organization owners can remove other owners.",
            },
            { status: 403 },
          );
        }

        // Remove the membership (soft delete for user access)
        const deletedMembership = await rbacPrisma.memberShip.delete({
          where: {
            id: membership.id,
          },
        });

        return NextResponse.json({
          success: true,
          message: "User access removed successfully.",
          data: {
            userId: id,
            removedMembership: deletedMembership,
          },
        });
      }

      // Check if the model supports soft delete (has deleted and deletedAt fields)
      const modelSchema = (rbacPrisma as any)[actualModelName];
      let hasSoftDeleteFields = false;

      // Skip soft delete check for emailTemplate and customField (don't have deleted/deletedAt fields)
      if (model.toLowerCase() !== "emailtemplate" && model.toLowerCase() !== "customfield") {
        try {
          const softDeleteCheck = await modelSchema.findFirst({
            where: { id },
            select: { deleted: true, deletedAt: true },
          });
          hasSoftDeleteFields =
            softDeleteCheck && softDeleteCheck.deleted !== undefined;
        } catch (error) {
          // If the query fails (e.g., fields don't exist), assume no soft delete
          hasSoftDeleteFields = false;
        }
      }

      let deletedRecord;
      if (hasSoftDeleteFields) {
        // Soft delete - update the deleted flag and deletedAt timestamp
        deletedRecord = await (rbacPrisma as any)[actualModelName].update({
          where: { id },
          data: {
            deleted: true,
            deletedAt: new Date(),
            updatedById: userId,
          },
        });
      } else {
        // Hard delete for models that don't support soft delete
        deletedRecord = await (rbacPrisma as any)[actualModelName].delete({
          where: { id },
        });
      }

      return NextResponse.json({
        success: true,
        message: "Record deleted successfully.",
        data: deletedRecord,
      });
    } catch (error: any) {
      console.error("Error in delete API:", error);
      return NextResponse.json(
        { error: error.message || "Something went wrong" },
        { status: 500 },
      );
    }
  },
);
