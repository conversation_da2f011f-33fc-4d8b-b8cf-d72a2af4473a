import { NextRequest, NextResponse } from "next/server";
import { Prisma, RelatedToType } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { withTenantRBAC } from "@flinkk/shared-rbac";
import { getServerSession } from "@flinkk/shared-auth/server-session";

/**
 * Maps model names to RelatedToType enum values
 */
function getRelatedToType(modelName: string): RelatedToType {
  const normalizedModel = modelName.toLowerCase();

  switch (normalizedModel) {
    case "lead":
      return RelatedToType.LEAD;
    case "opportunity":
      return RelatedToType.OPPORTUNITY;
    case "contact":
      return RelatedToType.CONTACT;
    case "businessaccount":
    case "account":
      return RelatedToType.ACCOUNT;
    case "task":
      return RelatedToType.TASK;
    case "supportticket":
    case "ticket":
      return RelatedToType.TICKET;
    case "quote":
      return RelatedToType.QUOTE;
    case "event":
      // Events default to LEAD as they might be related to leads
      return RelatedToType.LEAD;
    default:
      // Default to LEAD if no mapping found
      return RelatedToType.LEAD;
  }
}

export const dynamic = "force-dynamic";

/**
 * POST /api/method/[model]/create - Create a new record for any model
 *
 * This is a generic endpoint that can handle creation for any model type.
 * The model name is passed as a URL parameter.
 *
 * @param request - The request object containing the data to create
 * @param params - URL parameters containing the model name
 * @returns The created record or error response
 */
export const POST = withTenantRBAC(
  async (
    request: NextRequest,
    rbacPrisma: any,
    tenantId: string,
    { params }: { params: Promise<{ model: string }> },
  ) => {
    let model: string = ""; // Initialize model variable for error handling
    try {
      // Get user ID from session
      const { userId } = await getServerSession();

      const resolvedParams = await params;
      model = resolvedParams.model;

      if (!model) {
        return NextResponse.json(
          { error: "Model parameter is required" },
          { status: 400 },
        );
      }

      const body = await request.json();

      if (!body || Object.keys(body).length === 0) {
        return NextResponse.json(
          { error: "Request body is required" },
          { status: 400 },
        );
      }

      // Extract custom fields from the data
      const { customFields, ...recordData } = body;

      // Convert model parameter to proper case for Prisma (e.g., "leads" -> "lead")
      const normalizedModel = model.endsWith("s") ? model.slice(0, -1) : model;

      // Validate that the model exists in Prisma schema
      if (!(rbacPrisma as any)[normalizedModel]) {
        return NextResponse.json(
          { error: `Model "${model}" does not exist or is invalid.` },
          { status: 400 },
        );
      }

      // Prepare data with tenant ID and audit fields
      let createData = {
        ...recordData,
      };

      // Special handling for CustomField model - filter out fields that don't exist in schema
      if (normalizedModel === "customField") {
        const { minDate, maxDate, showTime, ...validCustomFieldData } =
          createData;
        createData = validCustomFieldData;
      }

      // Handle tenant relationship for models that require it
      if (
        [
          "lead",
          "opportunity",
          "contact",
          "task",
          "supportTicket",
          "campaign",
          "quote",
        ].includes(normalizedModel)
      ) {
        createData.tenant = {
          connect: { id: tenantId },
        };
      } else {
        // For models that only need tenantId as a field (including businessAccount, event, etc.)
        createData.tenantId = tenantId;
      }

      // Add audit fields based on model type
      if (normalizedModel === "note") {
        // Note model uses authorId instead of createdById/updatedById
        if (!createData.authorId) {
          createData.authorId = userId;
        }
      } else if (normalizedModel === "activityLog") {
        // ActivityLog model uses userId instead of createdById/updatedById
        if (!createData.userId) {
          createData.userId = userId;
        }
      } else if (
        [
          "session",
          "verificationToken",
          "inboxAgent",
          "participant",
          "conversationLabel",
          "message",
          "supportTicket", // Handle supportTicket separately
        ].includes(normalizedModel)
      ) {
        // These models don't have standard audit fields, skip adding them
      } else if (normalizedModel === "emailTemplate") {
        // EmailTemplate does not have createdById/updatedById
        // Do nothing
      } else {
        // Most other models use createdById/updatedById
        createData.createdById = userId;
        createData.updatedById = userId;
      }

      // Handle special requirements for Quote model
      if (normalizedModel === "quote") {
        // Set the current user as the owner if not specified
        if (!createData.ownerId) {
          createData.ownerId = userId;
        }
      }

      // Special handling for Lead model - ensure relationships are set properly
      if (normalizedModel === "lead") {
        // Remove any existing userId from form data to avoid conflicts
        if (createData.userId) {
          delete createData.userId;
        }
        // Remove any existing user relationship from form data
        if (createData.user) {
          delete createData.user;
        }
        // Use user relationship instead of direct userId field
        createData.user = {
          connect: { id: userId },
        };

        // Handle campaignId - remove if null to avoid Prisma validation errors
        if (
          createData.campaignId === null ||
          createData.campaignId === undefined
        ) {
          delete createData.campaignId;
        }

        // Handle priority field - set default if empty string or invalid value
        if (!createData.priority || createData.priority === "") {
          createData.priority = "MEDIUM"; // Default priority
        } else {
          // Validate priority is a valid enum value
          const validPriorities = ["LOW", "MEDIUM", "HIGH", "URGENT"];
          if (!validPriorities.includes(createData.priority)) {
            createData.priority = "MEDIUM"; // Default to MEDIUM if invalid
          }
        }

        // Handle audit fields - use relationships instead of direct IDs
        if (createData.createdById) {
          delete createData.createdById;
          createData.createdBy = {
            connect: { id: userId },
          };
        }
        if (createData.updatedById) {
          delete createData.updatedById;
          createData.updatedBy = {
            connect: { id: userId },
          };
        }
      }

      // Special handling for Task model - ensure relationships are set properly
      if (normalizedModel === "task") {
        // Handle userId - convert to user relationship if provided
        if (createData.userId) {
          createData.user = {
            connect: { id: createData.userId },
          };
          delete createData.userId;
        }
        // Remove any existing user relationship from form data to avoid conflicts
        if (createData.user && typeof createData.user === "string") {
          const userIdValue = createData.user;
          createData.user = {
            connect: { id: userIdValue },
          };
        }

        // Handle audit fields - use relationships instead of direct IDs
        if (createData.createdById) {
          delete createData.createdById;
          createData.createdBy = {
            connect: { id: userId },
          };
        }
        if (createData.updatedById) {
          delete createData.updatedById;
          createData.updatedBy = {
            connect: { id: userId },
          };
        }
      }

      // Special handling for Opportunity model - map field names and set required fields
      if (normalizedModel === "opportunity") {
        // Map 'name' field to 'dealName' as required by schema
        // Both 'name' and 'dealName' are required fields in the schema
        if (createData.name) {
          createData.dealName = createData.name;
          // Keep the original name field as it's also required
        } else {
          // If no name provided, create a default one
          createData.name = createData.dealName || "New Opportunity";
          createData.dealName = createData.name;
        }

        // Map 'value' to ensure it's properly handled
        if (createData.value !== undefined) {
          createData.value = parseFloat(createData.value) || 0;
        }

        // Helper function to convert date string to ISO DateTime
        const convertToISODateTime = (
          dateString: string | Date | null | undefined,
        ): Date | null => {
          if (!dateString || dateString === "") return null;

          try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return null;

            // If the date string is in YYYY-MM-DD format (from DatePicker),
            // set time to start of day to avoid timezone issues
            if (
              typeof dateString === "string" &&
              /^\d{4}-\d{2}-\d{2}$/.test(dateString)
            ) {
              const [year, month, day] = dateString.split("-").map(Number);
              return new Date(year, month - 1, day, 0, 0, 0, 0);
            }

            return date;
          } catch (error) {
            console.error("Error converting date:", error);
            return null;
          }
        };

        // Map 'closeDate' to 'expectedCloseDate' with proper date conversion
        if (createData.closeDate) {
          createData.expectedCloseDate = convertToISODateTime(
            createData.closeDate,
          );
          delete createData.closeDate;
        }

        // Convert expectedCloseDate if it exists
        if (createData.expectedCloseDate !== undefined) {
          createData.expectedCloseDate = convertToISODateTime(
            createData.expectedCloseDate,
          );
        }

        // Convert actualCloseDate if it exists
        if (createData.actualCloseDate !== undefined) {
          createData.actualCloseDate = convertToISODateTime(
            createData.actualCloseDate,
          );
        }

        // Convert other date fields if they exist
        if (createData.lastContactedAt !== undefined) {
          createData.lastContactedAt = convertToISODateTime(
            createData.lastContactedAt,
          );
        }

        if (createData.stageEnteredAt !== undefined) {
          createData.stageEnteredAt = convertToISODateTime(
            createData.stageEnteredAt,
          );
        }

        // Handle dealOwner and userId fields for opportunity assignment
        if (createData.userId) {
          createData.dealOwner = createData.userId;
          // Convert userId to user relationship for Prisma
          createData.user = {
            connect: { id: createData.userId },
          };
          delete createData.userId; // Remove direct userId field
        } else {
          createData.dealOwner = userId;
          // Convert userId to user relationship for Prisma
          createData.user = {
            connect: { id: userId },
          };
        }

        // Handle required relatedToId field
        if (!createData.relatedToId) {
          // If converting from a lead, use the lead ID
          if (createData.convertedFromLeadId) {
            createData.relatedToId = createData.convertedFromLeadId;
          } else if (createData.leadId) {
            createData.relatedToId = createData.leadId;
            createData.convertedFromLeadId = createData.leadId;
          } else {
            // Default to the user ID if no related entity is specified
            createData.relatedToId = userId;
          }
        }

        // Set default relatedToType if not provided
        if (!createData.relatedToType) {
          if (createData.convertedFromLeadId || createData.leadId) {
            createData.relatedToType = "LEAD";
          } else {
            createData.relatedToType = "LEAD"; // Default value
          }
        }

        // Handle relationships - convert IDs to connect relationships
        if (createData.accountId && createData.accountId !== "none") {
          createData.account = {
            connect: { id: createData.accountId },
          };
          delete createData.accountId;
        } else if (
          createData.accountId === "none" ||
          createData.accountId === null ||
          createData.accountId === undefined
        ) {
          delete createData.accountId;
        }

        if (createData.contactId && createData.contactId !== "none") {
          createData.contact = {
            connect: { id: createData.contactId },
          };
          delete createData.contactId;
        } else if (
          createData.contactId === "none" ||
          createData.contactId === null ||
          createData.contactId === undefined
        ) {
          delete createData.contactId;
        }

        // Handle convertedFromLeadId - convert to convertedFromLead relationship if provided
        if (
          createData.convertedFromLeadId &&
          createData.convertedFromLeadId !== "none"
        ) {
          createData.convertedFromLead = {
            connect: { id: createData.convertedFromLeadId },
          };
          delete createData.convertedFromLeadId;
        } else if (
          createData.convertedFromLeadId === "none" ||
          createData.convertedFromLeadId === null ||
          createData.convertedFromLeadId === undefined
        ) {
          delete createData.convertedFromLeadId;
        }

        // Handle campaignId - convert to campaign relationship if provided
        if (createData.campaignId && createData.campaignId !== "none") {
          createData.campaign = {
            connect: { id: createData.campaignId },
          };
          delete createData.campaignId;
        } else if (
          createData.campaignId === "none" ||
          createData.campaignId === null ||
          createData.campaignId === undefined
        ) {
          delete createData.campaignId;
        }

        // Note: userId field is converted to user relationship for Prisma compatibility
        // The user relationship is explicitly set using connect syntax

        // Handle audit fields - use relationships instead of direct IDs (same as Lead model)
        if (createData.createdById) {
          delete createData.createdById;
          createData.createdBy = {
            connect: { id: userId },
          };
        }
        if (createData.updatedById) {
          delete createData.updatedById;
          createData.updatedBy = {
            connect: { id: userId },
          };
        }

        // Handle ObjectID fields that should be null if empty or invalid
        const objectIdFields = [
          "bookingId",
          "fulfillmentId",
          "campaignId",
          "convertedFromLeadId",
          "contactId",
          "accountId",
          "relatedToId",
          "dealOwner",
          "createdById",
          "updatedById",
          "userId",
          "tenantId",
          "pipelineStageId",
        ];

        objectIdFields.forEach((field) => {
          if (
            createData[field] === "" ||
            createData[field] === null ||
            createData[field] === undefined
          ) {
            delete createData[field];
          } else if (
            createData[field] &&
            typeof createData[field] === "string"
          ) {
            // Validate ObjectID format
            const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(createData[field]);
            if (!isValidObjectId) {
              delete createData[field];
            }
          }
        });
      }

      // Special handling for Contact model - use relationships for audit fields and account
      if (normalizedModel === "contact") {
        // Handle userId - convert to user relationship if provided
        if (createData.userId) {
          createData.user = {
            connect: { id: createData.userId },
          };
          delete createData.userId;
        }

        // Handle account relationship - convert accountId to account relationship if provided
        if (createData.accountId && createData.accountId !== "none") {
          createData.account = {
            connect: { id: createData.accountId },
          };
          delete createData.accountId;
        } else if (
          createData.accountId === "none" ||
          createData.accountId === null ||
          createData.accountId === undefined
        ) {
          delete createData.accountId;
        }

        // Handle campaign relationship - convert campaignId to campaign relationship if provided
        if (createData.campaignId && createData.campaignId !== "none") {
          createData.campaign = {
            connect: { id: createData.campaignId },
          };
          delete createData.campaignId;
        } else if (
          createData.campaignId === "none" ||
          createData.campaignId === null ||
          createData.campaignId === undefined
        ) {
          delete createData.campaignId;
        }

        // Handle lead relationship - convert leadId to lead relationship if provided
        if (createData.leadId && createData.leadId !== "none") {
          createData.lead = {
            connect: { id: createData.leadId },
          };
          delete createData.leadId;
        } else if (
          createData.leadId === "none" ||
          createData.leadId === null ||
          createData.leadId === undefined
        ) {
          delete createData.leadId;
        }

        // Handle audit fields - use relationships instead of direct IDs
        if (createData.createdById) {
          delete createData.createdById;
          createData.createdBy = {
            connect: { id: userId },
          };
        }
        if (createData.updatedById) {
          delete createData.updatedById;
          createData.updatedBy = {
            connect: { id: userId },
          };
        }
      }

      // Handle null foreign key fields for all models
      if (
        createData.campaignId === null ||
        createData.campaignId === undefined ||
        createData.campaignId === "none"
      ) {
        delete createData.campaignId;
      }
      if (
        createData.contactId === null ||
        createData.contactId === undefined ||
        createData.contactId === "none"
      ) {
        delete createData.contactId;
      }
      if (
        createData.accountId === null ||
        createData.accountId === undefined ||
        createData.accountId === "none"
      ) {
        delete createData.accountId;
      }
      if (
        createData.leadId === null ||
        createData.leadId === undefined ||
        createData.leadId === "none"
      ) {
        delete createData.leadId;
      }
      // Handle userId field - delete for all models as we use relationships
      if (
        createData.userId === null ||
        createData.userId === undefined ||
        createData.userId === "none"
      ) {
        delete createData.userId;
      }

      // Final cleanup for Lead model - ensure no direct ID fields remain
      if (normalizedModel === "lead") {
        // Remove any remaining direct ID fields that should be relationships
        delete createData.userId;
        delete createData.campaignId;
        delete createData.createdById;
        delete createData.updatedById;
      }

      // Final cleanup for Contact model - ensure no direct ID fields remain
      if (normalizedModel === "contact") {
        // Remove any remaining direct ID fields that should be relationships
        delete createData.createdById;
        delete createData.updatedById;
      }

      // Final cleanup for Task model - ensure no direct ID fields remain
      if (normalizedModel === "task") {
        // Remove any remaining direct ID fields that should be relationships
        delete createData.userId;
        delete createData.createdById;
        delete createData.updatedById;
      }

      // Final cleanup for Opportunity model - ensure no conflicting fields remain
      if (normalizedModel === "opportunity") {
        // Remove any remaining fields that might conflict
        // Note: Keep 'name' field as it's required alongside 'dealName'
        // Note: userId is converted to user relationship (handled above)
        // Note: Keep 'dealOwner' field as it's a direct field (NOT a relationship)
        delete createData.closeDate; // Should be expectedCloseDate
        delete createData.tenantId; // Should be tenant relationship (already added above)
        delete createData.createdById; // Should be createdBy relationship
        delete createData.updatedById; // Should be updatedBy relationship
        delete createData.accountId; // Should be account relationship
        delete createData.contactId; // Should be contact relationship
        delete createData.convertedFromLeadId; // Should be convertedFromLead relationship
        delete createData.campaignId; // Should be campaign relationship
        delete createData.userId; // Should be user relationship (handled above)
      }

      // Special handling for Quote model - generate quote number and handle line items
      if (normalizedModel === "quote") {
        // Auto-generate quoteNumber if not provided
        if (!createData.quoteNumber) {
          // Use timestamp-based approach to avoid duplicates
          const timestamp = Date.now();
          const randomSuffix = Math.floor(Math.random() * 1000)
            .toString()
            .padStart(3, "0");
          createData.quoteNumber = `QUO-${timestamp}-${randomSuffix}`;

          // Check if this quote number already exists and regenerate if needed
          let attempts = 0;
          while (attempts < 5) {
            const existingQuote = await rbacPrisma.quote.findFirst({
              where: {
                quoteNumber: createData.quoteNumber,
                tenantId,
              },
            });

            if (!existingQuote) {
              break; // Unique quote number found
            }

            // Regenerate if duplicate found
            const newTimestamp = Date.now();
            const newRandomSuffix = Math.floor(Math.random() * 1000)
              .toString()
              .padStart(3, "0");
            createData.quoteNumber = `QUO-${newTimestamp}-${newRandomSuffix}`;
            attempts++;
          }
        }

        // Set owner to current user if not provided
        if (!createData.ownerId) {
          createData.ownerId = userId;
        }

        // Handle owner relationship - convert ownerId to owner relationship
        if (createData.ownerId) {
          createData.owner = {
            connect: { id: createData.ownerId },
          };
          delete createData.ownerId;
        }

        // Handle relationships - convert IDs to connect relationships
        if (createData.contactId && createData.contactId !== "none") {
          createData.contact = {
            connect: { id: createData.contactId },
          };
          delete createData.contactId;
        } else {
          delete createData.contactId;
        }

        if (createData.accountId && createData.accountId !== "none") {
          createData.account = {
            connect: { id: createData.accountId },
          };
          delete createData.accountId;
        } else {
          delete createData.accountId;
        }

        if (createData.opportunityId && createData.opportunityId !== "none") {
          createData.opportunity = {
            connect: { id: createData.opportunityId },
          };
          delete createData.opportunityId;
        } else {
          delete createData.opportunityId;
        }

        // Handle audit fields
        createData.createdBy = {
          connect: { id: userId },
        };
        createData.updatedBy = {
          connect: { id: userId },
        };
        delete createData.createdById;
        delete createData.updatedById;

        // Extract line items for separate creation
        const lineItems = createData.lines || [];
        delete createData.lines;

        // Store line items for later creation
        createData._lineItems = lineItems;

        // Quotation fields are now part of the Quote model schema and will be stored
        // No need to remove them - they will be persisted to the database
      }

      // Special handling for SupportTicket model - generate ticket number if not provided
      if (normalizedModel === "supportTicket") {
        // Auto-generate ticketNumber if not provided
        if (!createData.ticketNumber) {
          const ticketCount = await rbacPrisma.supportTicket.count({
            where: { tenantId },
          });
          createData.ticketNumber = `TCK-${String(ticketCount + 1).padStart(6, "0")}`;
        }

        // Handle contact relationship - convert contactId to contact relationship
        if (createData.contactId) {
          createData.contact = {
            connect: { id: createData.contactId },
          };
          delete createData.contactId;
        }

        // Handle company relationship - convert companyId to company relationship if provided
        if (createData.companyId && createData.companyId !== "none") {
          createData.company = {
            connect: { id: createData.companyId },
          };
          delete createData.companyId;
        } else {
          delete createData.companyId;
        }

        // Handle assignee relationship - convert assigneeId to assignee relationship if provided
        if (createData.assigneeId && createData.assigneeId !== "none") {
          createData.assignee = {
            connect: { id: createData.assigneeId },
          };
          delete createData.assigneeId;
        } else {
          delete createData.assigneeId;
        }

        // Handle audit fields - use relationships instead of direct IDs
        if (createData.createdById) {
          delete createData.createdById;
        }
        createData.createdBy = {
          connect: { id: userId },
        };

        if (createData.updatedById) {
          delete createData.updatedById;
        }
        createData.lastUpdatedBy = {
          connect: { id: userId },
        };

        // Remove the standard updatedById field if it exists
        delete createData.updatedById;
      }

      // Special handling for Campaign model - use relationships for audit fields
      if (normalizedModel === "campaign") {
        // Handle audit fields - use relationships instead of direct IDs
        if (createData.createdById) {
          delete createData.createdById;
          createData.createdBy = {
            connect: { id: userId },
          };
        }
        if (createData.updatedById) {
          delete createData.updatedById;
          createData.updatedBy = {
            connect: { id: userId },
          };
        }
      }

      // Final cleanup for SupportTicket model - ensure no conflicting fields remain
      if (normalizedModel === "supportTicket") {
        // Remove any remaining direct ID fields that should be relationships
        delete createData.contactId;
        delete createData.companyId;
        delete createData.assigneeId;
        delete createData.createdById;
        delete createData.updatedById;
        delete createData.updatedById;
      }

      // Final cleanup for Contact model - ensure no conflicting fields remain
      if (normalizedModel === "contact") {
        // Remove any remaining direct ID fields that should be relationships
        delete createData.userId; // Should be user relationship
        delete createData.tenantId; // Should be tenant relationship (already added above)
        delete createData.accountId; // Should be account relationship
        delete createData.campaignId; // Should be campaign relationship
        delete createData.leadId; // Should be lead relationship
        delete createData.createdById; // Should be createdBy relationship
        delete createData.updatedById; // Should be updatedBy relationship
      }

      // Special handling for Event model - uses direct fields and relationships
      if (normalizedModel === "event") {
        // Event model uses direct tenantId field (already set above)
        // Handle userId - can be direct field or relationship
        if (createData.userId) {
          // Keep userId as direct field for Event model
          // Event model supports both userId field and user relationship
        } else {
          // Set current user as default if no userId provided
          createData.userId = userId;
        }

        // Ensure audit fields are set properly
        if (!createData.createdById) {
          createData.createdById = userId;
        }
        if (!createData.updatedById) {
          createData.updatedById = userId;
        }
      }

      // Special handling for BusinessAccount model - uses direct fields, not relationships
      if (normalizedModel === "businessAccount") {
        // BusinessAccount uses direct userId field, not user relationship
        // Ensure we don't have conflicting tenant relationship
        if (createData.tenant) {
          delete createData.tenant;
        }

        // Handle primaryContactId - remove if "none" or null to avoid Prisma validation errors
        if (
          createData.primaryContactId === "none" ||
          createData.primaryContactId === null ||
          createData.primaryContactId === undefined
        ) {
          delete createData.primaryContactId;
        }
      }

      // Special handling for Product model
      if (normalizedModel === "product") {
        // Validate required fields for products
        if (
          !createData.categoryId ||
          typeof createData.categoryId !== "string" ||
          createData.categoryId.trim().length === 0
        ) {
          return NextResponse.json(
            { error: "Category is required" },
            { status: 400 },
          );
        }

        // Validate ObjectID fields
        const isValidObjectId = (id: string) => /^[0-9a-fA-F]{24}$/.test(id);

        // Handle categoryId - validate format
        if (!isValidObjectId(createData.categoryId)) {
          return NextResponse.json(
            { error: "Invalid category ID format" },
            { status: 400 },
          );
        }

        // Handle unitId - validate if provided
        if (createData.unitId) {
          if (
            typeof createData.unitId === "string" &&
            createData.unitId.trim() === ""
          ) {
            createData.unitId = null;
          } else if (!isValidObjectId(createData.unitId)) {
            return NextResponse.json(
              { error: "Invalid unit ID format" },
              { status: 400 },
            );
          }
        }

        // Handle preferredVendor - just trim if it's a string
        if (
          createData.preferredVendor &&
          typeof createData.preferredVendor === "string"
        ) {
          createData.preferredVendor =
            createData.preferredVendor.trim() || null;
        }

        // Handle legacy field mapping for backward compatibility
        if (
          createData.price !== undefined &&
          createData.sellingPrice === undefined
        ) {
          createData.sellingPrice = createData.price;
        }
        if (
          createData.cost !== undefined &&
          createData.costPrice === undefined
        ) {
          createData.costPrice = createData.cost;
        }

        // Set defaults for required fields
        if (!createData.type) {
          createData.type = "GOODS";
        }
        if (!createData.taxPreference) {
          createData.taxPreference = "TAXABLE";
        }
      }

      // Extract line items for quote and package before creating record
      const lineItems = createData._lineItems;
      delete createData._lineItems;

      // Extract package lines if present
      const packageLines = createData.lines;
      if (normalizedModel === "package" && packageLines) {
        delete createData.lines;
      }

      // Handle Package-specific field mapping
      if (normalizedModel === "package") {
        // Map isActive boolean to status enum
        if (createData.isActive !== undefined) {
          createData.status = createData.isActive ? "ACTIVE" : "INACTIVE";
          delete createData.isActive;
        }
      }

      // Create the record with RBAC applied
      const record = await (rbacPrisma as any)[normalizedModel].create({
        data: createData,
      });

      // Special handling for Product model - create default price book for each product
      if (normalizedModel === "product") {
        // Import regular Prisma client for price book operations
        const { prisma } = await import("@flinkk/database/prisma");

        // Get the selling price for the price book entry
        const sellingPrice = createData.sellingPrice || createData.price || 0;
        const costPrice = createData.costPrice || createData.cost || null;

        // Create a default price book for this specific product
        const defaultPriceBook = await prisma.priceBook.create({
          data: {
            name: `${createData.name} - Default Price Book`,
            description: `Default price book for ${createData.name}`,
            currency: "USD",
            isDefault: true,
            status: "ACTIVE",
            tenantId,
            createdById: userId,
            updatedById: userId,
          },
        });

        // Create default price book entry for the new product
        await prisma.priceBookEntry.create({
          data: {
            basePrice: sellingPrice,
            listPrice: sellingPrice,
            costPrice: costPrice,
            unitPrice: sellingPrice,
            isActive: true,
            priceBookId: defaultPriceBook.id,
            productId: record.id,
            tenantId,
            createdById: userId,
            updatedById: userId,
          },
        });
      }

      // Handle quote line items creation after main record is created
      if (normalizedModel === "quote" && lineItems && lineItems.length > 0) {
        for (const lineItem of lineItems) {
          await rbacPrisma.quoteLine.create({
            data: {
              ...lineItem,
              quoteId: record.id,
              tenantId,
            },
          });
        }
      }

      // Handle package line items creation after main record is created
      if (
        normalizedModel === "package" &&
        packageLines &&
        packageLines.length > 0
      ) {
        for (let i = 0; i < packageLines.length; i++) {
          const line = packageLines[i];

          // Calculate line totals
          const lineTotalBeforeDiscount = line.quantity * line.unitPrice;
          let discount = 0;

          if (line.discountType === "PERCENTAGE") {
            discount =
              (lineTotalBeforeDiscount * (line.discountValue || 0)) / 100;
          } else {
            discount = line.discountValue || 0;
          }

          const lineTotal = lineTotalBeforeDiscount - discount;

          await rbacPrisma.packageLine.create({
            data: {
              lineNumber: i + 1,
              productId: line.productId || null,
              description: line.description,
              quantity: line.quantity,
              unitId: line.unitId || null,
              unitPrice: line.unitPrice,
              listPrice: line.listPrice || null,
              discount,
              discountType: line.discountType || "FLAT",
              discountValue: line.discountValue || 0,
              lineTotal,
              lineTotalBeforeDiscount,
              packageId: record.id,
              tenantId,
            },
          });
        }
      }

      // Special handling for SupportTicket - create approval request for feature requests
      if (normalizedModel === "supportTicket" && record.type === "FEATURE") {
        try {
          await rbacPrisma.ticketApproval.create({
            data: {
              ticketId: record.id,
              requesterId: userId,
              tenantId,
              comments: "Automatically created from feature request ticket",
            },
          });
        } catch (approvalError) {
          console.error("Failed to create approval request:", approvalError);
          // Don't fail the ticket creation if approval creation fails
        }
      }

      // Handle custom fields if present
      if (customFields && customFields.length > 0) {
        for (const field of customFields) {
          if (field.id && field.value !== undefined) {
            await rbacPrisma.customFieldValue.create({
              data: {
                customFieldId: field.id,
                entityId: record.id,
                [`${normalizedModel}Id`]: record.id, // Dynamic field name based on model
                value: String(field.value), // Ensure value is a string
                createdById: userId,
                updatedById: userId,
              },
            });
          }
        }
      }

      // Create activity log
      await rbacPrisma.activityLog.create({
        data: {
          title: `${normalizedModel.charAt(0).toUpperCase() + normalizedModel.slice(1)} Created`,
          description: `${normalizedModel.charAt(0).toUpperCase() + normalizedModel.slice(1)} was created`,
          action: "CREATE",
          related_to_type: getRelatedToType(normalizedModel),
          related_to_id: record.id,
          user_id: userId,
          tenantId,
        },
      });

      // Revalidate the list page
      revalidatePath(`/${model}s`);

      return NextResponse.json(record, { status: 201 });
    } catch (error: any) {
      console.error(`Error creating ${model}:`, error);

      // Handle specific Prisma error codes
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // Unique constraint violation
        if (error.code === "P2002") {
          const target = (error.meta?.target as string[]) || [];
          const field = target.length > 0 ? target[0] : "field";
          return NextResponse.json(
            {
              error: `A record with this ${field} already exists. Please use a different value.`,
            },
            { status: 409 },
          );
        }

        // Foreign key constraint failed
        if (error.code === "P2003") {
          const fieldName = (error.meta?.field_name as string) || "field";
          return NextResponse.json(
            { error: `The referenced ${fieldName} does not exist.` },
            { status: 400 },
          );
        }

        // Required field missing
        if (error.code === "P2011") {
          const fieldName = (error.meta?.target as string) || "field";
          return NextResponse.json(
            { error: `The ${fieldName} field is required.` },
            { status: 400 },
          );
        }

        // Invalid input data
        if (error.code === "P2000") {
          return NextResponse.json(
            { error: "The provided value for the field is too long." },
            { status: 400 },
          );
        }

        // Malformed ObjectID
        if (error.code === "P2023") {
          return NextResponse.json(
            { error: "Invalid ID format. Please check your input values." },
            { status: 400 },
          );
        }
      }

      // Handle validation errors
      if (
        error.name === "ValidationError" ||
        error.message.includes("validation")
      ) {
        return NextResponse.json(
          {
            error:
              error.message || "Validation failed. Please check your input.",
          },
          { status: 400 },
        );
      }

      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 },
      );
    }
  },
);
