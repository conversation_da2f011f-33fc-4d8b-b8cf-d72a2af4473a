import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { createMailAPIFromConfig } from "lib/mail-config-helper";


// POST /api/domain/[id]/verify - Verify a domain
export async function POST(
  _req: NextRequest,
  props: { params: Promise<{ id: string }> },
) {
  const params = await props.params;
  try {
    // Check authentication
    let tenantId: string;
    let userId: string;

    try {
      const authResult = await getToken({ req: _req });
      tenantId = authResult.tenantId;
      userId = authResult.userId;
    } catch (authError) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!userId || !tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const domainId = params.id;

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return NextResponse.json(
        { error: "No mail configuration found. Please configure mail settings first." },
        { status: 400 }
      );
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Start domain verification using the configured API
    const domain = await mailAPI.startDomainVerification({
      id: domainId,
      tenantId: mailTenantId,
    });

    if (!domain) {
      return NextResponse.json({ error: "Domain not found" }, { status: 404 });
    }

    // Add isVerifying state to the response
    const domainWithState = {
      ...domain,
      isVerifying: false,
    };

    return NextResponse.json(domainWithState);
  } catch (error) {
    console.error("Error verifying domain:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
