import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { createMailAPIFromConfig } from "lib/mail-config-helper";


// DELETE /api/method/domain/delete/[id] - Delete a domain
export async function DELETE(
  _req: NextRequest,
  props: { params: Promise<{ id: string }> },
) {
  const params = await props.params;
  try {
    const domainId = params.id;

    // Get authentication from server session
    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return NextResponse.json(
        { error: "No mail configuration found. Please configure mail settings first." },
        { status: 400 }
      );
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Delete domain using the configured API
    const success = await mailAPI.deleteDomain({
      id: domainId,
      tenantId: mailTenantId,
    });
    return NextResponse.json({ success });
  } catch (error) {
    console.error("Error deleting domain:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Something went wrong",
        success: false,
      },
      { status: 500 },
    );
  }
}
