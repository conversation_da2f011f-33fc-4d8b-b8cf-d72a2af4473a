import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { createMailAPIFromConfig } from "lib/mail-config-helper";

// POST /api/domain - Create a new domain
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    let tenantId: string;

    try {
      const authResult = await getToken({ req: req });
      tenantId = authResult.tenantId;
    } catch (authError) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: "Domain name is required" },
        { status: 400 },
      );
    }
    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return NextResponse.json(
        { error: "No mail configuration found. Please configure mail settings first." },
        { status: 400 }
      );
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Create domain using the configured API
    const domain = await mailAPI.createDomain({
      domain: data.name,
      tenantId: mailTenantId,
    });

    return NextResponse.json(domain);
  } catch (error) {
    console.error("Error creating domain:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
