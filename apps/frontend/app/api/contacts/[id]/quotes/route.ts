import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";

export const dynamic = "force-dynamic";

// GET /api/contacts/[id]/quotes - Get quotes associated with a contact
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { tenantId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const contactId = resolvedParams.id;

    // Get quotes associated with this contact
    const quotes = await prisma.quote.findMany({
      where: {
        contactId: contactId,
        tenantId: tenantId,
      },
      select: {
        id: true,
        quoteNumber: true,
        name: true,
        description: true,
        status: true,
        version: true,
        subtotal: true,
        totalDiscount: true,
        totalTax: true,
        grandTotal: true,
        currency: true,
        validUntil: true,
        createdAt: true,
        updatedAt: true,
        opportunity: {
          select: {
            id: true,
            dealName: true,
          },
        },
        account: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      success: true,
      data: quotes,
      count: quotes.length,
    });
  } catch (error) {
    console.error("Error fetching contact quotes:", error);
    return NextResponse.json(
      { error: "Failed to fetch quotes" },
      { status: 500 },
    );
  }
}
