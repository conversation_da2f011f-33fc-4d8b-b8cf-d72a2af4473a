import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";


export const dynamic = "force-dynamic";

// GET /api/contacts/[id]/opportunities - Get opportunities associated with a contact
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { tenantId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const contactId = resolvedParams.id;

    // Get opportunities associated with this contact
    const opportunities = await prisma.opportunity.findMany({
      where: {
        contactId: contactId,
        tenantId: tenantId,
      },
      select: {
        id: true,
        dealName: true,
        description: true,
        stage: true,
        status: true,
        value: true,
        currency: true,
        probability: true,
        expectedCloseDate: true,
        createdAt: true,
        updatedAt: true,
        account: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      success: true,
      data: opportunities,
      count: opportunities.length,
    });
  } catch (error) {
    console.error("Error fetching contact opportunities:", error);
    return NextResponse.json(
      { error: "Failed to fetch opportunities" },
      { status: 500 },
    );
  }
}
