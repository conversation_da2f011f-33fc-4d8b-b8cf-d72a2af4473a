/**
 * Interface for tab configuration
 */
export interface TabConfig {
  id: string;
  label: string;
  path: string;
  description?: string;
  type?: string;
}

/**
 * Interface for entity tab configuration
 */
export interface EntityTabsConfig {
  [entityType: string]: TabConfig[];
}

/**
 * Configuration for tabs in the customize section
 */
export const CUSTOMIZE_TABS: EntityTabsConfig = {
  leads: [
    {
      id: "fields",
      label: "Standard Fields",
      path: "/customize/leads",
    },
    {
      id: "custom-fields",
      label: "Custom Fields",
      path: "/customize/leads/custom-fields",
    },
    {
      id: "status",
      label: "Lead Status",
      path: "/customize/leads/attribute/status",
      type: "lead_status",
      description: "Status options for leads in the sales pipeline",
    },
    {
      id: "source",
      label: "Lead Source",
      path: "/customize/leads/attribute/source",
      type: "lead_source",
      description: "Source options for leads in the sales pipeline",
    },
    {
      id: "stages",
      label: "Lead Stages",
      path: "/customize/leads/attribute/stages",
      type: "lead_stage",
      description: "Stage options for leads in the sales pipeline",
    },
  ],
  tasks: [
    {
      id: "fields",
      label: "Standard Fields",
      path: "/customize/tasks",
    },
    {
      id: "custom-fields",
      label: "Custom Fields",
      path: "/customize/tasks/custom-fields",
    },
    {
      id: "status",
      label: "Task Status",
      path: "/customize/tasks/status",
      type: "task_status",
      description: "Status options for tasks",
    },
    {
      id: "priority",
      label: "Task Priority",
      path: "/customize/tasks/priority",
      type: "task_priority",
      description: "Priority options for tasks",
    },
  ],
  accounts: [
    {
      id: "fields",
      label: "Standard Fields",
      path: "/customize/accounts",
    },
    {
      id: "custom-fields",
      label: "Custom Fields",
      path: "/customize/accounts/custom-fields",
    },
  ],
  opportunities: [
    {
      id: "fields",
      label: "Standard Fields",
      path: "/customize/opportunities",
    },
    {
      id: "custom-fields",
      label: "Custom Fields",
      path: "/customize/opportunities/custom-fields",
    },
    {
      id: "stages",
      label: "Opportunity Stages",
      path: "/customize/opportunities/attribute/stages",
      type: "opportunity_stage",
      description: "Stage options for opportunities in the sales pipeline",
    },
    {
      id: "status",
      label: "Opportunity Status",
      path: "/customize/opportunities/attribute/status",
      type: "OPPORTUNITY_STATUS",
      description:
        "Status options for opportunities (Open, Closed-Won, Closed-Lost)",
    },
  ],
  contacts: [
    {
      id: "fields",
      label: "Standard Fields",
      path: "/customize/contacts",
    },
    {
      id: "custom-fields",
      label: "Custom Fields",
      path: "/customize/contacts/custom-fields",
    },
  ],
  products: [
    {
      id: "fields",
      label: "Standard Fields",
      path: "/customize/products",
    },
    {
      id: "custom-fields",
      label: "Custom Fields",
      path: "/customize/products/custom-fields",
    },
    {
      id: "units",
      label: "Units",
      path: "/customize/products/units",
      description: "Manage units of measure for products",
    },
    {
      id: "categories",
      label: "Categories",
      path: "/customize/products/categories",
      description: "Manage product categories",
    }
  ],
  packages: [
    {
      id: "fields",
      label: "Standard Fields",
      path: "/customize/packages",
    },
    {
      id: "custom-fields",
      label: "Custom Fields",
      path: "/customize/packages/custom-fields",
    }
  ],
  pricebooks: [
    {
      id: "fields",
      label: "Standard Fields",
      path: "/customize/pricebooks",
    },
    {
      id: "custom-fields",
      label: "Custom Fields",
      path: "/customize/pricebooks/custom-fields",
    },

  ],
};

/**
 * Get tabs for a specific entity type
 * @param entityType The entity type
 * @returns Array of tab configurations
 */
export function getTabsForEntity(entityType: string): TabConfig[] {
  return CUSTOMIZE_TABS[entityType.toLowerCase()] || [];
}

/**
 * Get the active tab ID based on the current path
 * @param path The current path
 * @param entityType The entity type
 * @returns The active tab ID
 */
export function getActiveTabId(path: string, entityType: string): string {
  const tabs = getTabsForEntity(entityType);
  const tab = tabs.find((tab) => tab.path === path);
  return tab?.id || "fields";
}

/**
 * Get a specific tab configuration by ID
 * @param entityType The entity type
 * @param tabId The tab ID
 * @returns The tab configuration
 */
export function getTabById(
  entityType: string,
  tabId: string,
): TabConfig | undefined {
  const tabs = getTabsForEntity(entityType);
  return tabs.find((tab) => tab.id === tabId);
}
