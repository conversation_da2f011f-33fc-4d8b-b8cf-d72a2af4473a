"use server";

import { unstable_noStore as noStore } from "next/cache";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { createMailAPIFromConfig } from "@flinkk/shared-utils/mail-config-helper";

export interface EmailIdentity {
  id: string;
  name: string;
  domainId: string;
  tenantId: string;
  status?: "PENDING" | "VERIFIED" | "FAILED";
  createdAt: string;
  updatedAt: string;
}

export interface EmailIdentitiesResponse {
  success: boolean;
  data?: EmailIdentity[];
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
  };
}

// Get email identities for a domain
export async function getEmailIdentities(
  domainId: string,
): Promise<EmailIdentitiesResponse> {
  noStore();

  try {
    // Get authentication from server session
    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return {
        success: false,
        error: "No mail configuration found. Please configure mail settings first.",
        data: [],
      };
    }

    const { mailAPI } = mailConfig;

    // Get email identities using the configured API
    const emailIdentities = await mailAPI.listEmailIdentities({
      domainId,
    });

    const pagination = {
      page: 1,
      limit: 10,
      totalCount: emailIdentities.length,
      totalPages: 1,
    };

    return {
      success: true,
      data: emailIdentities,
      pagination,
    };
  } catch (error) {
    console.error("Error fetching email identities:", error);
    return {
      success: false,
      error: "Failed to fetch email identities",
      data: [],
    };
  }
}
