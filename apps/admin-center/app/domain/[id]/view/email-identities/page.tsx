import { getDomain } from "../actions";
import { getEmailIdentities } from "./actions";
import { EmailIdentifiersClient } from "./page-client";

interface EmailIdentifiersPageProps {
  params: {
    id: any;
  };
}

export default async function EmailIdentifiersPage({
  params,
}: EmailIdentifiersPageProps) {
  const id = await params.id;
  const [domainResult, emailIdentitiesResult] = await Promise.all([
    getDomain(id),
    getEmailIdentities(id),
  ]);

  if (!domainResult.success) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">
            Failed to load domain
          </h2>
          <p className="text-muted-foreground mt-2">
            {domainResult.error || "An error occurred while loading the domain"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <EmailIdentifiersClient
      domain={domainResult.data}
      emailIdentities={emailIdentitiesResult?.data || []}
      emailIdentitiesError={emailIdentitiesResult.error}
    />
  );
}
