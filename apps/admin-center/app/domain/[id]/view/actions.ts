import { unstable_noStore as noStore } from "next/cache";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { createMailAPIFromConfig } from "@flinkk/shared-utils/mail-config-helper";

// Map external API status to our Domain status
function mapDomainStatus(
  externalStatus: string,
): "PENDING" | "VERIFIED" | "FAILED" {
  switch (externalStatus?.toUpperCase()) {
    case "SUCCESS":
    case "VERIFIED":
      return "VERIFIED";
    case "PENDING":
      return "PENDING";
    case "FAILED":
    case "ERROR":
      return "FAILED";
    default:
      return "PENDING";
  }
}

// Transform external API domain to our Domain interface
function transformDomain(externalDomain: any, tenantId: string): any {
  // Extract the actual domain data from the nested structure
  const domainData = externalDomain?.data?.json || externalDomain;

  return {
    id: domainData.id,
    domain: domainData.name || domainData.domain, // Use 'name' field from API as 'domain'
    name: domainData.name, // Keep original name field
    tenantId: tenantId,
    region: domainData.region || "us-east-1",
    status: mapDomainStatus(domainData.status),
    createdAt: domainData.createdAt || new Date().toISOString(),
    updatedAt:
      domainData.updatedAt ||
      domainData.createdAt ||
      new Date().toISOString(),
    dkimStatus: domainData.dkimStatus
      ? mapDomainStatus(domainData.dkimStatus)
      : undefined,
    spfStatus: domainData.spfStatus
      ? mapDomainStatus(domainData.spfStatus)
      : undefined,
    isVerifying: domainData.isVerifying || false,
    publicKey: domainData.publicKey,
    dnsRecords: domainData.dnsRecords,
    dmarcAdded: domainData.dmarcAdded,
    inboundMXAdded: domainData.inboundMXAdded,
    // Include any additional fields from the API response
    ...domainData,
  };
}

// Get a single domain by ID
export async function getDomain(id: string) {
  noStore();

  try {
    // Get authentication from server session
    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return {
        success: false,
        error: "No mail configuration found. Please configure mail settings first."
      };
    }

    const { mailAPI } = mailConfig;

    // Get domain using the configured API
    const rawDomain = await mailAPI.getDomain(id);

    if (!rawDomain) {
      return { success: false, error: "Domain not found" };
    }

    // Transform the domain to match our interface
    const domain = transformDomain(rawDomain, tenantId);

    return {
      success: true,
      data: domain,
    };
  } catch (error) {
    console.error("Error fetching domain:", error);
    return {
      success: false,
      error: "Failed to fetch domain",
    };
  }
}
