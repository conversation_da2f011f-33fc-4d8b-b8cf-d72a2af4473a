"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { But<PERSON> } from "@flinkk/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { Globe, RefreshCw, Shield, Mail, Users } from "lucide-react";
import { StatusBadge } from "./_components/status-badge";
import { DnsRecordsTable } from "./_components/dns-records-table";

interface Domain {
  id: string;
  name: string;
  tenantId: string;
  region: string;
  status: "PENDING" | "VERIFIED" | "FAILED";
  createdAt: string;
  updatedAt: string;
  dkimStatus?: string;
  spfStatus?: string;
  dmarcStatus?: string;
  inboundStatus?: string;
  dkimRecords?: DnsRecord[];
  spfRecords?: DnsRecord[];
  dmarcRecords?: DnsRecord[];
  inboundRecords?: DnsRecord[];
  verificationRecords?: DnsRecord[];
  isVerifying?: boolean;
  // Additional fields that might come from the API
  [key: string]: any;
}

interface DnsRecord {
  name: string;
  type: string;
  value: string;
  status: "PENDING" | "VERIFIED" | "FAILED";
  description?: string;
}

interface DomainDetailsClientProps {
  domain: Domain;
}

export function DomainDetailsClient({ domain }: DomainDetailsClientProps) {
  const router = useRouter();
  const [isVerifying, setIsVerifying] = React.useState(false);

  // Get the domain name from the domain object
  const domainName = domain?.domain || domain?.name || domain?.id || "unknown-domain";

  // Client-side API functions
  const verifyDomain = async (domainId: string) => {
    try {
      const response = await fetch(`/api/method/domain/verify/${domainId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Include cookies for authentication
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`,
        );
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || "Failed to verify domain",
      };
    }
  };

  const handleVerify = async () => {
    setIsVerifying(true);
    try {
      const result = await verifyDomain(domain.id);
      if (result.success) {
        toast.success("Domain verification initiated");
        router.refresh();
      } else {
        toast.error(result.error || "Failed to verify domain");
      }
    } catch (error) {
      toast.error("Failed to verify domain");
    } finally {
      setIsVerifying(false);
    }
  };

  // Extract DKIM records from the API response
  const dkimRecords = domain?.dnsRecords?.filter((record: any) =>
    record.type === "TXT" && record.name.includes("_domainkey")
  ) || [];

  const sections = React.useMemo(() => [
    {
      title: "DKIM",
      description:
        "DomainKeys Identified Mail (DKIM) enhances email security by adding digital signatures to verify sender authenticity.",
      status: domain?.dkimStatus ?? "NOT_STARTED",
      records: [
        {
          type: "TXT",
          name: `camped._domainkey.${domain?.name}`,
          value: `p=${domain?.publicKey}`,
          status: "PENDING" as const,
        },
      ],
      icon: Shield,
    },
    {
      title: "SPF & Feedback",
      description:
        "Sender Policy Framework (SPF) prevents email spoofing by specifying which servers can send emails from your domain.",
      status: domain?.spfDetails ?? "NOT_STARTED",
      records: [
        {
          type: "MX",
          name: `camped.${domain?.name}`,
          value: `10 feedback-smtp.${domain.region}.amazonses.com`,
          status: "PENDING" as const,
        },
        {
          type: "TXT",
          name: domain?.name,
          value: `"v=spf1 include:amazonses.com ~all"`,
          status: "PENDING" as const,
        },
      ],
      icon: Mail,
    },
    {
      title: "Inbound",
      description:
        "Inbound email configuration allows your domain to receive emails through the mail service.",
      status: domain?.inboundMXAdded ? "SUCCESS" : "NOT_STARTED",
      records: [
        {
          type: "MX",
          name: domain?.name,
          value: `10 inbound-smtp.${domain.region}.amazonaws.com`,
          status: "PENDING" as const,
        },
      ],
      icon: Mail,
    },
    {
      title: "DMARC",
      description:
        "Domain-based Message Authentication, Reporting & Conformance (DMARC) provides email authentication and reporting.",
      status: domain?.dmarcAdded ? "SUCCESS" : "NOT_STARTED",
      records: [
        {
          type: "TXT",
          name: `_dmarc.${domain?.name}`,
          value: "v=DMARC1; p=none;",
          status: "PENDING" as const,
        },
      ],
      icon: Shield,
    },
  ], [domain, domainName]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Globe className="h-6 w-6 text-muted-foreground" />
          <div>
            <h1 className="text-2xl font-semibold">{domainName}</h1>
            <p className="text-sm text-muted-foreground">
              Region: {domain.region || "us-east-1"} • Created:{" "}
              {new Date(domain.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <StatusBadge status={domain.status} />
          {domain.status !== "VERIFIED" && (
            <Button
              onClick={handleVerify}
              disabled={isVerifying}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isVerifying ? "animate-spin" : ""}`}
              />
              {isVerifying ? "Verifying..." : "Verify Again"}
            </Button>
          )}
        </div>
      </div>

      {(domain as any)?.status === "SUCCESS" && (
        <div className="flex items-center justify-end space-x-3">
          <Button
            onClick={() =>
              router.push(`/domain/${domain.id}/view/email-identities`)
            }
            className="flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            Manage Email Identities
          </Button>
        </div>
      )}

      {/* DNS Records */}
      <div className="grid gap-6">
        {sections.map((section) => {
          const Icon = section.icon;
          return (
            <Card key={section.title}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Icon className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <CardTitle className="text-lg">{section.title}</CardTitle>
                      <CardDescription className="mt-1">
                        {section.description}
                      </CardDescription>
                    </div>
                  </div>
                  {section.status && <StatusBadge status={section.status} />}
                </div>
              </CardHeader>
              <CardContent>
                <DnsRecordsTable
                  records={section.records}
                  title={section.title}
                />
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
