import { notFound } from "next/navigation";
import { getDomain } from "./actions";
import { DomainDetailsClient } from "./page-client";

interface DomainDetailsPageProps {
  params: Promise<{ id: string }>;
}

export default async function DomainDetailsPage({
  params,
}: DomainDetailsPageProps) {
  const { id } = await params;

  const result = await getDomain(id);

  console.log({result});


  if (!result.success || !result.data) {
    notFound();
  }

  return <DomainDetailsClient domain={result.data} />;
}
