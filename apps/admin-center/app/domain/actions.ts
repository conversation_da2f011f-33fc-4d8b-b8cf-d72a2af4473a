import { unstable_noStore as noStore } from "next/cache";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { validateAction } from "@flinkk/shared-rbac";
import { createMailAPIFromConfig } from "@flinkk/shared-utils/mail-config-helper";

const MAIL_API_URL = process.env.NEXT_PUBLIC_MAIL_APP_URL;
// Map external API status to our Domain status
function mapDomainStatus(
  externalStatus: string,
): "PENDING" | "VERIFIED" | "FAILED" {
  switch (externalStatus?.toUpperCase()) {
    case "SUCCESS":
    case "VERIFIED":
      return "VERIFIED";
    case "PENDING":
      return "PENDING";
    case "FAILED":
    case "ERROR":
      return "FAILED";
    default:
      return "PENDING";
  }
}

// Transform external API domain to our Domain interface
function transformDomain(externalDomain: any, tenantId: string): any {
  return {
    id: externalDomain.id,
    name: externalDomain.name,
    tenantId: tenantId,
    region: externalDomain.region || "us-east-1",
    status: mapDomainStatus(externalDomain.status),
    createdAt: externalDomain.createdAt || new Date().toISOString(),
    updatedAt:
      externalDomain.updatedAt ||
      externalDomain.createdAt ||
      new Date().toISOString(),
    dkimStatus: externalDomain.dkimStatus
      ? mapDomainStatus(externalDomain.dkimStatus)
      : undefined,
    spfStatus: externalDomain.spfStatus
      ? mapDomainStatus(externalDomain.spfStatus)
      : undefined,
    isVerifying: false,
  };
}

export async function getDomains(input: {
  page?: number;
  per_page?: number;
  sort?: string;
  search?: string;
  from?: string;
  to?: string;
  operator?: string;
}) {
  noStore();

  try {
    // Validate user has permission to read domains
    await validateAction(
      "Mail Configuration",
      "read",
      "You do not have permission to view domains.",
    );

    const { page = 1, per_page = 10, sort, search } = input;

    // Get authentication from server session
    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      console.warn("No mail configuration found for tenant:", tenantId);
      return {
        data: [],
        pageCount: 1,
      };
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Additional safety check
    if (!mailAPI) {
      console.error("Mail API instance is undefined for tenant:", tenantId);
      return {
        data: [],
        pageCount: 1,
      };
    }

    // Get domains using the configured API with the mail tenant ID
    let rawDomains = await mailAPI.listDomains(mailTenantId);

    // Ensure rawDomains is an array
    if (!Array.isArray(rawDomains)) {
      console.error("Raw domains is not an array:", rawDomains);
      rawDomains = [];
    }

    // Transform domains to match our interface
    let domains = rawDomains.map((domain: any) =>
      transformDomain(domain, tenantId),
    );

    // Apply search filter if provided
    if (search && domains.length > 0) {
      domains = domains.filter(
        (domain: any) =>
          domain.name &&
          domain.name.toLowerCase().includes(search.toLowerCase()),
      );
    }

    // Apply sorting if domains is not empty
    if (domains.length > 0 && sort && typeof sort === "string") {
      const [sortBy, sortOrder] = sort.split(".");
      domains.sort((a: any, b: any) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];

        if (sortOrder === "asc") {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }

    // Apply pagination
    const totalCount = domains.length;
    const totalPages = Math.ceil(totalCount / per_page);
    const startIndex = (page - 1) * per_page;
    const paginatedDomains = domains.slice(startIndex, startIndex + per_page);

    return {
      data: paginatedDomains,
      pageCount: totalPages,
    };
  } catch (error) {
    console.error("Error fetching domains:", error);
    return {
      data: [],
      pageCount: 1,
    };
  }
}

// Get email identities for a domain
export async function getEmailIdentities(domainId: string) {
  noStore();

  try {
    // Validate user has permission to read email identities
    await validateAction(
      "Mail Configuration",
      "read",
      "You do not have permission to view email identities.",
    );

    // Get authentication from server session
    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return {
        success: false,
        error: "No mail configuration found. Please configure mail settings first.",
        data: [],
      };
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Get email identities using the configured API
    const emailIdentities = await mailAPI.listEmailIdentities({
      tenantId: mailTenantId,
      domainId: domainId,
    });

    return {
      success: true,
      data: emailIdentities || [],
    };
  } catch (error) {
    console.error("Error fetching email identities:", error);
    return {
      success: false,
      error: "Failed to fetch email identities",
      data: [],
    };
  }
}

// Create an email identity
export async function createEmailIdentity(
  domainId: string,
  emailData: { name: string },
) {
  noStore();

  try {
    // Validate user has permission to create email identities
    await validateAction(
      "Mail Configuration",
      "create",
      "You do not have permission to create email identities.",
    );
    // Get authentication from server session
    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return {
        success: false,
        error: "No mail configuration found. Please configure mail settings first.",
      };
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Create email identity using the configured API
    const emailIdentity = await mailAPI.createEmailIdentity({
      name: emailData.name,
      domainId: domainId,
      tenantId: mailTenantId,
    });

    return {
      success: true,
      data: emailIdentity,
    };
  } catch (error: any) {
    console.error("Error creating email identity:", error);
    return {
      success: false,
      error: error.message || "Failed to create email identity",
    };
  }
}

// Delete an email identity
export async function deleteEmailIdentity(emailId: string) {
  noStore();

  try {
    // Validate user has permission to delete email identities
    await validateAction(
      "Mail Configuration",
      "delete",
      "You do not have permission to delete email identities.",
    );

    // Get authentication from server session
    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return {
        success: false,
        error: "No mail configuration found. Please configure mail settings first.",
      };
    }

    const { mailAPI } = mailConfig;

    // Delete email identity using the configured API
    const success = await mailAPI.deleteEmailIdentity(emailId);

    return {
      success: success,
    };
  } catch (error) {
    console.error("Error deleting email identity:", error);
    return {
      success: false,
      error: "Failed to delete email identity",
    };
  }
}
