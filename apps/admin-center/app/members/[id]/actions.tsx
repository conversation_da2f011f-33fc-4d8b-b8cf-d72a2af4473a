"use server";

import { prisma } from "@flinkk/database/prisma";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { unstable_noStore as noStore } from "next/cache";
import { createMailAPIFromConfig } from "@flinkk/shared-utils/mail-config-helper";

export async function getMemberById(id: string) {
  try {
    const { userId, tenantId } = await getServerSession();

    if (!userId || !tenantId) {
      throw new Error("Unauthorized");
    }

    // Check if user has permission to view members (OWNER or ADMIN)
    const requesterMembership = await prisma.memberShip.findFirst({
      where: {
        userId: userId,
        tenantId: tenantId,
      },
    });

    if (!requesterMembership) {
      throw new Error("You don't have permission to view members");
    }

    // Find the membership with user and custom role data
    const membership = await prisma.memberShip.findFirst({
      where: {
        id: id,
        tenantId: tenantId, // Ensure it belongs to the current tenant
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            lastLogin: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        customRole: {
          select: {
            id: true,
            name: true,
            description: true,
            isSystem: true,
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!membership) {
      throw new Error("Member not found");
    }

    // Return formatted member data
    return {
      id: membership.id,
      name: membership.user.name,
      email: membership.user.email,
      image: membership.user.image,
      lastLogin: membership.user.lastLogin,
      roleId: membership.roleId,
      customRole: membership.customRole,
      isDefault: membership.isDefault,
      createdAt: membership.createdAt,
      updatedAt: membership.updatedAt,
      tenant: membership.tenant,
    };
  } catch (error) {
    console.error("Error fetching member by ID:", error);
    throw error;
  }
}

export interface EmailIdentity {
  id: string;
  name: string;
  domainId: string;
  tenantId: string;
  status?: "PENDING" | "VERIFIED" | "FAILED";
  createdAt: string;
  updatedAt: string;
}

export interface EmailIdentitiesResponse {
  success: boolean;
  data?: EmailIdentity[];
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
  };
}

// Get email identities for a domain
export async function listEmailIdentities(): Promise<EmailIdentitiesResponse> {
  noStore();

  try {
    // Get authentication from server session
    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    // Create mail API instance using stored configuration
    const mailConfig = await createMailAPIFromConfig(tenantId);

    if (!mailConfig) {
      return {
        success: false,
        error: "No mail configuration found. Please configure mail settings first.",
        data: [],
      };
    }

    const { mailAPI, mailTenantId } = mailConfig;

    // Get email identities using the configured API
    const emailIdentities = await mailAPI.listEmailIdentities({
      tenantId: mailTenantId,
    });

    const pagination = {
      page: 1,
      limit: 10,
      totalCount: emailIdentities.length,
      totalPages: 1,
    };

    return {
      success: true,
      data: emailIdentities,
      pagination,
    };
  } catch (error) {
    console.error("Error fetching email identities:", error);
    return {
      success: false,
      error: "Failed to fetch email identities",
      data: [],
    };
  }
}
