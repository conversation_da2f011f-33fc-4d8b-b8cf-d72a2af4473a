import { notFound } from "next/navigation";
import { PageClient } from "./page-client";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { prisma } from "@flinkk/database/prisma";

export default async function UnitsPage({
  params,
  searchParams,
}: {
  params: Promise<{ model: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const { model } = resolvedParams;

  // Only allow units page for products
  if (model !== "products") {
    notFound();
  }

  // Get pagination parameters
  const page = Number(resolvedSearchParams.page) || 1;
  const perPage = Number(resolvedSearchParams.perPage) || 10;
  const skip = (page - 1) * perPage;

  // Get session for tenant info
  const session = await getServerSession();
  const tenantId = session?.tenantId;

  if (!tenantId) {
    notFound();
  }

  // Fetch units data
  const [units, total] = await Promise.all([
    prisma.unit.findMany({
      where: {
        tenantId: tenantId,
        deleted: false,
      },
      orderBy: [
        { isSystem: "desc" }, // System units first
        { displayName: "asc" }, // Then alphabetical
      ],
      skip,
      take: perPage,
    }),
    prisma.unit.count({
      where: {
        tenantId: tenantId,
        deleted: false,
      },
    }),
  ]);

  return <PageClient data={units} total={total} page={page} perPage={perPage} model={model} />;
}
