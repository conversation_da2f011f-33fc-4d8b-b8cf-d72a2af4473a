"use client";

import React, { useState } from "react";
import { Badge } from "@flinkk/components/ui/badge";
import { Button } from "@flinkk/components/ui/button";
import { Pencil, Trash2, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "@flinkk/components/ui/alert-dialog";
import { toast } from "sonner";

export const getUnitsColumns = () => {
  const router = useRouter();
  // State for dialog and selected unit
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [selectedUnit, setSelectedUnit] = React.useState<any>(null);
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleDeleteClick = (unit: any) => {
    setSelectedUnit(unit);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedUnit) return;
    
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/units/${selectedUnit.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Unit deleted successfully");
        setDeleteDialogOpen(false);
        setSelectedUnit(null);
        router.refresh();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to delete unit");
      }
    } catch (error) {
      console.error("Error deleting unit:", error);
      toast.error("Failed to delete unit");
    } finally {
      setIsDeleting(false);
    }
  };

  return [
    {
      accessorKey: "displayName",
      header: "Unit Name",
      cell: ({ row }: any) => (
        <div className="font-medium">{row.original.displayName}</div>
      ),
    },
    {
      accessorKey: "name",
      header: "API Name",
      cell: ({ row }: any) => (
        <div className="font-mono text-xs">{row.original.name}</div>
      ),
    },
    {
      accessorKey: "isSystem",
      header: "Type",
      cell: ({ row }: any) => (
        <Badge variant={row.original.isSystem ? "default" : "secondary"}>
          {row.original.isSystem ? "System" : "Custom"}
        </Badge>
      ),
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }: any) => (
        <div className="text-sm text-muted-foreground">
          {new Date(row.original.createdAt).toLocaleDateString()}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }: any) => (
        <div className="flex items-center justify-end gap-2">
          <Button
            variant="ghost"
            size="icon"
            title="Edit Unit"
            onClick={() => router.push(`units/${row.original.id}`)}
            disabled={row.original.isSystem}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            title="Delete Unit"
            onClick={() => handleDeleteClick(row.original)}
            disabled={row.original.isSystem || isDeleting}
          >
            {isDeleting && selectedUnit?.id === row.original.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
          </Button>
          {/* Delete confirmation dialog */}
          {deleteDialogOpen && selectedUnit?.id === row.original.id && (
            <AlertDialog
              open={deleteDialogOpen}
              onOpenChange={setDeleteDialogOpen}
            >
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Unit</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete the unit "
                    {selectedUnit.displayName}"? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleConfirmDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Deleting...
                      </>
                    ) : (
                      "Delete"
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      ),
    },
  ];
};
