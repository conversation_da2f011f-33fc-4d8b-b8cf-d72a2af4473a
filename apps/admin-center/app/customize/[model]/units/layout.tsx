import { HeaderTab } from "../_components/header-tab";
import { Typography } from "@flinkk/components/ui/typography";
import { Button } from "@flinkk/components/ui/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";

export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ model: string }>;
}) {
  const resolvedParams = await params;
  const { model } = resolvedParams;
  return (
    <>
      <HeaderTab model={model} defaultValue="units" />
      <div className="flex justify-between items-center">
        <Typography type="h3">Product Units</Typography>
        <Button className="flex items-center gap-2" size="sm" asChild>
          <Link href="units/new">
            <PlusIcon className="h-4 w-4" />
            Add Unit
          </Link>
        </Button>
      </div>
      {children}
    </>
  );
}
