"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { Input } from "@flinkk/components/ui/input";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Loader2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@flinkk/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@flinkk/components/ui/alert-dialog";
import { toast } from "sonner";

// Validation schema for unit form
const unitFormSchema = z.object({
  name: z
    .string()
    .min(1, "Unit name is required")
    .max(50, "Unit name must be less than 50 characters")
    .regex(/^[a-zA-Z0-9_-]+$/, "Unit name can only contain letters, numbers, hyphens, and underscores"),
  displayName: z
    .string()
    .min(1, "Display name is required")
    .max(100, "Display name must be less than 100 characters"),
});

type UnitFormValues = z.infer<typeof unitFormSchema>;

interface UnitFormProps {
  id: string;
  initialData?: any;
  model: string;
}

export function PageClient({
  id,
  initialData,
  model,
}: UnitFormProps) {
  const router = useRouter();
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const isNewRecord = id === "new";

  // Custom save function for units
  const [isSaving, setIsSaving] = useState(false);

  const saveUnit = async (unitId: string, formData: UnitFormValues) => {
    setIsSaving(true);
    try {
      const url = isNewRecord ? "/api/units" : `/api/units/${unitId}`;
      const response = await fetch(url, {
        method: isNewRecord ? "POST" : "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error ||
            errorData.message ||
            `API error: ${response.statusText}`,
        );
      }

      toast.success(
        `Unit ${isNewRecord ? "created" : "updated"} successfully`,
      );
      router.push(`/customize/${model}/units`);
    } catch (error) {
      console.error("Error saving unit:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to save unit",
      );
    } finally {
      setIsSaving(false);
    }
  };

  const form = useForm<UnitFormValues>({
    resolver: zodResolver(unitFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      displayName: initialData?.displayName || "",
    },
  });

  const onSubmit = async (formData: UnitFormValues) => {
    const unitId = isNewRecord ? "new" : id;
    await saveUnit(unitId, formData);
  };

  const handleCancel = () => {
    if (form.formState.isDirty) {
      setShowCancelDialog(true);
    } else {
      router.push(`/customize/${model}/units`);
    }
  };

  const confirmCancel = () => {
    setShowCancelDialog(false);
    router.push(`/customize/${model}/units`);
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Unit Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., kg, pcs, hrs"
                      {...field}
                      disabled={isSaving}
                    />
                  </FormControl>
                  <FormDescription>
                    Short code for the unit (e.g., kg, pcs, hrs). Only letters, numbers, hyphens, and underscores allowed.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="displayName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Display Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Kilograms, Pieces, Hours"
                      {...field}
                      disabled={isSaving}
                    />
                  </FormControl>
                  <FormDescription>
                    Human-readable name for the unit that will be displayed to users.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSaving}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isNewRecord ? "Create Unit" : "Update Unit"}
            </Button>
          </div>
        </form>
      </Form>

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Discard changes?</AlertDialogTitle>
            <AlertDialogDescription>
              You have unsaved changes. Are you sure you want to discard them?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Continue editing</AlertDialogCancel>
            <AlertDialogAction onClick={confirmCancel}>
              Discard changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
