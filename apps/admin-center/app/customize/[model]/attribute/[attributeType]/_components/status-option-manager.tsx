"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import { Label } from "@flinkk/components/ui/label";
import { Textarea } from "@flinkk/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@flinkk/components/ui/card";
import { Badge } from "@flinkk/components/ui/badge";
import { PlusIcon, Trash2, Edit, Save, X } from "lucide-react";
import toast from "react-hot-toast";

interface StatusOption {
  id?: string;
  value: string;
  label: string;
  description?: string;
  color?: string;
  order?: number;
  isDefault?: boolean;
}

interface StatusOptionManagerProps {
  type: string;
  typeName: string;
  initialOptions: StatusOption[];
  tenantId: string;
  userId: string;
}

export function StatusOptionManager({
  type,
  typeName,
  initialOptions,
  tenantId,
  userId,
}: StatusOptionManagerProps) {
  const [options, setOptions] = useState<StatusOption[]>(initialOptions || []);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [newOption, setNewOption] = useState<StatusOption>({
    value: "",
    label: "",
    description: "",
    color: "#3b82f6",
  });

  const handleSave = async (option: StatusOption) => {
    try {
      const response = await fetch("/api/status-options", {
        method: option.id ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...option,
          type,
          tenantId,
          userId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to save option");
      }

      const savedOption = await response.json();
      
      if (option.id) {
        setOptions(options.map(opt => opt.id === option.id ? savedOption : opt));
      } else {
        setOptions([...options, savedOption]);
      }

      toast.success(`${typeName} option saved successfully`);
      setEditingId(null);
      setIsAdding(false);
      setNewOption({ value: "", label: "", description: "", color: "#3b82f6" });
    } catch (error) {
      toast.error("Failed to save option");
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/status-options/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete option");
      }

      setOptions(options.filter(opt => opt.id !== id));
      toast.success(`${typeName} option deleted successfully`);
    } catch (error) {
      toast.error("Failed to delete option");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Manage {typeName} Options</h2>
        <Button
          onClick={() => setIsAdding(true)}
          disabled={isAdding}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add Option
        </Button>
      </div>

      <div className="grid gap-4">
        {isAdding && (
          <Card>
            <CardHeader>
              <CardTitle>Add New Option</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="new-value">Value</Label>
                  <Input
                    id="new-value"
                    value={newOption.value}
                    onChange={(e) => setNewOption({ ...newOption, value: e.target.value })}
                    placeholder="e.g., NEW"
                  />
                </div>
                <div>
                  <Label htmlFor="new-label">Label</Label>
                  <Input
                    id="new-label"
                    value={newOption.label}
                    onChange={(e) => setNewOption({ ...newOption, label: e.target.value })}
                    placeholder="e.g., New"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="new-description">Description</Label>
                <Textarea
                  id="new-description"
                  value={newOption.description}
                  onChange={(e) => setNewOption({ ...newOption, description: e.target.value })}
                  placeholder="Optional description"
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={() => handleSave(newOption)}>
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button variant="outline" onClick={() => setIsAdding(false)}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {options.map((option) => (
          <Card key={option.id}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge variant="secondary">{option.value}</Badge>
                  <div>
                    <div className="font-medium">{option.label}</div>
                    {option.description && (
                      <div className="text-sm text-muted-foreground">
                        {option.description}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingId(option.id || null)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => option.id && handleDelete(option.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
