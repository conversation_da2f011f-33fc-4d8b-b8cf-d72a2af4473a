import { baseQuery } from "@flinkk/database/base-query";

/**
 * Get records for a specific attribute type
 * @param type The attribute type (e.g., "lead_status", "task_priority")
 * @returns Promise with the records data
 */
export async function getRecords(type: string) {
  try {
    const result = await baseQuery(
      "statusOption",
      { page: 1, perPage: 100 },
      [{ type: type }]
    );
    
    return {
      data: result.data || [],
      total: result.total || 0,
    };
  } catch (error) {
    console.error("Error fetching records:", error);
    return {
      data: [],
      total: 0,
    };
  }
}
