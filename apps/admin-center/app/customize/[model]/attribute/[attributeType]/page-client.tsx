"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { StatusOptionManager } from "./_components/status-option-manager";
import { getAttributesByModel } from "./_config/entity-attributes";
import { HeaderTab } from "../../_components/header-tab";

interface AttributePageClientProps {
  model: string;
  modelName: string;
  attributeType: string;
  type: string;
  typeName: string;
  description: string;
  tenantId: string;
  userId: string;
  statusOptions: any[];
}

/**
 * Client component for entity attributes
 * This component handles the UI for all attribute types
 */
export function AttributePageClient({
  model,
  modelName,
  attributeType,
  type,
  typeName,
  description,
  tenantId,
  userId,
  statusOptions,
}: AttributePageClientProps) {
  const router = useRouter();

  // Get all attributes for this model
  const attributes = getAttributesByModel(model);

  // Calculate number of tabs (standard fields + custom fields + attributes)
  const tabCount = 2 + attributes.length;

  return (
    <>
      <HeaderTab model={model} />
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold">{typeName}</h1>
            <p className="text-muted-foreground">{description}</p>
          </div>
        </div>

        <StatusOptionManager
          type={type}
          typeName={typeName}
          initialOptions={statusOptions}
          tenantId={tenantId}
          userId={userId}
        />
      </div>
    </>
  );
}
