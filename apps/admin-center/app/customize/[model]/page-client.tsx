"use client";

import React from "react";
import { HeaderTab } from "./_components/header-tab";
import { StandardFieldsTable } from "./_components/standard-fields-table";
import {
  ModelName,
  modelFieldsMap,
} from "@flinkk/shared-entity/standard-fields";

interface CustomizePageClientProps {
  model: string;
  data: any;
  total: number;
  page: number;
  perPage: number;
}

export function PageClient({
  model,
  data: initialData,
  total: initialTotal,
  page: initialPage,
  perPage: initialPerPage,
}: CustomizePageClientProps) {
  // Convert model name to entity type for display
  const getEntityType = (model: string) => {
    const entityMap: Record<string, string> = {
      leads: "Lead",
      accounts: "BusinessAccount",
      opportunities: "Opportunity",
      contacts: "Contact",
      campaigns: "Campaign",
      products: "Product",
      packages: "Package",
      tasks: "Task",
      tickets: "Ticket",
      pricebooks: "PriceBook",
    };
    return entityMap[model] || model;
  };

  const [page, setPage] = React.useState(initialPage);
  const [perPage, setPerPage] = React.useState(initialPerPage);
  const [data, setData] = React.useState(initialData);
  const [total, setTotal] = React.useState(initialTotal);
  const [loading, setLoading] = React.useState(false);

  // Client-side data fetching function
  const fetchTableData = (
    model: ModelName,
    page: number = 1,
    perPage: number = 10,
  ) => {
    try {
      if (!modelFieldsMap[model]) {
        throw new Error(`Invalid model: ${model}`);
      }

      // Convert object to array for pagination
      const allFields = Object.values(modelFieldsMap[model]);
      const total = allFields.length;
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const data = allFields.slice(start, end);
      return { data, total };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  };

  // Fetch data when page or perPage changes
  React.useEffect(() => {
    let isMounted = true;
    setLoading(true);
    const result = fetchTableData(model as ModelName, page, perPage);
    if (isMounted && result && result.data) {
      setData(result.data);
      setTotal(result.total);
    }
    setLoading(false);
    return () => {
      isMounted = false;
    };
  }, [model, page, perPage]);

  // Handler for DataTable pagination change
  const handlePaginationChange = (newPage: number, newPerPage: number) => {
    setPage(newPage);
    setPerPage(newPerPage);
  };

  return (
    <>
      <HeaderTab model={model} />
      <StandardFieldsTable
        fields={data}
        entityType={getEntityType(model)}
        total={total}
        page={page}
        perPage={perPage}
        loading={loading}
        onPaginationChange={handlePaginationChange}
      />
    </>
  );
}
