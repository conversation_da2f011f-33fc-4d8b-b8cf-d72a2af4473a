import { notFound } from "next/navigation";
import { prisma } from "@flinkk/database/prisma";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { PageClient } from "./page-client";

export default async function CategoryDetailPage({
  params,
}: {
  params: Promise<{ id: string; model: string }>;
}) {
  const { id, model } = await params;
  const isNewRecord = id === "new";

  // Only allow categories for products model
  if (model !== "products") {
    notFound();
  }

  let initialData = null;

  if (!isNewRecord) {
    const session = await getServerSession();
    const tenantId = session?.tenantId;

    if (!tenantId) {
      notFound();
    }

    // Fetch existing category data
    initialData = await prisma.productCategory.findFirst({
      where: {
        id: id,
        tenantId: tenantId,
        deleted: false,
      },
    });

    if (!initialData) {
      notFound();
    }
  }

  return (
    <PageClient
      id={id}
      initialData={initialData}
      model={model}
    />
  );
}
