import { PageClient } from "./page-client";
import { fetchTableData } from "./actions";

const validModels = [
  "leads",
  "accounts",
  "opportunities",
  "contacts",
  "products",
  "packages",
  "tasks",
  "pricebooks",
] as const;
type ValidModel = (typeof validModels)[number];

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ model: string }>;
  searchParams?: Promise<{ page?: string; perPage?: string }>;
}) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const { model } = resolvedParams;

  // Validate that the model is one of the expected values
  if (!validModels.includes(model as ValidModel)) {
    throw new Error(
      `Invalid model: ${model}. Expected one of: ${validModels.join(", ")}`,
    );
  }

  const page = resolvedSearchParams?.page
    ? parseInt(resolvedSearchParams.page, 10)
    : 1;
  const perPage = resolvedSearchParams?.perPage
    ? parseInt(resolvedSearchParams.perPage, 10)
    : 10;

  const { data, pageCount } = await fetchTableData(
    model as ValidModel,
    page,
    perPage,
  );
  const total = pageCount * perPage; // Estimate total (for now, as baseQuery returns pageCount)

  return (
    <PageClient
      model={model}
      data={data}
      total={total}
      page={page}
      perPage={perPage}
    />
  );
}
