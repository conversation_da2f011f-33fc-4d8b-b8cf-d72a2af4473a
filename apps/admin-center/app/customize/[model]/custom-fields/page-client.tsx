"use client";

import React from "react";
import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { getColumns } from "./columns";
import { useRouter, useSearchParams } from "next/navigation";
import { HeaderTab } from "../_components/header-tab";

interface CustomFieldsPageClientProps {
  model: string;
  data: any[];
  total: number;
  page: number;
  perPage: number;
}

export function PageClient({
  model,
  data,
  total,
  page,
  perPage,
}: CustomFieldsPageClientProps) {
  const columns = getColumns();
  const router = useRouter();
  const searchParams = useSearchParams();

  const { table } = useDataTable({
    data,
    columns,
    pageCount: Math.ceil(total / perPage),
    initialState: {
      pagination: {
        pageIndex: page - 1,
        pageSize: perPage,
      },
    },
    onPaginationChange: (paginationUpdater) => {
      let pageIndex, pageSize;
      if (typeof paginationUpdater === "function") {
        const newPagination = paginationUpdater({
          pageIndex: page - 1,
          pageSize: perPage,
        });
        pageIndex = newPagination.pageIndex;
        pageSize = newPagination.pageSize;
      } else {
        pageIndex = paginationUpdater.pageIndex;
        pageSize = paginationUpdater.pageSize;
      }
      const newPage = pageIndex + 1;
      const newPerPage = pageSize;
      const params = new URLSearchParams(searchParams?.toString() || "");
      params.set("page", String(newPage));
      params.set("perPage", String(newPerPage));
      router.push(`?${params.toString()}`);
    },
  });

  return (
    <>
      <HeaderTab model={model} />
      <DataTable table={table} doctype={`${model}CustomFields`} />
    </>
  );
}
