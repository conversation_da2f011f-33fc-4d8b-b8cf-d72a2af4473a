"use client";

import React, { useState } from "react";
import { Badge } from "@flinkk/components/ui/badge";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Pencil, Trash2, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import typeMap from "@flinkk/shared-entity/input-field.json";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "@flinkk/components/ui/alert-dialog";

import toast from "react-hot-toast";

function getFieldTypeDisplay(type: string): string {
  return typeMap[type as keyof typeof typeMap] || type;
}

function getAutoGroupName(type: string): string {
  // Fields without groupName go to "Additional Information"
  return "Additional Information";
}

export const getColumns = () => {
  const router = useRouter();
  // State for dialog and selected field
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [selectedField, setSelectedField] = React.useState<any>(null);

  // State for deletion loading
  const [isDeleting, setIsDeleting] = React.useState(false);

  // Custom delete function for custom fields
  const deleteCustomField = async (fieldId: string) => {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/custom-fields/${fieldId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete custom field");
      }

      toast.success("Custom field deleted successfully");
      setDeleteDialogOpen(false);
      setSelectedField(null);
      // Refresh the page or table
      router.refresh?.();
    } catch (error) {
      console.error("Error deleting custom field:", error);
      toast.error("Failed to delete custom field");
      setDeleteDialogOpen(false);
      setSelectedField(null);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteClick = (field: any) => {
    setSelectedField(field);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedField) return;
    await deleteCustomField(selectedField.id);
  };

  return [
    {
      accessorKey: "label",
      header: "Field Label",
      cell: ({ row }: any) => (
        <div className="font-medium">{row.original.label}</div>
      ),
    },
    {
      accessorKey: "name",
      header: "API Name",
      cell: ({ row }: any) => (
        <div className="font-mono text-xs">{row.original.name}</div>
      ),
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }: any) => (
        <Badge variant="outline">
          {getFieldTypeDisplay(row.original.type)}
        </Badge>
      ),
    },
    {
      accessorKey: "groupName",
      header: "Group",
      cell: ({ row }: any) => {
        const groupName = row.original.groupName;
        const fieldType = row.original.type;

        if (!groupName) {
          return (
            <div className="flex flex-col gap-1">
              <Badge variant="secondary" className="text-xs">
                Additional Information
              </Badge>
              <span className="text-xs text-muted-foreground">
                Default group
              </span>
            </div>
          );
        }

        return (
          <div className="flex flex-col gap-1">
            <Badge variant="default" className="text-xs">
              {groupName}
            </Badge>
            <span className="text-xs text-muted-foreground">Custom group</span>
          </div>
        );
      },
      filterFn: (row: any, id: string, value: string) => {
        if (!value || value === "all") return true;

        const groupName = row.original.groupName;

        if (value === "custom") {
          return !!groupName;
        }

        if (value === "additional") {
          return !groupName;
        }

        // Filter by specific group name
        const actualGroup = groupName || "Additional Information";
        return actualGroup === value;
      },
    },
    {
      accessorKey: "isRequired",
      header: "Required",
      cell: ({ row }: any) => (
        <Badge variant={row.original.isRequired ? "default" : "outline"}>
          {row.original.isRequired ? "Yes" : "No"}
        </Badge>
      ),
    },
    {
      id: "actions",
      cell: ({ row }: any) => (
        <div className="flex items-center justify-end gap-2">
          <Button
            variant="ghost"
            size="icon"
            title="Edit Field"
            onClick={() => router.push(`custom-fields/${row.original.id}`)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            title="Delete Field"
            onClick={() => handleDeleteClick(row.original)}
            disabled={row.original.isSystem || isDeleting}
          >
            {isDeleting && selectedField?.id === row.original.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
          </Button>
          {/* Delete confirmation dialog (one instance, controlled by state) */}
          {deleteDialogOpen && selectedField?.id === row.original.id && (
            <AlertDialog
              open={deleteDialogOpen}
              onOpenChange={setDeleteDialogOpen}
            >
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Custom Field</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete the field "
                    {selectedField.label}"? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleConfirmDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Deleting...
                      </>
                    ) : (
                      <>Delete</>
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      ),
    },
  ];
};
