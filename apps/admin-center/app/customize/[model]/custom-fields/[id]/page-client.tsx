"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { Input } from "@flinkk/components/ui/input";
import { Label } from "@flinkk/components/ui/label";
import { Button } from "@flinkk/components/ui/button";
import { Checkbox } from "@flinkk/components/ui/checkbox";
import { Textarea } from "@flinkk/components/ui/textarea";
import toast from "react-hot-toast";
import { Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import { Stepper, StepperContent } from "@flinkk/components/ui/stepper";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertD<PERSON>ogHeader,
  AlertD<PERSON>ogTitle,
} from "@flinkk/components/ui/alert-dialog";
// Define CustomFieldType enum locally instead of importing from Prisma
enum CustomFieldType {
  TEXT = "TEXT",
  TEXTAREA = "TEXTAREA",
  RICH_TEXT = "RICH_TEXT",
  NUMBER = "NUMBER",
  CURRENCY = "CURRENCY",
  PERCENT = "PERCENT",
  DATE = "DATE",
  DATETIME = "DATETIME",
  TIME = "TIME",
  BOOLEAN = "BOOLEAN",
  CHECKBOX = "CHECKBOX",
  SELECT = "SELECT",
  MULTI_SELECT = "MULTI_SELECT",
  PICKLIST = "PICKLIST",
  URL = "URL",
  EMAIL = "EMAIL",
  PHONE = "PHONE",
  FORMULA = "FORMULA",
  LOOKUP = "LOOKUP",
  GEOLOCATION = "GEOLOCATION",
  FILE = "FILE",
  IMAGE = "IMAGE",
}

// Define CustomField interface locally to match the database schema
interface CustomField {
  name: string;
  label: string;
  description?: string | null;
  type: string;
  isRequired?: boolean;
  defaultValue?: string | null;
  placeholder?: string | null;
  helpText?: string | null;
  options?: any[];
  entityType: string;
  isVisibleByDefault?: boolean;
  isEditableByDefault?: boolean;
  // Advanced properties
  minLength?: number | null;
  maxLength?: number | null;
  minValue?: number | null;
  maxValue?: number | null;
  decimalPlaces?: number;
  displayFormat?: string | null;
  isUnique?: boolean;
  isSearchable?: boolean;
  isSortable?: boolean;
  isFilterable?: boolean;
  validationRule?: string;
  validationMessage?: string;
  maskType?: string | null;
  groupName?: string;
}

// Extended interface for form data that includes UI-only fields
interface CustomFieldFormData
  extends Omit<
    CustomField,
    | "minLength"
    | "maxLength"
    | "minValue"
    | "maxValue"
    | "decimalPlaces"
    | "isUnique"
    | "displayFormat"
    | "validationRule"
    | "validationMessage"
    | "maskType"
    | "isSearchable"
    | "isSortable"
    | "isFilterable"
    | "groupName"
  > {
  minLength?: number | null;
  maxLength?: number | null;
  minValue?: number | null;
  maxValue?: number | null;
  decimalPlaces?: number;
  isUnique?: boolean;
  displayFormat?: string | null;
  validationRule?: string;
  validationMessage?: string;
  maskType?: string | null;
  isSearchable?: boolean;
  isSortable?: boolean;
  isFilterable?: boolean;
  groupName?: string;
  dependencies: any[];
}

import { FormSection } from "./_components/form-section";
import { TextFieldOptions } from "./_components/text-field-options";
import { NumberFieldOptions } from "./_components/number-field-options";
import { DateFieldOptions } from "./_components/date-field-options";
import { SelectFieldOptions } from "./_components/select-field-options";
import { ValidationRuleBuilder } from "./_components/validation-rule-builder";
import { FieldDependencyManager } from "./_components/field-dependency-manager";
import { FieldPreview } from "./_components/field-preview";

interface EnhancedCustomFieldFormProps {
  id: string;
  entityType: string;
  model: string;
  initialData?: any;
  fieldPermissions?: Record<string, { canView: boolean; canEdit: boolean }>;
  availableFields?: any[];
}

export function PageClient({
  id,
  entityType,
  model,
  initialData,
  availableFields = [],
}: EnhancedCustomFieldFormProps) {
  const router = useRouter();

  const [activeStep, setActiveStep] = useState(0);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const isNewRecord = id === "new";

  // Use standardized hook for saving custom field data
  const { save: saveCustomField, isLoading: isSaving } = useSaveFormData({
    model: "customField",
    onSuccess: () => {
      // Don't show toast here as useSaveFormData already shows one
      router.push(`/customize/${model}/custom-fields`);
    },
  });

  const onSubmit = async (formData: CustomField) => {
    const customFieldId = isNewRecord ? "new" : id;
    await saveCustomField(customFieldId, formData);
  };

  const onCancel = () => {
    // If form has been modified, show confirmation dialog
    setShowCancelDialog(true);
  };

  const steps = [
    {
      id: "basic",
      label: "Basic Information",
      description: "Define the field's core properties",
    },
    {
      id: "advanced",
      label: "Advanced Options",
      description: "Configure additional field settings",
    },
    {
      id: "validation",
      label: "Validation Rules",
      description: "Set up validation for the field",
    },
    {
      id: "dependencies",
      label: "Dependencies",
      description: "Define how this field interacts with others",
      optional: true,
    },
  ];
  const [formData, setFormData] = useState<CustomFieldFormData>({
    name: "",
    label: "",
    description: "",
    type: CustomFieldType.TEXT,
    isRequired: false,
    defaultValue: "",
    placeholder: "",
    helpText: "",
    options: [],
    isVisibleByDefault: true,
    isEditableByDefault: false,
    entityType: entityType,

    // Advanced properties
    minLength: null as number | null,
    maxLength: null as number | null,
    minValue: null as number | null,
    maxValue: null as number | null,
    decimalPlaces: 2,
    isUnique: false,
    displayFormat: null as string | null,
    validationRule: "",
    validationMessage: "",
    maskType: null as string | null,
    isSearchable: true,
    isSortable: true,
    isFilterable: true,
    groupName: "",

    // Dependencies (not part of database schema, used for UI only)
    dependencies: [] as any[],
  });

  // Initialize form data if editing an existing field
  useEffect(() => {
    if (initialData) {
      setFormData({
        ...formData,
        ...initialData,
        options: initialData.options || [],
        minLength: initialData.minLength ?? null,
        maxLength: initialData.maxLength ?? null,
        minValue: initialData.minValue ?? null,
        maxValue: initialData.maxValue ?? null,
        decimalPlaces: initialData.decimalPlaces ?? 2,
        displayFormat: initialData.displayFormat ?? null,
        maskType: initialData.maskType ?? null,
        validationRule: initialData.validationRule ?? "",
        validationMessage: initialData.validationMessage ?? "",
        isUnique: initialData.isUnique ?? false,
        isSearchable: initialData.isSearchable ?? true,
        isSortable: initialData.isSortable ?? true,
        isFilterable: initialData.isFilterable ?? true,
        groupName: initialData.groupName ?? "",
        dependencies: initialData?.dependencies || [],
      });
    }
  }, [initialData]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleAdvancedOptionChange = (name: string, value: any) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDependenciesChange = (dependencies: any[]) => {
    setFormData((prev) => ({ ...prev, dependencies }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If not on the last step, just go to the next step
    if (activeStep < steps.length - 1) {
      handleNext();
      return;
    }

    try {
      // Basic validation
      if (!formData.name || !formData.label || !formData.type) {
        toast.error("Please fill in all required fields");
        return;
      }

      // Format options for SELECT and MULTI_SELECT types
      let formattedData: CustomFieldFormData = { ...formData };

      // Handle special field types
      const fieldType = formData.type as string;
      if (
        fieldType === "SELECT" ||
        fieldType === "MULTI_SELECT" ||
        fieldType === "PICKLIST"
      ) {
        // Options are already in the correct format from the SelectFieldOptions component
        // Validate that options are provided for these field types
        if (!formattedData.options || formattedData.options.length === 0) {
          toast.error("Please add at least one option for this field type");
          return;
        }
      } else {
        // For other field types, set options to empty array
        formattedData.options = [];
      }

      // Add entity type
      formattedData.entityType = entityType;

      // Extract only the CustomField data, excluding UI-only fields
      const { dependencies, ...dataToSubmit } = formattedData;

      // Submit the form
      await onSubmit(dataToSubmit);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("An error occurred while submitting the form");
    }
  };

  // Generate a field name from the label
  const generateFieldName = () => {
    if (!formData.label) return;

    const fieldName = formData.label
      .toLowerCase()
      .replace(/[^a-z0-9_]/g, "_")
      .replace(/_+/g, "_")
      .replace(/^_|_$/g, "");

    setFormData((prev) => ({ ...prev, name: fieldName }));
  };

  // Render field type-specific options
  const renderFieldTypeOptions = () => {
    // Use type assertion to convert to string for comparison
    const fieldType = formData.type as string;
    switch (fieldType) {
      case "TEXT":
      case "TEXTAREA":
      case "RICH_TEXT":
      case "EMAIL":
      case "URL":
      case "PHONE":
        return (
          <TextFieldOptions
            minLength={formData.minLength}
            maxLength={formData.maxLength}
            isUnique={formData.isUnique}
            validationMessage={formData.validationMessage}
            maskType={formData.maskType}
            onChange={handleAdvancedOptionChange}
          />
        );
      case "NUMBER":
      case "CURRENCY":
      case "PERCENT":
        return (
          <NumberFieldOptions
            minValue={formData.minValue}
            maxValue={formData.maxValue}
            decimalPlaces={formData.decimalPlaces}
            isUnique={formData.isUnique}
            displayFormat={formData.displayFormat}
            onChange={handleAdvancedOptionChange}
          />
        );
      case "DATE":
      case "DATETIME":
      case "TIME":
        return (
          <DateFieldOptions
            displayFormat={formData.displayFormat}
            onChange={handleAdvancedOptionChange}
          />
        );
      case "SELECT":
      case "MULTI_SELECT":
      case "PICKLIST":
        return (
          <SelectFieldOptions
            options={formData.options}
            onChange={handleAdvancedOptionChange}
            isMultiSelect={fieldType === "MULTI_SELECT"}
          />
        );
      default:
        return null;
    }
  };

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1);
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleStepClick = (step: number) => {
    // Only allow clicking on completed steps or the next available step
    if (step <= activeStep + 1) {
      setActiveStep(step);
    }
  };

  return (
    <>
      <div className="pb-24">
        <form
          id="customFieldForm"
          onSubmit={handleSubmit}
          className="space-y-2"
        >
          <Stepper
            steps={steps}
            activeStep={activeStep}
            onStepClick={handleStepClick}
            className="mb-2"
          />

          {/* Step 1: Basic Information */}
          <StepperContent
            step={0}
            activeStep={activeStep}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div className="space-y-4">
                <FormSection title="Field Information">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="label">Field Label *</Label>
                      <Input
                        id="label"
                        name="label"
                        value={formData.label}
                        onChange={handleInputChange}
                        onBlur={generateFieldName}
                        placeholder="Annual Revenue"
                        required
                        aria-required="true"
                      />
                      <p className="text-xs text-muted-foreground">
                        The label shown to users
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="name">Field Name *</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="annual_revenue"
                        required
                        aria-required="true"
                      />
                      <p className="text-xs text-muted-foreground">
                        Internal field name (no spaces)
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="type">Field Type *</Label>
                      <Select
                        value={formData.type}
                        onValueChange={(value) =>
                          handleSelectChange("type", value)
                        }
                      >
                        <SelectTrigger id="type">
                          <SelectValue placeholder="Select field type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={CustomFieldType.TEXT}>
                            Text
                          </SelectItem>
                          <SelectItem value={CustomFieldType.TEXTAREA}>
                            Text Area
                          </SelectItem>
                          <SelectItem value={CustomFieldType.RICH_TEXT}>
                            Rich Text
                          </SelectItem>
                          <SelectItem value={CustomFieldType.NUMBER}>
                            Number
                          </SelectItem>
                          <SelectItem value={CustomFieldType.CURRENCY}>
                            Currency
                          </SelectItem>
                          <SelectItem value={CustomFieldType.PERCENT}>
                            Percent
                          </SelectItem>
                          <SelectItem value={CustomFieldType.DATE}>
                            Date
                          </SelectItem>
                          <SelectItem value={CustomFieldType.DATETIME}>
                            Date/Time
                          </SelectItem>
                          <SelectItem value={CustomFieldType.BOOLEAN}>
                            Boolean
                          </SelectItem>
                          <SelectItem value={CustomFieldType.CHECKBOX}>
                            Checkbox
                          </SelectItem>
                          <SelectItem value={CustomFieldType.SELECT}>
                            Select
                          </SelectItem>
                          <SelectItem value={CustomFieldType.MULTI_SELECT}>
                            Multi-Select
                          </SelectItem>
                          <SelectItem value={CustomFieldType.PICKLIST}>
                            Picklist
                          </SelectItem>
                          <SelectItem value={CustomFieldType.URL}>
                            URL
                          </SelectItem>
                          <SelectItem value={CustomFieldType.EMAIL}>
                            Email
                          </SelectItem>
                          <SelectItem value={CustomFieldType.PHONE}>
                            Phone
                          </SelectItem>
                          <SelectItem value={CustomFieldType.FILE}>
                            File
                          </SelectItem>
                          <SelectItem value={CustomFieldType.IMAGE}>
                            Image
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground">
                        The type of data this field will store
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder="Description of this field"
                        rows={2}
                      />
                      <p className="text-xs text-muted-foreground">
                        Internal description for administrators
                      </p>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="isRequired"
                        checked={formData.isRequired}
                        onCheckedChange={(checked) =>
                          handleCheckboxChange("isRequired", checked === true)
                        }
                      />
                      <Label htmlFor="isRequired">Required Field</Label>
                    </div>
                  </div>
                </FormSection>

                <FormSection title="Display Options">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="placeholder">Placeholder</Label>
                      <Input
                        id="placeholder"
                        name="placeholder"
                        value={formData.placeholder}
                        onChange={handleInputChange}
                        placeholder="Field placeholder text"
                      />
                      <p className="text-xs text-muted-foreground">
                        Text shown when the field is empty
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="defaultValue">Default Value</Label>
                      <Input
                        id="defaultValue"
                        name="defaultValue"
                        value={formData.defaultValue}
                        onChange={handleInputChange}
                        placeholder="Default value"
                      />
                      <p className="text-xs text-muted-foreground">
                        Value pre-filled when creating new records
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="helpText">Help Text</Label>
                      <Input
                        id="helpText"
                        name="helpText"
                        value={formData.helpText}
                        onChange={handleInputChange}
                        placeholder="Help text shown to users"
                      />
                      <p className="text-xs text-muted-foreground">
                        Explanatory text shown below the field
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="groupName">Field Group</Label>
                      <Input
                        id="groupName"
                        name="groupName"
                        value={formData.groupName}
                        onChange={handleInputChange}
                        placeholder="Group name"
                      />
                      <p className="text-xs text-muted-foreground">
                        Group this field with others (optional)
                      </p>
                    </div>
                  </div>
                </FormSection>
              </div>

              <div className="space-y-4">
                <FormSection title="Field Preview">
                  <FieldPreview
                    type={formData.type}
                    label={formData.label || "Field Label"}
                    placeholder={formData.placeholder}
                    helpText={formData.helpText}
                    isRequired={formData.isRequired}
                    options={formData.options}
                    defaultValue={formData.defaultValue}
                  />
                  <p className="text-xs text-muted-foreground mt-2 text-center">
                    This is a preview of how the field will appear to users.
                  </p>
                </FormSection>

                <FormSection title="Default Permissions">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="isVisibleByDefault">
                          Visible by Default
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Can all roles see this field by default?
                        </p>
                      </div>
                      <Checkbox
                        id="isVisibleByDefault"
                        checked={formData.isVisibleByDefault}
                        onCheckedChange={(checked) =>
                          handleCheckboxChange(
                            "isVisibleByDefault",
                            checked === true,
                          )
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="isEditableByDefault">
                          Editable by Default
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Can all roles edit this field by default?
                        </p>
                      </div>
                      <Checkbox
                        id="isEditableByDefault"
                        checked={formData.isEditableByDefault}
                        onCheckedChange={(checked) =>
                          handleCheckboxChange(
                            "isEditableByDefault",
                            checked === true,
                          )
                        }
                      />
                    </div>

                    <div className="p-3 bg-muted rounded-md mt-2">
                      <p className="text-xs text-muted-foreground">
                        You can configure role-specific permissions after
                        creating the field.
                      </p>
                    </div>
                  </div>
                </FormSection>
              </div>
            </div>
          </StepperContent>

          {/* Step 2: Advanced Options */}
          <StepperContent
            step={1}
            activeStep={activeStep}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 gap-2">
              {renderFieldTypeOptions()}

              <FormSection title="Search and Filter Options">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isSearchable">Searchable</Label>
                      <p className="text-xs text-muted-foreground">
                        Include this field in search results
                      </p>
                    </div>
                    <Checkbox
                      id="isSearchable"
                      checked={formData.isSearchable}
                      onCheckedChange={(checked) =>
                        handleCheckboxChange("isSearchable", checked === true)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isSortable">Sortable</Label>
                      <p className="text-xs text-muted-foreground">
                        Allow sorting by this field in list views
                      </p>
                    </div>
                    <Checkbox
                      id="isSortable"
                      checked={formData.isSortable}
                      onCheckedChange={(checked) =>
                        handleCheckboxChange("isSortable", checked === true)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isFilterable">Filterable</Label>
                      <p className="text-xs text-muted-foreground">
                        Allow filtering by this field in list views
                      </p>
                    </div>
                    <Checkbox
                      id="isFilterable"
                      checked={formData.isFilterable}
                      onCheckedChange={(checked) =>
                        handleCheckboxChange("isFilterable", checked === true)
                      }
                    />
                  </div>
                </div>
              </FormSection>
            </div>
          </StepperContent>

          {/* Step 3: Validation Rules */}
          <StepperContent
            step={2}
            activeStep={activeStep}
            className="space-y-4"
          >
            <ValidationRuleBuilder
              validationRule={formData.validationRule}
              validationMessage={formData.validationMessage}
              onChange={handleAdvancedOptionChange}
            />
          </StepperContent>

          {/* Step 4: Dependencies */}
          <StepperContent
            step={3}
            activeStep={activeStep}
            className="space-y-4"
          >
            <FieldDependencyManager
              entityType={entityType}
              dependencies={formData.dependencies}
              onChange={handleDependenciesChange}
              currentFieldId={initialData?.id}
              availableFields={availableFields}
            />
          </StepperContent>
        </form>

        {/* Fixed Footer with Action Buttons - Aligned with main content */}
        <div className="fixed bottom-0 md:left-[var(--sidebar-width,0px)] left-0 right-0 border-t border-border bg-background/95 backdrop-blur-sm shadow-md z-50 transition-[left] duration-200 ease-linear md:peer-data-[collapsible=icon]:left-[var(--sidebar-width-icon,64px)] md:peer-data-[state=collapsed]:left-0">
          <div className="px-4 sm:px-4 py-2 flex items-center justify-between max-w-screen-2xl mx-auto">
            <div className="flex-shrink-0">
              {activeStep > 0 && (
                <Button
                  type="button"
                  variant="ghost"
                  onClick={handleBack}
                  className="flex items-center h-9"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1"
                  >
                    <path d="m15 18-6-6 6-6" />
                  </svg>
                  Previous
                </Button>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="px-4 h-10"
              >
                Cancel
              </Button>
              <Button
                type={activeStep === steps.length - 1 ? "submit" : "button"}
                disabled={isSaving}
                onClick={
                  activeStep === steps.length - 1 ? undefined : handleNext
                }
                form={
                  activeStep === steps.length - 1
                    ? "customFieldForm"
                    : undefined
                }
                className="px-4 h-10 font-medium"
                variant="default"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {initialData ? "Updating..." : "Creating..."}
                  </>
                ) : activeStep === steps.length - 1 ? (
                  <>{initialData ? "Update Field" : "Create Field"}</>
                ) : (
                  <>
                    Next
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="ml-1"
                    >
                      <path d="m9 18 6-6-6-6" />
                    </svg>
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Cancel Confirmation Dialog */}
        <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                Are you sure you want to cancel?
              </AlertDialogTitle>
              <AlertDialogDescription>
                Any unsaved changes will be lost.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Continue Editing</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => router.push(`/customize/${model}/custom-fields`)}
              >
                Discard Changes
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
}
