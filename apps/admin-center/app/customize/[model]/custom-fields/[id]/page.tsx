import { PageClient } from "./page-client";

import { notFound } from "next/navigation";
import {
  getFieldPermissions,
  getCustomFieldsForEntity,
} from "@flinkk/database/field-utils";
import { OrganizationUserRole } from "@flinkk/database/field-utils/types";

import { getRecord } from "@flinkk/database/query-utils";

export default async function CustomFieldDetailPage({
  params,
}: {
  params: { id: string; model: string };
}) {
  const { id, model } = await params;
  const isNewRecord = id === "new";

  // Validate model parameter
  const validModels = [
    "leads",
    "accounts",
    "opportunities",
    "contacts",
    "packages",
    "tasks",
    "tickets",
    "products",
    "pricebooks",
  ];

  if (!validModels.includes(model.toLowerCase())) {
    notFound();
  }

  // Map model name to entity type (singular form)
  const entityTypeMap: Record<string, string> = {
    leads: "Lead",
    accounts: "BusinessAccount",
    opportunities: "Opportunity",
    contacts: "Contact",
    packages: "Package",
    tasks: "Task",
    tickets: "Ticket",
    products: "Product",
    pricebooks: "PriceBook",
  };

  const entityType = entityTypeMap[model.toLowerCase()] || model;

  let initialData = null;

  if (!isNewRecord) {
    try {
      const { response } = await getRecord({ model: "customField", id });
      initialData = response.data;

      if (!initialData) {
        notFound();
      }
    } catch (error) {
      console.error("Error fetching custom field:", error);
      notFound();
    }
  }

  const userRole = OrganizationUserRole.MEMBER;

  // Fetch field permissions for this model and user role
  const fieldPermissions = await getFieldPermissions(entityType, userRole);

  // Fetch available fields for dependencies
  const availableFields = await getCustomFieldsForEntity(entityType, id);

  return (
    <PageClient
      id={id}
      initialData={initialData}
      entityType={entityType}
      model={model}
      fieldPermissions={fieldPermissions}
      availableFields={availableFields}
    />
  );
}
