"use client";

import { Tabs, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@flinkk/components/ui/tabs";
import { useRouter } from "next/navigation";
import { getAttributesByModel } from "../attribute/[attributeType]/_config/entity-attributes";

export const HeaderTab = ({ model, defaultValue }: any) => {
  const router = useRouter();
  // Get all attributes for this model
  const attributes = getAttributesByModel(model);

  // Special handling for products model
  const isProductsModel = model === "products";
  const productTabs = isProductsModel ? [
    { id: "units", label: "Units" },
    { id: "categories", label: "Categories" }
  ] : [];

  // Calculate number of tabs (standard fields + custom fields + attributes + product-specific tabs)
  const tabCount = 2 + attributes.length + productTabs.length;
  // Determine the grid columns class
  const getGridCols = () => {
    switch (tabCount) {
      case 2:
        return "grid-cols-2";
      case 3:
        return "grid-cols-3";
      case 4:
        return "grid-cols-4";
      case 5:
        return "grid-cols-5";
      case 6:
        return "grid-cols-6";
      default:
        return "grid-cols-4"; // fallback
    }
  };
  return (
    <Tabs defaultValue={defaultValue || "fields"} className="w-full">
      <TabsList className={`grid w-full ${getGridCols()}`}>
        <TabsTrigger value="fields">Standard Fields</TabsTrigger>
        <TabsTrigger
          value="custom-fields"
          onClick={() => {
            router.push(`/customize/${model}/custom-fields`);
          }}
        >
          Custom Fields
        </TabsTrigger>
        {/* Dynamically render attribute tabs */}
        {attributes.map((attr) => (
          <TabsTrigger
            key={attr.id}
            value={attr.id}
            onClick={() => {
              router.push(`/customize/${model}/attribute/${attr.id}`);
            }}
          >
            {attr.label}
          </TabsTrigger>
        ))}
        {/* Render product-specific tabs */}
        {productTabs.map((tab) => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            onClick={() => {
              router.push(`/customize/${model}/${tab.id}`);
            }}
          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
};
