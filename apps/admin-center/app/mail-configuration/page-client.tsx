"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { FormElements } from "@flinkk/dynamic-form/form-elements";
import { SectionWrapper } from "@flinkk/dynamic-form/components/section-wrapper";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@flinkk/components/ui/card";
import { Alert, AlertDescription } from "@flinkk/components/ui/alert";
import { Loader2, Mail, Server, Key, AlertCircle, CheckCircle } from "lucide-react";
import { 
  mailConfigurationFormSchema, 
  type MailConfigurationFormValues 
} from "./_lib/validations";
import { 
  createMailConfiguration, 
  updateMailConfiguration, 
  deleteMailConfiguration 
} from "./actions";

interface MailConfigurationPageClientProps {
  initialData: any;
  initialError: string | null;
}

export function MailConfigurationPageClient({ 
  initialData, 
  initialError 
}: MailConfigurationPageClientProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(initialError);

  const isEditing = !!initialData;

  // Form setup with React Hook Form
  const form = useForm<MailConfigurationFormValues>({
    resolver: zodResolver(mailConfigurationFormSchema),
    defaultValues: {
      mailUrl: initialData?.mailUrl || "",
      mailTenantId: initialData?.mailTenantId || "",
      isActive: initialData?.isActive ?? true,
    },
  });

  const onSubmit = async (values: MailConfigurationFormValues) => {
    setIsSaving(true);
    setError(null);

    try {
      let result;
      
      if (isEditing) {
        result = await updateMailConfiguration({
          id: initialData.id,
          ...values,
        });
      } else {
        result = await createMailConfiguration(values);
      }

      if (result.success) {
        toast.success(result.message || `Mail configuration ${isEditing ? 'updated' : 'created'} successfully`);
        // Optionally refresh the page or redirect
        window.location.reload();
      } else {
        setError(result.error || "An error occurred");
        toast.error(result.error || "An error occurred");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData?.id) return;
    
    if (!confirm("Are you sure you want to delete this mail configuration? This action cannot be undone.")) {
      return;
    }

    setIsDeleting(true);
    setError(null);

    try {
      const result = await deleteMailConfiguration(initialData.id);

      if (result.success) {
        toast.success(result.message || "Mail configuration deleted successfully");
        window.location.reload();
      } else {
        setError(result.error || "Failed to delete mail configuration");
        toast.error(result.error || "Failed to delete mail configuration");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">Mail Configuration</h1>
          <p className="text-muted-foreground">
            Configure your organization's mail server settings
          </p>
        </div>
        {isEditing && (
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting || isSaving}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              "Delete Configuration"
            )}
          </Button>
        )}
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Success Status */}
      {initialData && !error && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Mail configuration is active and ready to use.
          </AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Mail Server Configuration */}
          <SectionWrapper
            title="Mail Server Configuration"
            description="Configure your mail server connection settings"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormElements
                control={form.control}
                name="mailUrl"
                label="Mail Server URL"
                type="url"
                placeholder="https://smtp.gmail.com"
                required
                disabled={isSaving}
                helperText="Enter the full URL of your mail server (e.g., https://smtp.gmail.com)"
              />
              <FormElements
                control={form.control}
                name="mailTenantId"
                label="Mail Tenant ID"
                type="text"
                placeholder="your-tenant-id"
                required
                disabled={isSaving}
                helperText="Your mail service tenant or account identifier"
              />
            </div>
          </SectionWrapper>

          {/* Status */}
          <SectionWrapper
            title="Status"
            description="Control the active status of this configuration"
          >
            <div className="grid grid-cols-1 gap-4">
              <FormElements
                control={form.control}
                name="isActive"
                label="Active"
                type="switch"
                disabled={isSaving}
                helperText="Enable or disable this mail configuration"
              />
            </div>
          </SectionWrapper>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditing ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>
                  {isEditing ? "Update Configuration" : "Create Configuration"}
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
