"use server";

import { getServerSession } from "@flinkk/shared-auth/server-session";
import {prisma} from "@flinkk/database/prisma";
import { revalidatePath } from "next/cache";
import {
  mailConfigurationFormSchema,
  updateMailConfigurationSchema,
  type MailConfigurationFormValues,
  type UpdateMailConfigurationValues
} from "./_lib/validations";

/**
 * Get mail configuration for the current tenant
 * @returns Mail configuration data or null if not found
 */
export async function getMailConfiguration() {
  try {
    const session = await getServerSession();

    if (!session?.userId || !session?.tenantId) {
      throw new Error("Authentication required");
    }

    const { tenantId } = session;

    const mailConfiguration = await prisma.mailConfiguration.findUnique({
      where: {
        tenantId,
      },
      select: {
        id: true,
        mailUrl: true,
        mailTenantId: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      success: true,
      data: mailConfiguration,
    };
  } catch (error) {
    console.error("Error fetching mail configuration:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch mail configuration",
    };
  }
}

/**
 * Create a new mail configuration
 * @param data Mail configuration data
 * @returns Success response or error
 */
export async function createMailConfiguration(data: MailConfigurationFormValues) {
  try {
    const session = await getServerSession();

    if (!session?.userId || !session?.tenantId) {
      throw new Error("Authentication required");
    }

    const { tenantId } = session;

    // Validate the input data
    const validatedData = mailConfigurationFormSchema.parse(data);

    // Check if mail configuration already exists for this tenant
    const existingConfig = await prisma.mailConfiguration.findUnique({
      where: { tenantId },
    });

    if (existingConfig) {
      return {
        success: false,
        error: "Mail configuration already exists for this organization. Please update the existing configuration.",
      };
    }

    // Create the mail configuration
    const mailConfiguration = await prisma.mailConfiguration.create({
      data: {
        ...validatedData,
        tenantId,
      },
      select: {
        id: true,
        mailUrl: true,
        mailTenantId: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    revalidatePath("/mail-configuration");

    return {
      success: true,
      data: mailConfiguration,
      message: "Mail configuration created successfully",
    };
  } catch (error) {
    console.error("Error creating mail configuration:", error);
    
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return {
        success: false,
        error: "Mail configuration already exists for this organization",
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create mail configuration",
    };
  }
}

/**
 * Update an existing mail configuration
 * @param data Mail configuration data with ID
 * @returns Success response or error
 */
export async function updateMailConfiguration(data: UpdateMailConfigurationValues) {
  try {
    const session = await getServerSession();

    if (!session?.userId || !session?.tenantId) {
      throw new Error("Authentication required");
    }

    const { tenantId } = session;

    // Validate the input data
    const validatedData = updateMailConfigurationSchema.parse(data);
    const { id, ...updateData } = validatedData;

    // Update the mail configuration
    const mailConfiguration = await prisma.mailConfiguration.update({
      where: {
        id,
        tenantId, // Ensure user can only update their tenant's configuration
      },
      data: updateData,
      select: {
        id: true,
        mailUrl: true,
        mailTenantId: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    revalidatePath("/mail-configuration");

    return {
      success: true,
      data: mailConfiguration,
      message: "Mail configuration updated successfully",
    };
  } catch (error) {
    console.error("Error updating mail configuration:", error);
    
    if (error instanceof Error && error.message.includes("Record to update not found")) {
      return {
        success: false,
        error: "Mail configuration not found",
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update mail configuration",
    };
  }
}

/**
 * Delete mail configuration
 * @param id Configuration ID
 * @returns Success response or error
 */
export async function deleteMailConfiguration(id: string) {
  try {
    const session = await getServerSession();

    if (!session?.userId || !session?.tenantId) {
      throw new Error("Authentication required");
    }

    const { tenantId } = session;

    // Delete the mail configuration
    await prisma.mailConfiguration.delete({
      where: {
        id,
        tenantId, // Ensure user can only delete their tenant's configuration
      },
    });

    revalidatePath("/mail-configuration");

    return {
      success: true,
      message: "Mail configuration deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting mail configuration:", error);
    
    if (error instanceof Error && error.message.includes("Record to delete does not exist")) {
      return {
        success: false,
        error: "Mail configuration not found",
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete mail configuration",
    };
  }
}
