import { Metada<PERSON> } from "next";
import { getMailConfiguration } from "./actions";
import { MailConfigurationPageClient } from "./page-client";

export const metadata: Metadata = {
  title: "Mail Configuration",
  description: "Configure mail server settings for your organization",
};

export default async function MailConfigurationPage() {
  // Fetch existing mail configuration on the server
  const result = await getMailConfiguration();

  return (
    <MailConfigurationPageClient 
      initialData={result.success ? result.data : null}
      initialError={result.success ? null : result.error}
    />
  );
}
