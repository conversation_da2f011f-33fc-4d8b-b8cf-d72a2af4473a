import { z } from "zod";

// URL validation regex - basic URL format validation
const URL_REGEX = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;

// Mail Configuration form validation schema
export const mailConfigurationFormSchema = z.object({
  mailUrl: z
    .string()
    .min(1, "Mail URL is required")
    .max(255, "Mail URL must be less than 255 characters")
    .regex(URL_REGEX, "Please enter a valid URL (e.g., https://smtp.gmail.com)")
    .refine(
      (url) => {
        try {
          const urlObj = new URL(url);
          return urlObj.protocol === "http:" || urlObj.protocol === "https:";
        } catch {
          return false;
        }
      },
      "Please enter a valid HTTP or HTTPS URL"
    ),

  mailTenantId: z
    .string()
    .min(1, "Mail Tenant ID is required")
    .max(100, "Mail Tenant ID must be less than 100 characters")
    .trim(),

  isActive: z.boolean().default(true),
});

// Type for the form values
export type MailConfigurationFormValues = z.infer<typeof mailConfigurationFormSchema>;

// Server-side validation schema (includes ID for updates)
export const updateMailConfigurationSchema = mailConfigurationFormSchema.extend({
  id: z.string().min(1, "Configuration ID is required"),
});

export type UpdateMailConfigurationValues = z.infer<typeof updateMailConfigurationSchema>;

// Search params validation for the mail configuration page
export const mailConfigurationSearchParamsSchema = z.object({
  page: z.coerce.number().default(1),
  perPage: z.coerce.number().default(10),
  sort: z.string().optional(),
  isActive: z.string().optional(),
  from: z.string().optional(),
  to: z.string().optional(),
});

export type MailConfigurationSearchParams = z.infer<typeof mailConfigurationSearchParamsSchema>;
