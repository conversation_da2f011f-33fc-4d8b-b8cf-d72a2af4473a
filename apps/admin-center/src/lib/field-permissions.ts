import { prisma } from "@flinkk/database/prisma";

/**
 * Check if a user can view a specific field
 * @param customFieldId The ID of the custom field
 * @param tenantId The organization ID
 * @returns Boolean indicating if the user can view the field
 */
export async function canViewField(
  customFieldId: string,
  tenantId: string,
): Promise<boolean> {
  // Get the custom field
  const customField = await prisma.customField.findUnique({
    where: {
      id: customFieldId,
      tenantId,
    },
  });

  if (!customField) {
    return false;
  }

  // Check if there's a specific permission for this field
  const permission = await prisma.fieldPermission.findUnique({
    where: {
      customFieldId_tenantId: {
        customFieldId,
        tenantId,
      },
    },
  });

  // If there's a specific permission, use it
  if (permission) {
    return permission.canView;
  }

  // Otherwise, use the default visibility setting
  return customField.isVisibleByDefault;
}

/**
 * Check if a user can edit a specific field
 * @param customFieldId The ID of the custom field
 * @param tenantId The organization ID
 * @returns Boolean indicating if the user can edit the field
 */
export async function canEditField(
  customFieldId: string,
  tenantId: string,
): Promise<boolean> {
  // Get the custom field
  const customField = await prisma.customField.findUnique({
    where: {
      id: customFieldId,
      tenantId,
    },
  });

  if (!customField) {
    return false;
  }

  // Check if there's a specific permission for this field
  const permission = await prisma.fieldPermission.findUnique({
    where: {
      customFieldId_tenantId: {
        customFieldId,
        tenantId,
      },
    },
  });

  // If there's a specific permission, use it
  if (permission) {
    return permission.canEdit;
  }

  // Otherwise, use the default editability setting
  return customField.isEditableByDefault;
}

/**
 * Get all field permissions for a specific entity type
 * @param entityType The entity type (e.g., "Lead", "Contact", etc.)
 * @param tenantId The organization ID
 * @returns Object mapping field IDs to their permissions
 */
export async function getFieldPermissionsForEntityType(
  entityType: string,
  tenantId: string,
): Promise<Record<string, { canView: boolean; canEdit: boolean }>> {
  // Get all custom fields for this entity type
  const customFields = await prisma.customField.findMany({
    where: {
      entityType,
      tenantId,
    },
  });

  // Get all specific permissions for this entity type
  const permissions = await prisma.fieldPermission.findMany({
    where: {
      tenantId,
      customField: {
        entityType,
      },
    },
  });

  // Build the permissions map
  const permissionsMap: Record<string, { canView: boolean; canEdit: boolean }> =
    {};

  // First, set default permissions based on the custom field settings
  for (const field of customFields) {
    permissionsMap[field.id] = {
      canView: field.isVisibleByDefault,
      canEdit: field.isEditableByDefault,
    };
  }

  // Then, override with specific permissions if they exist
  for (const permission of permissions) {
    permissionsMap[permission.customFieldId] = {
      canView: permission.canView,
      canEdit: permission.canEdit,
    };
  }

  return permissionsMap;
}

/**
 * Filter fields based on user permissions
 * @param fields Array of fields with their values
 * @param permissions Permissions map from getFieldPermissionsForEntityType
 * @param viewOnly If true, only filter for viewing (not editing)
 * @returns Filtered fields array
 */
export function filterFieldsByPermissions<
  T extends { id: string; customFieldId: string },
>(
  fields: T[],
  permissions: Record<string, { canView: boolean; canEdit: boolean }>,
  viewOnly = false,
): T[] {
  return fields.filter((field) => {
    const permission = permissions[field.customFieldId];
    if (!permission) return false;

    return viewOnly ? permission.canView : permission.canEdit;
  });
}

/**
 * Apply field permissions to an entity's custom fields
 * @param entity The entity with customFields
 * @param entityType The entity type (e.g., "Lead", "Contact", etc.)
 * @param tenantId The organization ID
 * @param viewOnly If true, only filter for viewing (not editing)
 * @returns The entity with filtered custom fields
 */
export async function applyFieldPermissions<
  T extends { customFields?: any[] },
>(
  entity: T,
  entityType: string,
  tenantId: string,
  viewOnly = true,
): Promise<T> {
  if (!entity.customFields || entity.customFields.length === 0) {
    return entity;
  }

  // Get permissions for this entity type
  const permissions = await getFieldPermissionsForEntityType(
    entityType,
    tenantId,
  );

  // Filter the custom fields
  const filteredCustomFields = filterFieldsByPermissions(
    entity.customFields,
    permissions,
    viewOnly,
  );

  // Return the entity with filtered custom fields
  return {
    ...entity,
    customFields: filteredCustomFields,
  };
}

/**
 * Check if a user can view a specific default field
 * @param entityType The entity type (e.g., "Lead", "Contact", etc.)
 * @param fieldName The field name
 * @param tenantId The organization ID
 * @returns Boolean indicating if the user can view the field
 */
export async function canViewDefaultField(
  entityType: string,
  fieldName: string,
  tenantId: string,
): Promise<boolean> {
  // Always make certain fields visible regardless of permissions
  if (
    ["id", "createdAt", "updatedAt", "isActive", "isDeleted"].includes(
      fieldName,
    )
  ) {
    return true;
  }

  // Check if there's a specific permission for this field
  const permission = await prisma.defaultFieldPermission.findUnique({
    where: {
      entityType_fieldName_tenantId: {
        entityType,
        fieldName,
        tenantId,
      },
    },
  });

  // If there's a specific permission, use it
  if (permission) {
    return permission.canView;
  }

  // Always make certain fields visible regardless of permissions
  if (
    ["id", "createdAt", "updatedAt", "isActive", "isDeleted"].includes(
      fieldName,
    )
  ) {
    return true;
  }

  // Default to true if no specific permission exists
  return true;
}

/**
 * Check if a user can edit a specific default field
 * @param entityType The entity type (e.g., "Lead", "Contact", etc.)
 * @param fieldName The field name
 * @param tenantId The organization ID
 * @returns Boolean indicating if the user can edit the field
 */
export async function canEditDefaultField(
  entityType: string,
  fieldName: string,
  tenantId: string,
): Promise<boolean> {
  // System fields are not editable by default
  if (["id", "createdAt", "updatedAt"].includes(fieldName)) {
    return false;
  }

  // Check if there's a specific permission for this field
  const permission = await prisma.defaultFieldPermission.findUnique({
    where: {
      entityType_fieldName_tenantId: {
        entityType,
        fieldName,
        tenantId,
      },
    },
  });

  // If there's a specific permission, use it
  if (permission) {
    return permission.canEdit;
  }

  // Default to true for non-system fields
  return true;
}

/**
 * Get all default field permissions for an entity type
 * @param entityType The entity type (e.g., "Lead", "Contact", etc.)
 * @param tenantId The organization ID
 * @returns Object mapping field names to their permissions
 */
export async function getDefaultFieldPermissions(
  entityType: string,
  tenantId: string,
): Promise<Record<string, { canView: boolean; canEdit: boolean }>> {
  // Get all specific permissions for this entity type
  const permissions = await prisma.defaultFieldPermission.findMany({
    where: {
      entityType,
      tenantId,
    },
  });

  // Build the permissions map
  const permissionsMap: Record<string, { canView: boolean; canEdit: boolean }> =
    {};

  // Set default permissions for common fields
  const commonFields = [
    "id",
    "createdAt",
    "updatedAt",
    "isActive",
    "isDeleted",
  ];
  for (const fieldName of commonFields) {
    permissionsMap[fieldName] = {
      canView: true,
      canEdit: ["id", "createdAt", "updatedAt"].includes(fieldName)
        ? false
        : true,
    };
  }

  // Then, override with specific permissions if they exist
  for (const permission of permissions) {
    permissionsMap[permission.fieldName] = {
      canView: permission.canView,
      canEdit: permission.canEdit,
    };
  }

  return permissionsMap;
}

/**
 * Get all field permissions (both default and custom) for an entity
 * @param entityType The entity type (e.g., "Lead", "Contact", etc.)
 * @param tenantId The organization ID
 * @returns Object with default and custom field permissions
 */
export async function getAllFieldPermissions(
  entityType: string,
  tenantId: string,
): Promise<{
  defaultFields: Record<string, { canView: boolean; canEdit: boolean }>;
  customFields: Record<string, { canView: boolean; canEdit: boolean }>;
}> {
  // Get default field permissions
  const defaultFieldPermissions = await getDefaultFieldPermissions(
    entityType,
    tenantId,
  );

  // Get custom field permissions
  const customFieldPermissions = await getFieldPermissionsForEntityType(
    entityType,
    tenantId,
  );

  return {
    defaultFields: defaultFieldPermissions,
    customFields: customFieldPermissions,
  };
}

/**
 * Apply all field permissions to an entity
 * @param entity The entity object
 * @param entityType The entity type (e.g., "Lead", "Contact", etc.)
 * @param tenantId The organization ID
 * @param viewOnly If true, only filter for viewing (not editing)
 * @returns The entity with filtered fields
 */
export async function applyAllFieldPermissions<
  T extends Record<string, any> & { customFields?: any[] },
>(
  entity: T,
  entityType: string,
  tenantId: string,
  viewOnly = true,
): Promise<Partial<T>> {
  // Get all permissions
  const { defaultFields, customFields } = await getAllFieldPermissions(
    entityType,
    tenantId,
  );

  // Create a filtered entity
  const filteredEntity: Partial<T> = {};

  // Always include the id field regardless of permissions
  if ("id" in entity) {
    filteredEntity["id" as keyof T] = entity["id" as keyof T];
  }

  // Include only fields that the user has permission to view/edit
  for (const fieldName in entity) {
    // Skip the id field as it's already included
    if (fieldName === "id") continue;

    // Skip functions and non-data properties
    if (typeof entity[fieldName] === "function") continue;

    // Handle nested objects like user, tasks, etc.
    if (
      fieldName === "user" ||
      fieldName === "tasks" ||
      fieldName === "activityLogs" ||
      fieldName === "customFields" ||
      fieldName === "campaign"
    ) {
      filteredEntity[fieldName as keyof T] = entity[fieldName as keyof T];
      continue;
    }

    // Check if this is a default field with permissions
    const permission = defaultFields[fieldName];
    if (permission) {
      // Only include if user has permission
      if (viewOnly ? permission.canView : permission.canEdit) {
        filteredEntity[fieldName as keyof T] = entity[fieldName as keyof T];
      }
    } else {
      // For fields without explicit permissions, check if it's a system field
      const isSystemField = ["id", "createdAt", "updatedAt"].includes(
        fieldName,
      );

      // Include system fields for OWNER and ADMIN, or fields that are required for the UI to function
      if (isSystemField) {
        filteredEntity[fieldName as keyof T] = entity[fieldName as keyof T];
      } else if (
        ["createdAt", "updatedAt", "isActive", "isDeleted"].includes(fieldName)
      ) {
        // Always include these fields as they're needed for basic functionality
        filteredEntity[fieldName as keyof T] = entity[fieldName as keyof T];
      }
      // Otherwise, don't include the field
    }
  }

  // Filter custom fields (if they exist)
  if (entity.customFields && Array.isArray(entity.customFields)) {
    filteredEntity.customFields = filterFieldsByPermissions(
      entity.customFields,
      customFields,
      viewOnly,
    );
  }

  return filteredEntity;
}
