"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import { Label } from "@flinkk/components/ui/label";
import { Textarea } from "@flinkk/components/ui/textarea";
import { Switch } from "@flinkk/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@flinkk/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@flinkk/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@flinkk/components/ui/tabs";
import { Badge } from "@flinkk/components/ui/badge";
import { toast } from "sonner";
import { BotIcon, ClockIcon, MessageSquareIcon, SettingsIcon } from "lucide-react";

interface ChatbotConfigProps {
  inboxId: string;
}

interface ChatbotConfig {
  enabled: boolean;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  autoReplyDelay: number;
  businessHours: {
    enabled: boolean;
    timezone: string;
    schedule: {
      [key: string]: { start: string; end: string; enabled: boolean };
    };
  };
  fallbackToHuman: boolean;
  keywords: {
    humanHandoff: string[];
    noReply: string[];
  };
}

export function WhatsAppChatbotConfig({ inboxId }: ChatbotConfigProps) {
  const [config, setConfig] = useState<ChatbotConfig>({
    enabled: false,
    model: "gpt-4o-mini",
    temperature: 0.7,
    maxTokens: 500,
    systemPrompt: "",
    autoReplyDelay: 2,
    businessHours: {
      enabled: false,
      timezone: "UTC",
      schedule: {
        mon: { start: "09:00", end: "17:00", enabled: true },
        tue: { start: "09:00", end: "17:00", enabled: true },
        wed: { start: "09:00", end: "17:00", enabled: true },
        thu: { start: "09:00", end: "17:00", enabled: true },
        fri: { start: "09:00", end: "17:00", enabled: true },
        sat: { start: "09:00", end: "17:00", enabled: false },
        sun: { start: "09:00", end: "17:00", enabled: false },
      },
    },
    fallbackToHuman: true,
    keywords: {
      humanHandoff: ["human", "agent", "representative", "speak to someone"],
      noReply: ["stop", "unsubscribe", "opt out"],
    },
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchConfig();
  }, [inboxId]);

  const fetchConfig = async () => {
    try {
      const response = await fetch(`/api/whatsapp/chatbot-config?inboxId=${inboxId}`);
      if (response.ok) {
        const data = await response.json();
        setConfig(data.chatbotConfig);
      }
    } catch (error) {
      console.error("Error fetching chatbot config:", error);
      toast.error("Failed to load chatbot configuration");
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfig = async () => {
    setIsSaving(true);
    try {
      const response = await fetch("/api/whatsapp/chatbot-config", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          inboxId,
          chatbotConfig: config,
        }),
      });

      if (response.ok) {
        toast.success("Chatbot configuration saved successfully");
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to save configuration");
      }
    } catch (error) {
      console.error("Error saving chatbot config:", error);
      toast.error("Failed to save chatbot configuration");
    } finally {
      setIsSaving(false);
    }
  };

  const updateConfig = (path: string, value: any) => {
    setConfig((prev) => {
      const newConfig = { ...prev };
      const keys = path.split(".");
      let current = newConfig as any;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  const updateKeywords = (type: "humanHandoff" | "noReply", value: string) => {
    const keywords = value.split(",").map((k) => k.trim()).filter((k) => k.length > 0);
    updateConfig(`keywords.${type}`, keywords);
  };

  if (isLoading) {
    return <div className="p-4">Loading chatbot configuration...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BotIcon className="h-5 w-5" />
          AI Chatbot Configuration
        </CardTitle>
        <CardDescription>
          Configure AI-powered automatic responses for your WhatsApp inbox
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="general" className="space-y-4">
          <TabsList>
            <TabsTrigger value="general" className="flex items-center gap-2">
              <SettingsIcon className="h-4 w-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="prompts" className="flex items-center gap-2">
              <MessageSquareIcon className="h-4 w-4" />
              Prompts
            </TabsTrigger>
            <TabsTrigger value="schedule" className="flex items-center gap-2">
              <ClockIcon className="h-4 w-4" />
              Schedule
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            {/* Enable/Disable Chatbot */}
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enabled">Enable AI Chatbot</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically respond to incoming WhatsApp messages
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  id="enabled"
                  checked={config.enabled}
                  onCheckedChange={(value) => updateConfig("enabled", value)}
                />
                <Badge variant={config.enabled ? "default" : "secondary"}>
                  {config.enabled ? "Enabled" : "Disabled"}
                </Badge>
              </div>
            </div>

            {config.enabled && (
              <>
                {/* AI Model Selection */}
                <div className="space-y-2">
                  <Label htmlFor="model">AI Model</Label>
                  <Select
                    value={config.model}
                    onValueChange={(value) => updateConfig("model", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gpt-4o-mini">GPT-4o Mini (Recommended)</SelectItem>
                      <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                      <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Temperature */}
                <div className="space-y-2">
                  <Label htmlFor="temperature">Response Creativity (Temperature)</Label>
                  <Input
                    id="temperature"
                    type="number"
                    min="0"
                    max="2"
                    step="0.1"
                    value={config.temperature}
                    onChange={(e) => updateConfig("temperature", parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    0 = More focused, 2 = More creative
                  </p>
                </div>

                {/* Max Tokens */}
                <div className="space-y-2">
                  <Label htmlFor="maxTokens">Maximum Response Length</Label>
                  <Input
                    id="maxTokens"
                    type="number"
                    min="50"
                    max="1000"
                    value={config.maxTokens}
                    onChange={(e) => updateConfig("maxTokens", parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Number of tokens (roughly 4 characters per token)
                  </p>
                </div>

                {/* Auto Reply Delay */}
                <div className="space-y-2">
                  <Label htmlFor="autoReplyDelay">Response Delay (seconds)</Label>
                  <Input
                    id="autoReplyDelay"
                    type="number"
                    min="0"
                    max="30"
                    value={config.autoReplyDelay}
                    onChange={(e) => updateConfig("autoReplyDelay", parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Delay before sending AI response to appear more natural
                  </p>
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="prompts" className="space-y-4">
            {config.enabled && (
              <>
                {/* System Prompt */}
                <div className="space-y-2">
                  <Label htmlFor="systemPrompt">System Prompt</Label>
                  <Textarea
                    id="systemPrompt"
                    rows={6}
                    value={config.systemPrompt}
                    onChange={(e) => updateConfig("systemPrompt", e.target.value)}
                    placeholder="You are a helpful AI assistant for our company. Be friendly, professional, and concise in your responses..."
                  />
                  <p className="text-xs text-muted-foreground">
                    Instructions for how the AI should behave and respond
                  </p>
                </div>

                {/* Human Handoff Keywords */}
                <div className="space-y-2">
                  <Label htmlFor="humanHandoff">Human Handoff Keywords</Label>
                  <Input
                    id="humanHandoff"
                    value={config.keywords.humanHandoff.join(", ")}
                    onChange={(e) => updateKeywords("humanHandoff", e.target.value)}
                    placeholder="human, agent, representative, speak to someone"
                  />
                  <p className="text-xs text-muted-foreground">
                    Comma-separated keywords that trigger human handoff
                  </p>
                </div>

                {/* No Reply Keywords */}
                <div className="space-y-2">
                  <Label htmlFor="noReply">No Reply Keywords</Label>
                  <Input
                    id="noReply"
                    value={config.keywords.noReply.join(", ")}
                    onChange={(e) => updateKeywords("noReply", e.target.value)}
                    placeholder="stop, unsubscribe, opt out"
                  />
                  <p className="text-xs text-muted-foreground">
                    Comma-separated keywords that prevent AI responses
                  </p>
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            {config.enabled && (
              <>
                {/* Business Hours Toggle */}
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="businessHours">Limit to Business Hours</Label>
                    <p className="text-sm text-muted-foreground">
                      Only respond during configured business hours
                    </p>
                  </div>
                  <Switch
                    id="businessHours"
                    checked={config.businessHours.enabled}
                    onCheckedChange={(value) => updateConfig("businessHours.enabled", value)}
                  />
                </div>

                {config.businessHours.enabled && (
                  <div className="space-y-4 border rounded-lg p-4">
                    <h4 className="font-medium">Business Hours Schedule</h4>
                    {Object.entries(config.businessHours.schedule).map(([day, schedule]) => (
                      <div key={day} className="flex items-center gap-4">
                        <div className="w-12">
                          <Label className="capitalize">{day}</Label>
                        </div>
                        <Switch
                          checked={schedule.enabled}
                          onCheckedChange={(value) =>
                            updateConfig(`businessHours.schedule.${day}.enabled`, value)
                          }
                        />
                        {schedule.enabled && (
                          <>
                            <Input
                              type="time"
                              value={schedule.start}
                              onChange={(e) =>
                                updateConfig(`businessHours.schedule.${day}.start`, e.target.value)
                              }
                              className="w-32"
                            />
                            <span>to</span>
                            <Input
                              type="time"
                              value={schedule.end}
                              onChange={(e) =>
                                updateConfig(`businessHours.schedule.${day}.end`, e.target.value)
                              }
                              className="w-32"
                            />
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end pt-4 border-t">
          <Button onClick={saveConfig} disabled={isSaving}>
            {isSaving ? "Saving..." : "Save Configuration"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
