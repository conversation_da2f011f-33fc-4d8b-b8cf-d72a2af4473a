"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import { Label } from "@flinkk/components/ui/label";
import { Textarea } from "@flinkk/components/ui/textarea";
import { Switch } from "@flinkk/components/ui/switch";
import {
  <PERSON>bs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@flinkk/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import toast from "react-hot-toast";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@flinkk/components/ui/avatar";
import { Checkbox } from "@flinkk/components/ui/checkbox";
import {
  ArrowLeftIcon,
  SaveIcon,
  Trash2Icon,
  UsersIcon,
  SettingsIcon,
  Code2Icon,
  WrenchIcon,
  SearchIcon,
  CopyIcon,
  MailIcon,
  MessageCircleIcon,
} from "lucide-react";
import { WidgetCodeDialog } from "./widget-code-dialog";
import { WhatsAppChatbotConfig } from "./whatsapp-chatbot-config";

interface InboxDetailsProps {
  inboxId: string;
}

export function InboxDetails({ inboxId }: InboxDetailsProps) {
  const router = useRouter();

  const [inbox, setInbox] = useState<any>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showWidgetCode, setShowWidgetCode] = useState(false);
  const [activeTab, setActiveTab] = useState("settings");

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    isActive: true,
    color: "#0070f3",
    settings: {
      // Chat widget settings
      welcomeMessage: "Hello! How can we help you today?",
      widgetPosition: "right",
      widgetStyle: "standard",

      // Email settings
      supportEmail: "",
      imapHost: "",
      imapPort: 993,
      imapUsername: "",
      imapPassword: "",
      autoResponseEnabled: false,
      autoResponseTemplate: "",

      // WhatsApp settings
      whatsappProvider: "whatsapp_cloud",
      phoneNumber: "",
      apiKey: "",
      phoneNumberId: "",
      businessAccountId: "",
      webhookVerifyToken: "",

      // Common settings
      defaultAssigneeId: "",
      requireValidContact: false,
    },
  });

  useEffect(() => {
    const fetchInbox = async () => {
      try {
        const response = await fetch(`/api/inboxes/${inboxId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch inbox");
        }
        const data = await response.json();
        setInbox(data);
        setFormData({
          name: data.name || "",
          description: data.description || "",
          isActive: data.isActive !== false, // Default to true if not specified
          color: data.color || "#0070f3",
          settings: {
            // Default settings
            welcomeMessage: "Hello! How can we help you today?",
            widgetPosition: "right",
            widgetStyle: "standard",
            supportEmail: "",
            imapHost: "",
            imapPort: 993,
            imapUsername: "",
            imapPassword: "",
            smtpHost: "",
            smtpPort: 587,
            smtpUsername: "",
            smtpPassword: "",
            autoResponseEnabled: false,
            autoResponseTemplate: "",
            whatsappProvider: "whatsapp_cloud",
            phoneNumber: "",
            apiKey: "",
            phoneNumberId: "",
            businessAccountId: "",
            webhookVerifyToken: "",
            defaultAssigneeId: "",
            requireValidContact: false,

            // Override with actual settings from data
            ...(data.settings || {}),
          },
        });
      } catch (error) {
        console.error("Error fetching inbox:", error);
        toast.error("Failed to load inbox details. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    const fetchUsers = async () => {
      try {
        const { getUsers } = await import("../../../app/actions/users");
        const data = await getUsers();
        // The Server Action returns { users: [...] }, so we need to extract the users array
        setUsers(data.users || []);
      } catch (error) {
        console.error("Error fetching users:", error);
        // Ensure users is always an array even if the request fails
        setUsers([]);
      }
    };

    const fetchInboxAgents = async () => {
      try {
        const response = await fetch(`/api/inboxes/${inboxId}/agents`);
        if (!response.ok) {
          throw new Error("Failed to fetch inbox agents");
        }
        const data = await response.json();
        setSelectedUsers(data.map((agent: any) => agent.userId));
      } catch (error) {
        console.error("Error fetching inbox agents:", error);
      }
    };

    fetchInbox();
    fetchUsers();
    fetchInboxAgents();
  }, [inboxId, toast]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSettingsChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      settings: { ...prev.settings, [name]: value },
    }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, isActive: checked }));
  };

  const handleSave = async () => {
    if (!formData.name) {
      toast.error("Inbox name is required");
      return;
    }

    setIsSaving(true);

    try {
      const response = await fetch(`/api/inboxes/${inboxId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Failed to update inbox");
      }

      const updatedInbox = await response.json();
      setInbox(updatedInbox);

      toast.success("Inbox updated successfully");
    } catch (error) {
      console.error("Error updating inbox:", error);
      toast.error("Failed to update inbox. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (
      !confirm(
        "Are you sure you want to delete this inbox? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/inboxes/${inboxId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete inbox");
      }
      toast.success("Inbox deleted successfully");
      router.push("/inboxes");
    } catch (error) {
      console.error("Error deleting inbox:", error);
      toast.error("Failed to delete inbox. Please try again.");
    }
  };

  const handleUserToggle = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSaveAgents = async () => {
    setIsSaving(true);

    try {
      const response = await fetch(`/api/inboxes/${inboxId}/agents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          agentIds: selectedUsers,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update agents");
      }
      toast.success("Agents updated successfully");
    } catch (error) {
      console.error("Error updating agents:", error);
      toast.error("Failed to update agents. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const filteredUsers = users.filter((user) => {
    const query = searchQuery.toLowerCase();
    return (
      user.name?.toLowerCase().includes(query) ||
      user.email?.toLowerCase().includes(query)
    );
  });

  const getInitials = (name: string) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  if (isLoading) {
    return (
      <div className="container py-2">
        <div className="text-center py-12">Loading inbox details...</div>
      </div>
    );
  }

  return (
    <div className="container py-2 space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/inboxes")}
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Button>
          <h1 className="text-3xl font-bold">{inbox?.name}</h1>
          {!formData.isActive && (
            <span className="ml-2 px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 rounded">
              Inactive
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {inbox?.type === "CHAT" && (
            <Button
              variant="outline"
              onClick={() => setShowWidgetCode(true)}
              className="flex items-center gap-2"
            >
              <Code2Icon className="h-4 w-4" />
              Widget Code
            </Button>
          )}
          <Button
            variant="destructive"
            onClick={handleDelete}
            className="flex items-center gap-2"
          >
            <Trash2Icon className="h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <Tabs
        defaultValue="settings"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="mb-2">
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <SettingsIcon className="h-4 w-4" />
            Settings
          </TabsTrigger>
          <TabsTrigger
            value="collaborators"
            className="flex items-center gap-2"
          >
            <UsersIcon className="h-4 w-4" />
            Collaborators
          </TabsTrigger>

          {/* Widget Builder - Only for CHAT inboxes */}
          {inbox?.type === "CHAT" && (
            <TabsTrigger value="widget" className="flex items-center gap-2">
              <Code2Icon className="h-4 w-4" />
              Widget Builder
            </TabsTrigger>
          )}

          {/* Configuration - For EMAIL and CHAT inboxes */}
          {(inbox?.type === "EMAIL" || inbox?.type === "CHAT") && (
            <TabsTrigger
              value="configuration"
              className="flex items-center gap-2"
            >
              <WrenchIcon className="h-4 w-4" />
              Configuration
            </TabsTrigger>
          )}

          {/* WhatsApp - Only for WHATSAPP inboxes */}
          {inbox?.type === "WHATSAPP" && (
            <TabsTrigger value="whatsapp" className="flex items-center gap-2">
              <MessageCircleIcon className="h-4 w-4" />
              WhatsApp
            </TabsTrigger>
          )}

          {/* Email Configuration - Only for EMAIL inboxes */}
          {inbox?.type === "EMAIL" && (
            <TabsTrigger value="email" className="flex items-center gap-2">
              <MailIcon className="h-4 w-4" />
              Email
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Inbox Settings</CardTitle>
              <CardDescription>
                Manage the basic settings for your inbox
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="space-y-2">
                <Label htmlFor="name">Inbox Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="e.g., Website Support"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Describe the purpose of this inbox"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="color">Brand Color</Label>
                <div className="flex items-center gap-3">
                  <Input
                    id="color"
                    name="color"
                    type="color"
                    value={formData.color}
                    onChange={handleChange}
                    className="w-12 h-10 p-1"
                  />
                  <div
                    className="w-10 h-10 rounded-md"
                    style={{ backgroundColor: formData.color }}
                  ></div>
                  <span className="text-sm text-muted-foreground">
                    {formData.color}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="isActive">Active</Label>
                  <p className="text-sm text-muted-foreground">
                    Inactive inboxes won't receive new messages
                  </p>
                </div>
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={handleSwitchChange}
                />
              </div>

              <div className="pt-4 flex justify-end">
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  {isSaving ? (
                    "Saving..."
                  ) : (
                    <>
                      <SaveIcon className="h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>

              {/* Inbox Information */}
              <div className="border-t pt-6 mt-6">
                <h3 className="text-lg font-medium mb-4">Inbox Information</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium">Inbox Type</p>
                    <p className="text-muted-foreground">
                      {inbox?.type === "CHAT" && "Website Chat"}
                      {inbox?.type === "EMAIL" && "Email Support"}
                      {inbox?.type === "WHATSAPP" && "WhatsApp Business"}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Status</p>
                    <div className="flex items-center gap-2">
                      <div
                        className={`h-2 w-2 rounded-full ${formData.isActive ? "bg-green-500" : "bg-red-500"}`}
                      ></div>
                      <span className="text-muted-foreground">
                        {formData.isActive ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="font-medium">Created</p>
                    <p className="text-muted-foreground">
                      {inbox?.createdAt
                        ? new Date(inbox.createdAt).toLocaleDateString()
                        : "Unknown"}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Last Updated</p>
                    <p className="text-muted-foreground">
                      {inbox?.updatedAt
                        ? new Date(inbox.updatedAt).toLocaleDateString()
                        : "Unknown"}
                    </p>
                  </div>

                  {/* Type-specific information */}
                  {inbox?.type === "CHAT" && (
                    <>
                      <div>
                        <p className="font-medium">Widget Position</p>
                        <p className="text-muted-foreground capitalize">
                          {formData.settings.widgetPosition || "Right"}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium">Widget Style</p>
                        <p className="text-muted-foreground capitalize">
                          {formData.settings.widgetStyle || "Standard"}
                        </p>
                      </div>
                    </>
                  )}

                  {inbox?.type === "EMAIL" && (
                    <>
                      <div>
                        <p className="font-medium">Support Email</p>
                        <p className="text-muted-foreground">
                          {formData.settings.supportEmail || "Not configured"}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium">Auto-Response</p>
                        <p className="text-muted-foreground">
                          {formData.settings.autoResponseEnabled
                            ? "Enabled"
                            : "Disabled"}
                        </p>
                      </div>
                    </>
                  )}

                  {inbox?.type === "WHATSAPP" && (
                    <>
                      <div>
                        <p className="font-medium">Phone Number</p>
                        <p className="text-muted-foreground">
                          {formData.settings.phoneNumber || "Not configured"}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium">Provider</p>
                        <p className="text-muted-foreground">
                          {formData.settings.whatsappProvider ===
                          "whatsapp_cloud"
                            ? "WhatsApp Cloud API"
                            : "360Dialog"}
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collaborators">
          <Card>
            <CardHeader>
              <CardTitle>Collaborators</CardTitle>
              <CardDescription>
                Manage agents who have access to this inbox
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>

              <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
                {filteredUsers.length === 0 ? (
                  <div className="text-center py-2 text-muted-foreground">
                    No users found matching your search
                  </div>
                ) : (
                  filteredUsers.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleUserToggle(user.id)}
                    >
                      <Checkbox
                        id={`user-${user.id}`}
                        checked={selectedUsers.includes(user.id)}
                        onCheckedChange={() => handleUserToggle(user.id)}
                      />
                      <Avatar>
                        <AvatarImage src={user.image || ""} alt={user.name} />
                        <AvatarFallback>
                          {getInitials(user.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{user.name}</p>
                        <p className="text-sm text-muted-foreground truncate">
                          {user.email}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className="pt-4 flex items-center justify-between">
                <div>
                  <p className="font-medium">Selected Agents</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedUsers.length} agent
                    {selectedUsers.length !== 1 && "s"} selected
                  </p>
                </div>
                <Button onClick={handleSaveAgents} disabled={isSaving}>
                  {isSaving ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="widget">
          <Card>
            <CardHeader>
              <CardTitle>Widget Builder</CardTitle>
              <CardDescription>
                Customize the appearance and behavior of your chat widget
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {inbox?.type === "CHAT" ? (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="welcomeMessage">Welcome Message</Label>
                    <Textarea
                      id="welcomeMessage"
                      name="welcomeMessage"
                      value={formData.settings.welcomeMessage}
                      onChange={handleSettingsChange}
                      placeholder="Hello! How can we help you today?"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="widgetPosition">Widget Position</Label>
                    <div className="flex gap-2">
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="position-right"
                          name="widgetPosition"
                          value="right"
                          checked={formData.settings.widgetPosition === "right"}
                          onChange={handleSettingsChange}
                          className="mr-2"
                        />
                        <Label htmlFor="position-right">Right</Label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="position-left"
                          name="widgetPosition"
                          value="left"
                          checked={formData.settings.widgetPosition === "left"}
                          onChange={handleSettingsChange}
                          className="mr-2"
                        />
                        <Label htmlFor="position-left">Left</Label>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 flex justify-end">
                    <Button onClick={handleSave} disabled={isSaving}>
                      {isSaving ? "Saving..." : "Save Changes"}
                    </Button>
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Widget customization is only available for Website Chat
                  inboxes.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="configuration">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Configuration</CardTitle>
              <CardDescription>
                Configure advanced settings and get integration code for your
                inbox
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {inbox?.type === "CHAT" ? (
                <>
                  <div>
                    <h3 className="text-lg font-medium mb-2">
                      Widget Integration Code
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Copy and paste this code into your website to add the chat
                      widget.
                    </p>

                    <div className="bg-muted p-4 rounded-md relative">
                      <pre className="text-sm overflow-x-auto">
                        <code>{`<!-- FlinkCRM Chat Widget -->
<script
  src="${typeof window !== "undefined" ? window.location.origin : ""}/widget.js"
  data-inbox-id="${inboxId}"
  data-position="${formData.settings.widgetPosition || "right"}"
  data-color="${formData.color || "#0070f3"}"
  data-welcome-heading="Hello there 👋"
  data-welcome-tagline="We're here to help you with your questions."
  data-base-url="${typeof window !== "undefined" ? window.location.origin : ""}"
></script>`}</code>
                      </pre>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="absolute top-2 right-2"
                        onClick={() => {
                          const code = `<!-- FlinkCRM Chat Widget -->
<script
  src="${typeof window !== "undefined" ? window.location.origin : ""}/widget.js"
  data-inbox-id="${inboxId}"
  data-position="${formData.settings.widgetPosition || "right"}"
  data-color="${formData.color || "#0070f3"}"
  data-welcome-heading="Hello there 👋"
  data-welcome-tagline="We're here to help you with your questions."
  data-base-url="${typeof window !== "undefined" ? window.location.origin : ""}"
></script>`;
                          navigator.clipboard.writeText(code);
                          toast.success(
                            "The widget code has been copied to your clipboard."
                          );
                        }}
                      >
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </div>

                    <Button
                      variant="outline"
                      className="mt-2"
                      onClick={() => setShowWidgetCode(true)}
                    >
                      <Code2Icon className="h-4 w-4 mr-2" />
                      View More Integration Options
                    </Button>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-2">
                      Advanced Settings
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Additional configuration options will be available soon.
                    </p>
                  </div>
                </>
              ) : inbox?.type === "EMAIL" ? (
                <>
                  <div>
                    <h3 className="text-lg font-medium mb-2">
                      Email Configuration
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Your email inbox is configured to receive support emails
                      at:
                    </p>

                    <div className="bg-muted p-4 rounded-md">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Support Email</p>
                          <p className="text-sm text-muted-foreground">
                            {formData.settings.supportEmail || "Not configured"}
                          </p>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            if (formData.settings.supportEmail) {
                              navigator.clipboard.writeText(
                                formData.settings.supportEmail
                              );
                              toast.success(
                                "Email address copied to clipboard"
                              );
                            }
                          }}
                        >
                          <CopyIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                      <h4 className="font-medium text-blue-900 mb-2">
                        How it works:
                      </h4>
                      <ul className="text-sm text-blue-800 space-y-1">
                        <li>
                          • Emails sent to your support address are
                          automatically converted to tickets
                        </li>
                        <li>• Email threads are maintained as conversations</li>
                        <li>• Replies from agents are sent back via email</li>
                        <li>
                          • Auto-responses can be configured for immediate
                          acknowledgment
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-2">
                      Email Settings Summary
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">IMAP Server</p>
                        <p className="text-muted-foreground">
                          {formData.settings.imapHost || "Not configured"}:
                          {formData.settings.imapPort || "993"}
                        </p>
                      </div>

                      <div>
                        <p className="font-medium">Auto-Response</p>
                        <p className="text-muted-foreground">
                          {formData.settings.autoResponseEnabled
                            ? "Enabled"
                            : "Disabled"}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium">Valid Contact Required</p>
                        <p className="text-muted-foreground">
                          {formData.settings.requireValidContact ? "Yes" : "No"}
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Advanced configuration is only available for Website Chat and
                  Email inboxes.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="whatsapp">
          <Card>
            <CardHeader>
              <CardTitle>WhatsApp Configuration</CardTitle>
              <CardDescription>
                Manage your WhatsApp Business API settings and webhook
                configuration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {inbox?.type === "WHATSAPP" ? (
                <>
                  {/* Provider Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Provider Settings</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">Provider</p>
                        <p className="text-muted-foreground">
                          {formData.settings.whatsappProvider ===
                          "whatsapp_cloud"
                            ? "WhatsApp Cloud API (Meta)"
                            : "360Dialog"}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium">Phone Number</p>
                        <p className="text-muted-foreground">
                          {formData.settings.phoneNumber || "Not configured"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Webhook Configuration */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">
                      Webhook Configuration
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="webhookUrl">Webhook URL</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="webhookUrl"
                            value={
                              formData.settings.phoneNumber
                                ? `${typeof window !== "undefined" ? window.location.origin : ""}/api/webhooks/whatsapp/${formData.settings.phoneNumber.replace("+", "")}`
                                : "Configure phone number first"
                            }
                            readOnly
                            className="bg-muted"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const webhookUrl = formData.settings.phoneNumber
                                ? `${typeof window !== "undefined" ? window.location.origin : ""}/api/webhooks/whatsapp/${formData.settings.phoneNumber.replace("+", "")}`
                                : "";
                              if (webhookUrl) {
                                navigator.clipboard.writeText(webhookUrl);
                                toast.success(
                                  "Webhook URL copied to clipboard"
                                );
                              }
                            }}
                            disabled={!formData.settings.phoneNumber}
                          >
                            <CopyIcon className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Configure this URL in your WhatsApp provider settings
                        </p>
                      </div>

                      {formData.settings.whatsappProvider ===
                        "whatsapp_cloud" && (
                        <div>
                          <Label htmlFor="verifyToken">
                            Webhook Verify Token
                          </Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="verifyToken"
                              value={
                                formData.settings.webhookVerifyToken ||
                                "Generated automatically"
                              }
                              readOnly
                              className="bg-muted"
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                if (formData.settings.webhookVerifyToken) {
                                  navigator.clipboard.writeText(
                                    formData.settings.webhookVerifyToken
                                  );
                                  toast.success(
                                    "Verify token copied to clipboard"
                                  );
                                }
                              }}
                              disabled={!formData.settings.webhookVerifyToken}
                            >
                              <CopyIcon className="h-4 w-4" />
                            </Button>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            Use this token when configuring the webhook in Meta
                            Business Manager
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Setup Instructions */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Setup Instructions</h3>
                    <div className="bg-muted p-4 rounded-lg">
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        {formData.settings.whatsappProvider ===
                        "whatsapp_cloud" ? (
                          <>
                            <li>
                              Go to Meta Business Manager → WhatsApp →
                              Configuration
                            </li>
                            <li>
                              Add the webhook URL above as your webhook endpoint
                            </li>
                            <li>Use the verify token provided above</li>
                            <li>Subscribe to message events</li>
                            <li>Test the webhook connection</li>
                          </>
                        ) : (
                          <>
                            <li>Go to your 360Dialog dashboard → Webhooks</li>
                            <li>
                              Add the webhook URL above as your webhook endpoint
                            </li>
                            <li>Configure message events</li>
                            <li>Test the webhook connection</li>
                          </>
                        )}
                      </ol>
                    </div>
                  </div>

                  {/* AI Chatbot Configuration */}
                  <div className="space-y-4">
                    <WhatsAppChatbotConfig inboxId={inboxId} />
                  </div>

                  {/* Message Templates */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Message Templates</h3>
                    <div className="bg-muted p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <p className="text-sm font-medium">
                          Template Management
                        </p>
                        <Button variant="outline" size="sm" disabled>
                          Sync Templates
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Message template management will be available in a
                        future update. Templates are automatically synced from
                        your WhatsApp provider.
                      </p>
                    </div>
                  </div>

                  {/* API Configuration Summary */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-2">
                      API Configuration Summary
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">Provider</p>
                        <p className="text-muted-foreground">
                          {formData.settings.whatsappProvider ===
                          "whatsapp_cloud"
                            ? "WhatsApp Cloud API"
                            : "360Dialog"}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium">Phone Number</p>
                        <p className="text-muted-foreground">
                          {formData.settings.phoneNumber || "Not configured"}
                        </p>
                      </div>
                      {formData.settings.whatsappProvider ===
                        "whatsapp_cloud" && (
                        <>
                          <div>
                            <p className="font-medium">Phone Number ID</p>
                            <p className="text-muted-foreground">
                              {formData.settings.phoneNumberId ||
                                "Not configured"}
                            </p>
                          </div>
                          <div>
                            <p className="font-medium">Business Account ID</p>
                            <p className="text-muted-foreground">
                              {formData.settings.businessAccountId ||
                                "Not configured"}
                            </p>
                          </div>
                        </>
                      )}
                      <div>
                        <p className="font-medium">API Key Status</p>
                        <p className="text-muted-foreground">
                          {formData.settings.apiKey
                            ? "Configured"
                            : "Not configured"}
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  WhatsApp configuration is only available for WhatsApp inboxes.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email">
          <Card>
            <CardHeader>
              <CardTitle>Email Configuration</CardTitle>
              <CardDescription>
                Manage your email inbox settings and IMAP/SMTP configuration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {inbox?.type === "EMAIL" ? (
                <>
                  {/* Email Settings */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Email Settings</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">Support Email</p>
                        <p className="text-muted-foreground">
                          {formData.settings.supportEmail || "Not configured"}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium">Auto-Response</p>
                        <p className="text-muted-foreground">
                          {formData.settings.autoResponseEnabled
                            ? "Enabled"
                            : "Disabled"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* IMAP Configuration */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">IMAP Configuration</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">IMAP Server</p>
                        <p className="text-muted-foreground">
                          {formData.settings.imapHost || "Not configured"}:
                          {formData.settings.imapPort || "993"}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium">IMAP Username</p>
                        <p className="text-muted-foreground">
                          {formData.settings.imapUsername || "Not configured"}
                        </p>
                      </div>
                    </div>
                  </div>
                  {/* Auto-Response Template */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">
                      Auto-Response Template
                    </h3>
                    <div className="bg-muted p-4 rounded-lg">
                      <p className="text-sm whitespace-pre-wrap">
                        {formData.settings.autoResponseTemplate ||
                          "No auto-response template configured."}
                      </p>
                    </div>
                  </div>

                  {/* Email Connection Status */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-2">
                      Connection Status
                    </h3>
                    <div className="flex items-center gap-2">
                      <div
                        className={`h-3 w-3 rounded-full ${inbox?.status === "ACTIVE" ? "bg-green-500" : "bg-red-500"}`}
                      ></div>
                      <span className="text-sm">
                        {inbox?.status === "ACTIVE"
                          ? "Connected"
                          : "Disconnected"}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      Last checked:{" "}
                      {inbox?.lastChecked
                        ? new Date(inbox.lastChecked).toLocaleString()
                        : "Never"}
                    </p>
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Email configuration is only available for Email inboxes.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {showWidgetCode && (
        <WidgetCodeDialog
          inbox={inbox}
          onClose={() => setShowWidgetCode(false)}
        />
      )}
    </div>
  );
}
